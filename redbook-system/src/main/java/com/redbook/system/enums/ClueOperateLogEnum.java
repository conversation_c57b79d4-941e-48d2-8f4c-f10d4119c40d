package com.redbook.system.enums;


/**
 * 线索动态日志记录枚举
 * <AUTHOR>
 * @date 2024-03-28
 */
public enum ClueOperateLogEnum {

    IMPORT("批量导入","录入","录入系统"),
    ASSIGN("批量分配、分配","分配","将线索分配给“{0}”"),
    RECYCLE("回收共有池","回收共有池","回收到共有池。"),
    TRANSFER("批量转移","转移","由“{0}”转移至“{1}”。"),
    UPDATE_LABEL("更换质量标签","修改质量标签","修改质量标签为“XXX”"),
    UPDATE_USER_ID("更换会员号","修改会员号","修改会员号为“XXX”"),
    WRITE_FOLLOW("写跟进","跟进",""),
    DO_DEAL("办理成交","成交",""),
    SET_INVALID("标记无效","无效",""),
    REFERRAL_ADD("转介绍-添加","转介绍","添加线索转介绍人为“XXX”"),
    REFERRAL_DELETE("转介绍-删除关联","删除关联","将当前转介绍人“XXX”删除关联"),

    ;

    /**
     * 功能按钮
     */
    private final String function;
    /**
     * 操作
     */
    private final String operate;
    /**
     * 记录内容
     */
    private final String content;

    ClueOperateLogEnum(String function, String operate,String content) {
        this.function = function;
        this.operate = operate;
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public String getFunction() {
        return function;
    }

    public String getOperate() {
        return operate;
    }
}
