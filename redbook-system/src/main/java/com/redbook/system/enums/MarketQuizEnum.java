package com.redbook.system.enums;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-19 11:15
 */
public enum MarketQuizEnum {

    BASE_QUIZ("基础测评","baseQuiz"),
    INTEGRATIVE_QUIZ("综合测评","integrativeQuiz"),
    VOICE_QUIZ("发音测评","voiceQuiz"),
    LETTER_CAPITAL_LOW("字母大小写挑战","letterCapitalLow"),
    LETTER_CHOOSE("字母听选挑战","letterChoose"),
    VOCABULARY_CHALLENGE("词汇量挑战","vocabularyChallenge"),

    ;

    private final String name;
    private final String type;

    public static List<Child> getChildList() {
        List<Child> list = new ArrayList<>();
        Child child1 = new Child();
        child1.setName(MarketQuizEnum.LETTER_CAPITAL_LOW.name);
        child1.setType(MarketQuizEnum.LETTER_CAPITAL_LOW.type);
        Child child2 = new Child();
        child2.setName(MarketQuizEnum.LETTER_CHOOSE.name);
        child2.setType(MarketQuizEnum.LETTER_CHOOSE.type);
        Child child3 = new Child();
        child3.setName(MarketQuizEnum.VOCABULARY_CHALLENGE.name);
        child3.setType(MarketQuizEnum.VOCABULARY_CHALLENGE.type);
        list.add(child1);
        list.add(child2);
        list.add(child3);
        return list;
    }

    public static List<Child> getChildListByType(String type) {
        List<Child> childList = getChildList();
        return childList.stream().filter(child -> !child.getType().equals(type)).collect(Collectors.toList());

    }


    @Data
    public static class Child{
        private String name;
        private String type;
    }



    MarketQuizEnum(String name, String type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }


    public static void main(String[] args) {
        List<Child> childList = getChildList();
        System.out.println(childList);


        List<Child> letterChoose = getChildListByType("letterChoose");
        System.out.println(letterChoose);
    }


}
