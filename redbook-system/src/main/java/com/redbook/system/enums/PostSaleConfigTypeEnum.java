package com.redbook.system.enums;

/**
 * 线索状态枚举
 *
 * <AUTHOR>
 * @date 2024-03-26 15:14
 */
public enum PostSaleConfigTypeEnum {
    SPARE_PART_REMARKS("售后备件备注",true),
    SERVICE_CENTER("售后服务中心",true),
    SERVICE_HOTLINE("服务热线",true),

    ;

    private final String desc;
    private final Boolean isUnique;


    PostSaleConfigTypeEnum(String desc,Boolean isUnique) {
        this.desc = desc;
        this.isUnique = isUnique;
    }

    public String getDesc() {
        return desc;
    }

    public Boolean getUnique() {
        return isUnique;
    }
}
