package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.SchoolRemoteStatus;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-15
 */
public interface SchoolRemoteStatusMapper extends BaseMapper<SchoolRemoteStatus> {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    SchoolRemoteStatus selectSchoolRemoteStatusById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param schoolRemoteStatus 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<SchoolRemoteStatus> selectSchoolRemoteStatusList(SchoolRemoteStatus schoolRemoteStatus);

    /**
     * 新增【请填写功能名称】
     *
     * @param schoolRemoteStatus 【请填写功能名称】
     * @return 结果
     */
    int insertSchoolRemoteStatus(SchoolRemoteStatus schoolRemoteStatus);

    /**
     * 修改【请填写功能名称】
     *
     * @param schoolRemoteStatus 【请填写功能名称】
     * @return 结果
     */
    int updateSchoolRemoteStatus(SchoolRemoteStatus schoolRemoteStatus);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    int deleteSchoolRemoteStatusById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSchoolRemoteStatusByIds(Long[] ids);

    SchoolRemoteStatus selectByAgentId(Long agentId);
}
