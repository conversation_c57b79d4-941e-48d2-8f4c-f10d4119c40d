package com.redbook.system.mapper;

import com.redbook.system.domain.RechargeDiscountConfig;

import java.util.List;
/**
 * 充值优惠配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-12
 */
public interface RechargeDiscountConfigMapper
{
    /**
     * 查询充值优惠配置
     * 
     * @param fundtypeid 充值优惠配置主键
     * @return 充值优惠配置
     */
    List<RechargeDiscountConfig> selectRechargeDiscountConfigByFundtypeid(Long fundtypeid);

    /**
     * 新增充值优惠配置
     * 
     * @param rechargeDiscountConfig 充值优惠配置
     * @return 结果
     */
    int insertRechargeDiscountConfig(RechargeDiscountConfig rechargeDiscountConfig);

    /**
     * 修改充值优惠配置
     * 
     * @param rechargeDiscountConfig 充值优惠配置
     * @return 结果
     */
    int updateRechargeDiscountConfig(RechargeDiscountConfig rechargeDiscountConfig);

    /**
     * 删除充值优惠配置
     * 
     * @param fundtypeid 充值优惠配置主键
     * @return 结果
     */
    int deleteRechargeDiscountConfigByFundtypeid(Long fundtypeid);

    /**
     * 批量删除充值优惠配置
     * 
     * @param fundtypeids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRechargeDiscountConfigByFundtypeids(Long[] fundtypeids);
}
