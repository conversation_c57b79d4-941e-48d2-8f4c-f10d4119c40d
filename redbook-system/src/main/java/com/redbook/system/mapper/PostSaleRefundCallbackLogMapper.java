package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.pay.PostSaleRefundCallbackLog;

import java.util.List;

/**
 * 退款回调日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface PostSaleRefundCallbackLogMapper extends BaseMapper<PostSaleRefundCallbackLog>
{
    /**
     * 查询退款回调日志
     * 
     * @param id 退款回调日志主键
     * @return 退款回调日志
     */
        PostSaleRefundCallbackLog selectPostSaleRefundCallbackLogById(Long id);

    /**
     * 查询退款回调日志列表
     * 
     * @param postSaleRefundCallbackLog 退款回调日志
     * @return 退款回调日志集合
     */
    List<PostSaleRefundCallbackLog> selectPostSaleRefundCallbackLogList(PostSaleRefundCallbackLog postSaleRefundCallbackLog);

    /**
     * 新增退款回调日志
     * 
     * @param postSaleRefundCallbackLog 退款回调日志
     * @return 结果
     */
    int insertPostSaleRefundCallbackLog(PostSaleRefundCallbackLog postSaleRefundCallbackLog);

    /**
     * 修改退款回调日志
     * 
     * @param postSaleRefundCallbackLog 退款回调日志
     * @return 结果
     */
    int updatePostSaleRefundCallbackLog(PostSaleRefundCallbackLog postSaleRefundCallbackLog);

    /**
     * 删除退款回调日志
     * 
     * @param id 退款回调日志主键
     * @return 结果
     */
    int deletePostSaleRefundCallbackLogById(Long id);

    /**
     * 批量删除退款回调日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePostSaleRefundCallbackLogByIds(Long[] ids);
}
