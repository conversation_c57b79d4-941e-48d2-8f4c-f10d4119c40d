package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleServiceCentre;

import java.util.List;
/**
 * 售后服务中心Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface PostSaleServiceCentreMapper extends BaseMapper<PostSaleServiceCentre>
{
    /**
     * 查询售后服务中心
     * 
     * @param id 售后服务中心主键
     * @return 售后服务中心
     */
        PostSaleServiceCentre selectPostSaleServiceCentreById(Long id);

    /**
     * 查询售后服务中心列表
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 售后服务中心集合
     */
    List<PostSaleServiceCentre> selectPostSaleServiceCentreList(PostSaleServiceCentre postSaleServiceCentre);

    /**
     * 新增售后服务中心
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 结果
     */
    int insertPostSaleServiceCentre(PostSaleServiceCentre postSaleServiceCentre);

    /**
     * 修改售后服务中心
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 结果
     */
    int updatePostSaleServiceCentre(PostSaleServiceCentre postSaleServiceCentre);

    /**
     * 删除售后服务中心
     * 
     * @param id 售后服务中心主键
     * @return 结果
     */
    int deletePostSaleServiceCentreById(Long id);

    /**
     * 批量删除售后服务中心
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePostSaleServiceCentreByIds(Long[] ids);
}
