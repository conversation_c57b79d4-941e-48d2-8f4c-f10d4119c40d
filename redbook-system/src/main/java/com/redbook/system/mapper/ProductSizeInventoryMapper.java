package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.ProductSizeInventory;
import com.redbook.system.domain.dto.AddProductSizeInventoryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * 商品尺码库存Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-09
 */
public interface ProductSizeInventoryMapper extends BaseMapper<ProductSizeInventory>
{
    /**
     * 查询商品尺码库存
     * 
     * @param id 商品尺码库存主键
     * @return 商品尺码库存
     */
        ProductSizeInventory selectProductSizeInventoryById(Integer id);

    /**
     * 查询商品尺码库存列表
     * 
     * @param productSizeInventory 商品尺码库存
     * @return 商品尺码库存集合
     */
    List<ProductSizeInventory> selectProductSizeInventoryList(ProductSizeInventory productSizeInventory);

    /**
     * 新增商品尺码库存
     * 
     * @param productSizeInventory 商品尺码库存
     * @return 结果
     */
    int insertProductSizeInventory(ProductSizeInventory productSizeInventory);

    /**
     * 修改商品尺码库存
     * 
     * @param productSizeInventory 商品尺码库存
     * @return 结果
     */
    int updateProductSizeInventory(ProductSizeInventory productSizeInventory);


    /**
     * 删除商品尺码库存
     * 
     * @param id 商品尺码库存主键
     * @return 结果
     */
    int deleteProductSizeInventoryById(Integer id);

    /**
     * 批量删除商品尺码库存
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProductSizeInventoryByIds(Integer[] ids);

    int addInventory(AddProductSizeInventoryDto productSizeInventory);

    void updateProductSizeInventoryCount(@Param("id") Integer id, @Param("orderCount") Integer orderCount);
    void addInventoryByProductId(@Param("productId") Integer productId, @Param("inventory") Integer inventory);
    void updateProductSizeInventoryCountByProductId(@Param("productId") Integer productId, @Param("inventory") Integer inventory);
}
