package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.ExclusiveShopTransactionInfo;

import java.util.List;
/**
 * 专卖店交易记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-21
 */
public interface ExclusiveShopTransactionInfoMapper extends BaseMapper<ExclusiveShopTransactionInfo>
{
    /**
     * 查询专卖店交易记录
     * 
     * @param id 专卖店交易记录主键
     * @return 专卖店交易记录
     */
        ExclusiveShopTransactionInfo selectExclusiveShopTransactionInfoById(Long id);

    /**
     * 查询专卖店交易记录列表
     * 
     * @param exclusiveShopTransactionInfo 专卖店交易记录
     * @return 专卖店交易记录集合
     */
    List<ExclusiveShopTransactionInfo> selectExclusiveShopTransactionInfoList(ExclusiveShopTransactionInfo exclusiveShopTransactionInfo);

    /**
     * 新增专卖店交易记录
     * 
     * @param exclusiveShopTransactionInfo 专卖店交易记录
     * @return 结果
     */
    int insertExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo exclusiveShopTransactionInfo);

    /**
     * 修改专卖店交易记录
     * 
     * @param exclusiveShopTransactionInfo 专卖店交易记录
     * @return 结果
     */
    int updateExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo exclusiveShopTransactionInfo);

    /**
     * 删除专卖店交易记录
     * 
     * @param id 专卖店交易记录主键
     * @return 结果
     */
    int deleteExclusiveShopTransactionInfoById(Long id);

    /**
     * 批量删除专卖店交易记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteExclusiveShopTransactionInfoByIds(Long[] ids);
}
