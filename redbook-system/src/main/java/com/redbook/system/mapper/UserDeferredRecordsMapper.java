package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.UserDeferredRecords;

import java.util.List;
/**
 * 会员手动延期记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-13
 */
public interface UserDeferredRecordsMapper extends BaseMapper<UserDeferredRecords>
{
    /**
     * 查询会员手动延期记录
     * 
     * @param id 会员手动延期记录主键
     * @return 会员手动延期记录
     */
        UserDeferredRecords selectUserDeferredRecordsById(Long id);

    /**
     * 查询会员手动延期记录列表
     * 
     * @param userDeferredRecords 会员手动延期记录
     * @return 会员手动延期记录集合
     */
    List<UserDeferredRecords> selectUserDeferredRecordsList(UserDeferredRecords userDeferredRecords);

    /**
     * 新增会员手动延期记录
     * 
     * @param userDeferredRecords 会员手动延期记录
     * @return 结果
     */
    int insertUserDeferredRecords(UserDeferredRecords userDeferredRecords);

    /**
     * 修改会员手动延期记录
     * 
     * @param userDeferredRecords 会员手动延期记录
     * @return 结果
     */
    int updateUserDeferredRecords(UserDeferredRecords userDeferredRecords);

    /**
     * 删除会员手动延期记录
     * 
     * @param id 会员手动延期记录主键
     * @return 结果
     */
    int deleteUserDeferredRecordsById(Long id);

    /**
     * 批量删除会员手动延期记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteUserDeferredRecordsByIds(Long[] ids);
}
