package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.MarketNormalQuiz;

import java.util.List;
/**
 * 代理商网络营销-基础测评Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-19
 */
public interface MarketNormalQuizMapper extends BaseMapper<MarketNormalQuiz>
{
    /**
     * 查询代理商网络营销-基础测评
     * 
     * @param id 代理商网络营销-基础测评主键
     * @return 代理商网络营销-基础测评
     */
        MarketNormalQuiz selectMarketNormalQuizById(Long id);

    /**
     * 查询代理商网络营销-基础测评列表
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 代理商网络营销-基础测评集合
     */
    List<MarketNormalQuiz> selectMarketNormalQuizList(MarketNormalQuiz marketNormalQuiz);

    /**
     * 新增代理商网络营销-基础测评
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 结果
     */
    int insertMarketNormalQuiz(MarketNormalQuiz marketNormalQuiz);

    /**
     * 修改代理商网络营销-基础测评
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 结果
     */
    int updateMarketNormalQuiz(MarketNormalQuiz marketNormalQuiz);
    int update(MarketNormalQuiz marketNormalQuiz);

    /**
     * 删除代理商网络营销-基础测评
     * 
     * @param id 代理商网络营销-基础测评主键
     * @return 结果
     */
    int deleteMarketNormalQuizById(Long id);

    /**
     * 批量删除代理商网络营销-基础测评
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteMarketNormalQuizByIds(Long[] ids);
}
