package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleGoodsOrder;

import java.util.List;
import java.util.Map;
/**
 * 商品订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleGoodsOrderMapper extends BaseMapper<PostSaleGoodsOrder>
{
    /**
     * 查询商品订单
     * 
     * @param id 商品订单主键
     * @return 商品订单
     */
        PostSaleGoodsOrder selectPostSaleGoodsOrderById(Long id);
        PostSaleGoodsOrder selectPostSaleGoodsOrderByOrderNo(String orderNo);

    /**
     * 查询商品订单列表
     * 
     * @param postSaleGoodsOrder 商品订单
     * @return 商品订单集合
     */
    List<PostSaleGoodsOrder> selectPostSaleGoodsOrderList(PostSaleGoodsOrder postSaleGoodsOrder);

    /**
     * 售后后台查询订单
     * @param postSaleGoodsOrder
     * @return
     */
    List<PostSaleGoodsOrder> goodsOrderListByAdmin(PostSaleGoodsOrder postSaleGoodsOrder);

    Map<String, Object> summaryCount();
    /**
     * 新增商品订单
     * 
     * @param postSaleGoodsOrder 商品订单
     * @return 结果
     */
    int insertPostSaleGoodsOrder(PostSaleGoodsOrder postSaleGoodsOrder);

    /**
     * 修改商品订单
     * 
     * @param postSaleGoodsOrder 商品订单
     * @return 结果
     */
    int updatePostSaleGoodsOrder(PostSaleGoodsOrder postSaleGoodsOrder);
    int updateOrder(PostSaleGoodsOrder postSaleGoodsOrder);

    /**
     * 删除商品订单
     * 
     * @param id 商品订单主键
     * @return 结果
     */
    int deletePostSaleGoodsOrderById(Long id);
    int deleteByOrderNo(String orderNo);

    /**
     * 批量删除商品订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePostSaleGoodsOrderByIds(Long[] ids);
}
