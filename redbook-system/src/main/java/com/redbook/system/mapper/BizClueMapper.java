package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.BizClue;
import com.redbook.system.domain.dto.BizCluePostDTO;
import com.redbook.system.domain.vo.BizClueListVO;
import com.redbook.system.domain.vo.BizClueReferralVO;
import com.redbook.system.domain.vo.BizClueRequestVO;

import java.util.List;

/**
 * 线索Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
public interface BizClueMapper extends BaseMapper<BizClue>
{
    /**
     * 查询线索
     * 
     * @param clueId 线索主键
     * @return 线索
     */
    BizClue selectBizClueByClueId(Long clueId);

    /**
     * 查询线索列表
     * 
     * @param bizClueRequestVO 线索
     * @return 线索集合
     */
    List<BizClueListVO> selectBizClueList(BizClueRequestVO bizClueRequestVO);

    /**
     * 查询线索转介绍列表
     *
     * @param clueId 线索id
     * @return 线索集合
     */
    List<BizClueReferralVO> selectClueListForReferral(Long clueId);


    /**
     * 获取最大id
     * @return
     */
    Long selectMaxId();


    /**
     * 新增线索
     * 
     * @param bizClue 线索
     * @return 结果
     */
    int insertBizClue(BizClue bizClue);

    /**
     * 批量新增线索
     *
     * @param bizClueList 线索
     * @return 结果
     */
    int batchInsertBizClue(List<BizClue> bizClueList);

    /**
     * 修改线索
     * 
     * @param bizClue 线索
     * @return 结果
     */
    int updateBizClue(BizClue bizClue);


    /**
     * 查询符合条件的线索id
     * @param bizCluePostDTO
     * @return
     */
    List<Long> getVerifyPassIds(BizCluePostDTO bizCluePostDTO);

    int batchPost(BizCluePostDTO bizCluePostDTO);

    /**
     * 删除线索
     * 
     * @param clueId 线索主键
     * @return 结果
     */
    int deleteBizClueByClueId(Long clueId);

    /**
     * 批量删除线索
     * 
     * @param clueIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBizClueByClueIds(Long[] clueIds);
}
