package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.BizClueLog;

import java.util.List;
/**
 * 线索动态日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
public interface BizClueLogMapper extends BaseMapper<BizClueLog>
{
    /**
     * 查询线索动态日志
     * 
     * @param clueLogId 线索动态日志主键
     * @return 线索动态日志
     */
        BizClueLog selectBizClueLogByClueLogId(Long clueLogId);

    /**
     * 查询线索动态日志列表
     * 
     * @param bizClueLog 线索动态日志
     * @return 线索动态日志集合
     */
    List<BizClueLog> selectBizClueLogList(BizClueLog bizClueLog);

    /**
     * 新增线索动态日志
     * 
     * @param bizClueLog 线索动态日志
     * @return 结果
     */
    int insertBizClueLog(BizClueLog bizClueLog);

    /**
     * 批量新增线索动态日志
     *
     * @param list 线索动态日志列表
     * @return 结果
     */
    int batchInsertBizClueLog(List<BizClueLog> list);

    /**
     * 修改线索动态日志
     * 
     * @param bizClueLog 线索动态日志
     * @return 结果
     */
    int updateBizClueLog(BizClueLog bizClueLog);

    /**
     * 删除线索动态日志
     * 
     * @param clueLogId 线索动态日志主键
     * @return 结果
     */
    int deleteBizClueLogByClueLogId(Long clueLogId);

    /**
     * 批量删除线索动态日志
     * 
     * @param clueLogIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBizClueLogByClueLogIds(Long[] clueLogIds);
}
