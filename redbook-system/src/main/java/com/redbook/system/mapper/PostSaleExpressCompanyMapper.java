package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleExpressCompany;

import java.util.List;
/**
 * 售后快递公司Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface PostSaleExpressCompanyMapper extends BaseMapper<PostSaleExpressCompany>
{
    /**
     * 查询售后快递公司
     * 
     * @param id 售后快递公司主键
     * @return 售后快递公司
     */
        PostSaleExpressCompany selectPostSaleExpressCompanyById(Long id);

    /**
     * 查询售后快递公司列表
     * 
     * @param postSaleExpressCompany 售后快递公司
     * @return 售后快递公司集合
     */
    List<PostSaleExpressCompany> selectPostSaleExpressCompanyList(PostSaleExpressCompany postSaleExpressCompany);

    /**
     * 新增售后快递公司
     * 
     * @param postSaleExpressCompany 售后快递公司
     * @return 结果
     */
    int insertPostSaleExpressCompany(PostSaleExpressCompany postSaleExpressCompany);

    /**
     * 修改售后快递公司
     * 
     * @param postSaleExpressCompany 售后快递公司
     * @return 结果
     */
    int updatePostSaleExpressCompany(PostSaleExpressCompany postSaleExpressCompany);

    /**
     * 删除售后快递公司
     * 
     * @param id 售后快递公司主键
     * @return 结果
     */
    int deletePostSaleExpressCompanyById(Long id);

    /**
     * 批量删除售后快递公司
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePostSaleExpressCompanyByIds(Long[] ids);
}
