package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.AgentMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代理商管理映射Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface AgentMappingMapper extends BaseMapper<AgentMapping>
{
    /**
     * 查询代理商管理映射
     * 
     * @param id 代理商管理映射主键
     * @return 代理商管理映射
     */
    AgentMapping selectAgentMappingById(Long id);

    /**
     * 查询代理商管理映射列表
     * 
     * @param agentMapping 代理商管理映射
     * @return 代理商管理映射集合
     */
    List<AgentMapping> selectAgentMappingList(AgentMapping agentMapping);

    /**
     * 新增代理商管理映射
     * 
     * @param agentMapping 代理商管理映射
     * @return 结果
     */
    int insertAgentMapping(AgentMapping agentMapping);

    /**
     * 修改代理商管理映射
     * 
     * @param agentMapping 代理商管理映射
     * @return 结果
     */
    int updateAgentMapping(AgentMapping agentMapping);

    /**
     * 删除代理商管理映射
     * 
     * @param id 代理商管理映射主键
     * @return 结果
     */
    int deleteAgentMappingById(Long id);

    /**
     * 批量删除代理商管理映射
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAgentMappingByIds(Long[] ids);

    int deleteByUserId(@Param("userId") Long userId);

    List<Long> getManageByAgentId(@Param("agentId") Long agentId);
    List<Long> getManageIdByAgentIdAndRoleId(@Param("agentId") Long agentId,@Param("roleId") Long roleId);

    List<String> getManagerNameListByAgentId(@Param("agentId") Long agentId);

    void deleteAgentMappingByUserId(@Param("userId") Long userId, @Param("agentId") Long agentId);
    void deleteAgentMappingByRoleId(@Param("roleId") Long roleId, @Param("agentId") Long agentId);

    void deleteAgentMappingByUserIds(Long[] userIds);


    String getKeFuAccountByAgentId(Long agentId);

}
