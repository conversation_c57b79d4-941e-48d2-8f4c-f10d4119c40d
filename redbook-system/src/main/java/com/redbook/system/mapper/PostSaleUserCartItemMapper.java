package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleUserCartItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 购物车具体内容条目Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleUserCartItemMapper extends BaseMapper<PostSaleUserCartItem>
{
    /**
     * 根据购物车id和商品id查询购物车条目
     * @param cartId
     * @param goodId
     * @return
     */
    PostSaleUserCartItem selectPostSaleUserCartItem(@Param("cartId") Integer cartId,@Param("goodId") Integer goodId);

    /**
     * 查询购物车具体内容条目
     * 
     * @param id 购物车具体内容条目主键
     * @return 购物车具体内容条目
     */
    PostSaleUserCartItem selectPostSaleUserCartItemById(Long id);

    /**
     * 查询购物车具体内容条目列表
     * 
     * @param appletUserId
     * @return 购物车具体内容条目集合
     */
    List<PostSaleUserCartItem> selectPostSaleUserCartItemList(Integer appletUserId);

    /**
     * 新增购物车具体内容条目
     * 
     * @param postSaleUserCartItem 购物车具体内容条目
     * @return 结果
     */
    int insertPostSaleUserCartItem(PostSaleUserCartItem postSaleUserCartItem);

    int batchInsertPostSaleUserCartItem(List<PostSaleUserCartItem> postSaleUserCartItemList);

    /**
     * 修改购物车具体内容条目
     * 
     * @param postSaleUserCartItem 购物车具体内容条目
     * @return 结果
     */
    int updatePostSaleUserCartItem(PostSaleUserCartItem postSaleUserCartItem);

    /**
     * 删除购物车具体内容条目
     * 
     * @param id 购物车具体内容条目主键
     * @return 结果
     */
    int deletePostSaleUserCartItemById(Integer id);
    /**
     * 批量删除购物车具体内容条目
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePostSaleUserCartItemByIds(Long[] ids);


    int deletePostSaleUserCartItemByCartId(Integer cartId);
    int deleteByUserId(Integer userId);


}
