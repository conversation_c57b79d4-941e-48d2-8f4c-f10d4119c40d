package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.BizClueReferral;

import java.util.List;
/**
 * 线索转介绍Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
public interface BizClueReferralMapper extends BaseMapper<BizClueReferral>
{
    /**
     * 查询线索转介绍
     * 
     * @param clueReferralId 线索转介绍主键
     * @return 线索转介绍
     */
        BizClueReferral selectBizClueReferralByClueReferralId(Long clueReferralId);

    /**
     * 查询线索转介绍列表
     * 
     * @param bizClueReferral 线索转介绍
     * @return 线索转介绍集合
     */
    List<BizClueReferral> selectBizClueReferralList(BizClueReferral bizClueReferral);

    /**
     * 新增线索转介绍
     * 
     * @param bizClueReferral 线索转介绍
     * @return 结果
     */
    int insertBizClueReferral(BizClueReferral bizClueReferral);

    /**
     * 修改线索转介绍
     * 
     * @param bizClueReferral 线索转介绍
     * @return 结果
     */
    int updateBizClueReferral(BizClueReferral bizClueReferral);

    /**
     * 删除线索转介绍
     * 
     * @param clueReferralId 线索转介绍主键
     * @return 结果
     */
    int deleteBizClueReferralByClueReferralId(Long clueReferralId);

    /**
     * 批量删除线索转介绍
     * 
     * @param clueReferralIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteBizClueReferralByClueReferralIds(Long[] clueReferralIds);
}
