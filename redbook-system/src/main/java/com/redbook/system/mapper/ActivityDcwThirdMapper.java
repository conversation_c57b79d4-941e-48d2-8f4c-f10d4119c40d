package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.ActivityDcwThirdUser;
import com.redbook.system.domain.dto.ActivityDcwThirdIndexListDto;
import com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityDcwThirdMapper extends BaseMapper<ActivityDcwThirdUser> {

    /**
     * 赛段1、2、3、5的数据
     * @param activityDcwThirdIndexListDto
     * @return
     */
    List<ActivityDcwThirdUser> selectGameStage1List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectGameStage2List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectGameStage3List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectGameStage5List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdPersonalRank> selectGameStage1TopNumList(@Param("activityBaseId") Integer activityBaseId, @Param("limitNum") int limitNum);

    List<ActivityDcwThirdPersonalRank> selectGameStage2TopNumList(@Param("activityBaseId") Integer activityBaseId, @Param("limitNum") int limitNum);

    List<ActivityDcwThirdPersonalRank> selectGameStage3TopNumList(@Param("activityBaseId") Integer activityBaseId, @Param("cityId") int cityId, @Param("limitNum") int limitNum);

    /**
     * 赛段四各阶段数据
     * @param activityDcwThirdIndexListDto
     * @return
     */
    List<ActivityDcwThirdUser> selectGameStage4Mode100List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectGameStage4Mode32List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectGameStage4Mode16List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectGameStage4Mode8List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    /**
     * 赛段四各阶段上榜数据
     * @param activityBaseId
     * @param stage
     * @param limitNum
     * @return
     */
    List<ActivityDcwThirdPersonalRank> selectGameStage4Mode100TopList(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage, @Param("limitNum") Integer limitNum);

    List<ActivityDcwThirdPersonalRank> selectGameStage4Mode32TopList(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage, @Param("limitNum") Integer limitNum);

    List<ActivityDcwThirdPersonalRank> selectGameStage4Mode16TopList(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage, @Param("limitNum") Integer limitNum);

    List<ActivityDcwThirdPersonalRank> selectGameStage4Mode8TopList(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage, @Param("limitNum") Integer limitNum);

}
