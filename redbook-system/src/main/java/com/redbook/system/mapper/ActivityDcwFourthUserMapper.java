package com.redbook.system.mapper;


import com.redbook.system.domain.ActivityDcwThirdUser;
import com.redbook.system.domain.dto.ActivityDcwThirdIndexListDto;
import com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ActivityDcwFourthUserMapper {

    /**
     * 赛段1、2、3、5的数据
     * @param activityDcwThirdIndexListDto
     * @return
     */
    List<ActivityDcwThirdUser> selectGameStage1List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectGameStage2List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectJinJiSaiMode100List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectFinalList(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdPersonalRank> selectGameStage1TopNumList(@Param("activityBaseId") Integer activityBaseId, @Param("limitNum") int limitNum);

    List<ActivityDcwThirdPersonalRank> selectGameStage2TopNumList(@Param("activityBaseId") Integer activityBaseId, @Param("limitNum") int limitNum);

    List<ActivityDcwThirdPersonalRank> selectGameStage3TopNumList(@Param("activityBaseId") Integer activityBaseId, @Param("cityId") int cityId, @Param("limitNum") int limitNum);

    /**
     * 赛段四各阶段数据
     * @param activityDcwThirdIndexListDto
     * @return
     */
    List<ActivityDcwThirdUser> selectGameStage4Mode100List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectJinJiSaiMode32List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectJinJiSaiMode16List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    List<ActivityDcwThirdUser> selectJinJiSaiMode8List(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto);

    /**
     * 赛段四各阶段上榜数据
     * @param activityBaseId
     * @param stage
     * @param limitNum
     * @return
     */
    List<ActivityDcwThirdPersonalRank> selectJinJiSaiMode100TopList(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage, @Param("limitNum") Integer limitNum);

    List<ActivityDcwThirdPersonalRank> selectJinJiSaiMode32TopList(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage, @Param("limitNum") Integer limitNum);

    List<ActivityDcwThirdPersonalRank> selectJinJiSaiMode16TopList(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage, @Param("limitNum") Integer limitNum);

    List<ActivityDcwThirdPersonalRank> selectJinJiSaiMode8TopList(@Param("activityBaseId") Integer activityBaseId, @Param("stage") Integer stage, @Param("limitNum") Integer limitNum);

}