package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleGoodsClassify;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleGoodsClassifyMapper extends BaseMapper<PostSaleGoodsClassify>
{
    /**
     * 查询商品类型
     * 
     * @param id 商品类型主键
     * @return 商品类型
     */
        PostSaleGoodsClassify selectPostSaleGoodsClassifyById(Long id);

    /**
     * 查询商品类型列表
     * 
     * @param postSaleGoodsClassify 商品类型
     * @return 商品类型集合
     */
    List<PostSaleGoodsClassify> selectPostSaleGoodsClassifyList(PostSaleGoodsClassify postSaleGoodsClassify);

    /**
     * 新增商品类型
     * 
     * @param postSaleGoodsClassify 商品类型
     * @return 结果
     */
    int insertPostSaleGoodsClassify(PostSaleGoodsClassify postSaleGoodsClassify);

    /**
     * 修改商品类型
     * 
     * @param postSaleGoodsClassify 商品类型
     * @return 结果
     */
    int updatePostSaleGoodsClassify(PostSaleGoodsClassify postSaleGoodsClassify);

    /**
     * 删除商品类型
     *
     * @param id     商品类型主键
     * @param userId
     * @return 结果
     */
    int deletePostSaleGoodsClassifyById(@Param("id") Long id, @Param("userId") String userId);

    /**
     * 批量删除商品类型
     *
     * @param ids    需要删除的数据主键集合
     * @param userId
     * @return 结果
     */
    int deletePostSaleGoodsClassifyByIds(@Param("ids") Long[] ids, @Param("userId") String userId);
}
