package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.ProductAgent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 区域商城商品状态Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface ProductAgentMapper extends BaseMapper<ProductAgent>
{
    /**
     * 查询区域商城商品状态
     * 
     * @param id 区域商城商品状态主键
     * @return 区域商城商品状态
     */
    ProductAgent selectProductAgentById(Integer id);
    ProductAgent selectProductAgent(@Param("agentId") Long agentId,@Param("productId") Integer productId);

    /**
     * 查询区域商城商品状态列表
     * 
     * @param productAgent 区域商城商品状态
     * @return 区域商城商品状态集合
     */
    List<ProductAgent> selectProductAgentList(ProductAgent productAgent);

    /**
     * 新增区域商城商品状态
     * 
     * @param productAgent 区域商城商品状态
     * @return 结果
     */
    int insertProductAgent(ProductAgent productAgent);

    /**
     * 修改区域商城商品状态
     * 
     * @param productAgent 区域商城商品状态
     * @return 结果
     */
    int updateProductAgent(ProductAgent productAgent);

    /**
     * 删除区域商城商品状态
     * 
     * @param id 区域商城商品状态主键
     * @return 结果
     */
    int deleteProductAgentById(Integer id);

    /**
     * 批量删除区域商城商品状态
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProductAgentByIds(Integer[] ids);
}
