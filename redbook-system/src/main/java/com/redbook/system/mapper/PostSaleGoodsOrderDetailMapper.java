package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleGoodsOrderDetail;

import java.util.List;
/**
 * 商品订单详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface PostSaleGoodsOrderDetailMapper extends BaseMapper<PostSaleGoodsOrderDetail>
{
    /**
     * 查询商品订单详情
     * 
     * @param id 商品订单详情主键
     * @return 商品订单详情
     */
        PostSaleGoodsOrderDetail selectPostSaleGoodsOrderDetailById(Long id);

    List<PostSaleGoodsOrderDetail> selectByOrderId(Long orderId);
    /**
     * 查询商品订单详情列表
     * 
     * @param postSaleGoodsOrderDetail 商品订单详情
     * @return 商品订单详情集合
     */
    List<PostSaleGoodsOrderDetail> selectPostSaleGoodsOrderDetailList(PostSaleGoodsOrderDetail postSaleGoodsOrderDetail);

    /**
     * 新增商品订单详情
     * 
     * @param postSaleGoodsOrderDetail 商品订单详情
     * @return 结果
     */
    int insertPostSaleGoodsOrderDetail(PostSaleGoodsOrderDetail postSaleGoodsOrderDetail);

    int batchInsertGoodsOrderDetail(List<PostSaleGoodsOrderDetail> goodsOrderDetailList);

    /**
     * 修改商品订单详情
     * 
     * @param postSaleGoodsOrderDetail 商品订单详情
     * @return 结果
     */
    int updatePostSaleGoodsOrderDetail(PostSaleGoodsOrderDetail postSaleGoodsOrderDetail);

    /**
     * 删除商品订单详情
     * 
     * @param id 商品订单详情主键
     * @return 结果
     */
    int deletePostSaleGoodsOrderDetailById(Long id);

    /**
     * 批量删除商品订单详情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePostSaleGoodsOrderDetailByIds(Long[] ids);
}
