package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.AgentRechargeOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代理商充值Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-23
 */
public interface AgentRechargeOrderMapper extends BaseMapper<AgentRechargeOrder>
{
    /**
     * 查询代理商充值
     * 
     * @param id 代理商充值主键
     * @return 代理商充值
     */
    AgentRechargeOrder selectAgentRechargeOrderById(Long id);

    /**
     * 查询代理商充值列表
     * 
     * @param agentRechargeOrder 代理商充值
     * @return 代理商充值集合
     */
    List<AgentRechargeOrder> selectAgentRechargeOrderList(AgentRechargeOrder agentRechargeOrder);

    /**
     * 新增代理商充值
     * 
     * @param agentRechargeOrder 代理商充值
     * @return 结果
     */
    int insertAgentRechargeOrder(AgentRechargeOrder agentRechargeOrder);

    /**
     * 修改代理商充值
     * 
     * @param agentRechargeOrder 代理商充值
     * @return 结果
     */
    int updateAgentRechargeOrder(AgentRechargeOrder agentRechargeOrder);

    /**
     * 删除代理商充值
     * 
     * @param id 代理商充值主键
     * @return 结果
     */
    int deleteAgentRechargeOrderById(Long id);

    /**
     * 批量删除代理商充值
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAgentRechargeOrderByIds(Long[] ids);

    AgentRechargeOrder selectAgentRechargeOrderByOrderId(@Param("orderId") String tradeNo);
}
