package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.Model;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品型号Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-14
 */
public interface ModelMapper extends BaseMapper<Model>
{
    /**
     * 查询产品型号
     * 
     * @param id 产品型号主键
     * @return 产品型号
     */
    Model selectModelById(Long id);

    /**
     * 查询产品型号列表
     * 
     * @param model 产品型号
     * @return 产品型号集合
     */
    List<Model> selectModelList(Model model);

    /**
     * 新增产品型号
     * 
     * @param model 产品型号
     * @return 结果
     */
    int insertModel(Model model);

    /**
     * 修改产品型号
     * 
     * @param model 产品型号
     * @return 结果
     */
    int updateModel(Model model);

    /**
     * 删除产品型号
     * 
     * @param id 产品型号主键
     * @return 结果
     */
    int deleteModelById(Long id);

    /**
     * 批量删除产品型号
     * 
     * @param ids 需要删除的数据主键集合
     * @param username
     * @return 结果
     */
    int deleteModelByIds(@Param("array") Long[] ids, @Param("username") String username);

    /**
     * 查询不能删除的型号，既产量大于0的型号id
     * @return
     */
    List<Long> selectCanNotDeleteIds();
}
