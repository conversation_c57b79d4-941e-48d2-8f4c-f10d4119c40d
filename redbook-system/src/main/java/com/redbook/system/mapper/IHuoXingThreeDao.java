package com.redbook.system.mapper;

import com.redbook.system.domain.old.HuoXingThreeBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022 -05-29
 */
public interface IHuoXingThreeDao {
    
    /**
     * 添加火星移民第3季记录
     * @param huoXingThreeBean
     * @return
     */
    int addRenewRecord(HuoXingThreeBean huoXingThreeBean);

    /**
     * 获取签约人下火星移民的记录
     * @param contractorId
     * @param memberUserId
     * @return
     */
    List<HuoXingThreeBean> getRenewRecord(@Param("contractorId") Long contractorId,
                                        @Param("memberUserId")String memberUserId);

    Map<String, Object> getQiJiYunAgent(@Param("contractorId")Long contractorId);
    int addQiJiYunAgent(@Param("contractorId")long contractorId,
                        @Param("contractorName")String contractorName,
                        @Param("agentId")long agentId,
                        @Param("areaName")String areaName);
    //开通奇记云代理商
    int openQiJiYun(@Param("contractorId")long contractorId,
                   @Param("agentId")long agentId,
                   @Param("channelAgentId")int channelAgentId,
                   @Param("channelAgentInviteCode")String channelAgentInviteCode);
    //增加火星移民赠送的奇记云账号数量
    int addQiJiYunPresentAccountNum(@Param("contractorId")long contractorId,
                             @Param("addOneMonthNum")int addOneMonthNum,
                             @Param("addThreeMonthNum")int addThreeMonthNum,
                             @Param("addSixMonthNum")int addSixMonthNum,
                             @Param("addTwelveMonthNum")int addTwelveMonthNum);
    //增加已开通奇记云账号数量
    int addQiJiYunOpenedAccountNum(@Param("channelAgentId")int channelAgentId,
                                    @Param("addOneMonthNum")int addOneMonthNum,
                                    @Param("addThreeMonthNum")int addThreeMonthNum,
                                    @Param("addSixMonthNum")int addSixMonthNum,
                                   @Param("addTwelveMonthNum")int addTwelveMonthNum);
    //增加奇记云账号
    int addQiJiYunAccount(@Param("username")String username,
                          @Param("channelAgentId")int channelAgentId,
                          @Param("openDate")String openDate,
                          @Param("timeLen")String timeLen,
                          @Param("seriesId")int seriesId);
    //获取奇记云账号列表
    List<Map<String, Object>> getQiJiYunAccountList(@Param("channelAgentId")int channelAgentId,
                                                    @Param("timeLen")String timeLen,
                                                    @Param("seriesId")int seriesId);
}
