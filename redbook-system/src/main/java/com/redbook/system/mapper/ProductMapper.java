package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.Product;
import org.apache.ibatis.annotations.Param;

import java.util.List;
/**
 * 商品Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-09
 */
public interface ProductMapper extends BaseMapper<Product>
{
    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
        Product selectProductById(Integer id);
        Product selectProductInventoryById(Integer id);

    /**
     * 查询商品列表
     * 
     * @param product 商品
     * @return 商品集合
     */
    List<Product> selectProductList(Product product);

    /**
     * 新增商品
     * 
     * @param product 商品
     * @return 结果
     */
    int insertProduct(Product product);

    /**
     * 修改商品
     * 
     * @param product 商品
     * @return 结果
     */
    int updateProduct(Product product);

    /**
     * 删除商品
     * 
     * @param id 商品主键
     * @return 结果
     */
    int deleteProductById(Integer id);

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProductByIds(Integer[] ids);

    /**
     * 批量上下架和删除
     *
     * @param ids
     * @param status
     * @return
     */
    int changeStatus(@Param("array") Integer[] ids, @Param("status") Integer status);
    int changeShowSystem(@Param("array") Integer[] ids, @Param("showSystem") Integer showSystem);
}
