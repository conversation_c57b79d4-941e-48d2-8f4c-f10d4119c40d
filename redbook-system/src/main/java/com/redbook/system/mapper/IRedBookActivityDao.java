package com.redbook.system.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 活动相关
 * <AUTHOR>
 * @date 2022/12/2 10:15
 */
public interface IRedBookActivityDao {
    String getActivityConfig(@Param("activityContentId") Integer activityContentId,@Param("configKey") String configKey);
    Map<String,Object> getJoinUserInfo(@Param("activityBaseId") Integer activityBaseId,@Param("userId") String userId);

    void insertUser(@Param("activityBaseId") Integer activityBaseId,@Param("userId") String userId,@Param("userName") String userName,@Param("userAddr") String userAddr);

    /**
     * 获取剩余火星纪念币
     * @return
     */
    int getSurplusCommemorateCoin();

    /**
     * 获取邀请人
     * @param userId
     * @return
     */
    String getInviter(@Param("userId") String userId);

    /**
     * 更新邀请用户状态
     * @param userId
     */
    void updateInvitedUserStatus(@Param("userId") String userId);

    /**
     * 增加云火星币
     * @param userId
     */
    void addCoinNum(@Param("userId") String userId,@Param("cloudCoin")Integer cloudCoin,@Param("commemorateCoin")Integer commemorateCoin);

    /**
     * 插入火星币/纪念币记录表
     * @param userId
     * @param change_num
     * @param change_type 变动类型 0：挖币 1：抽奖 2：邀请用户奖励  3：购买会员
     * @param type 类型 0：云火星币 1：纪念币
     */
    void insertCoinChangeRecord(@Param("userId") String userId,@Param("change_num")Integer change_num,@Param("change_type")Integer change_type,@Param("type")Integer type);
}
