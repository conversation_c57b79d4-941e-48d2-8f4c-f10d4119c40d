package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.PostSaleSnRepairHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品维修记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface PostSaleSnRepairHistoryMapper extends BaseMapper<PostSaleSnRepairHistory> {


    /**
     * 新增产品维修记录
     *
     * @param postSaleSnRepairHistory 产品维修记录
     * @return 结果
     */
    int insertPostSaleRepairHistory(PostSaleSnRepairHistory postSaleSnRepairHistory);

    //根据产品sn码查询维修记录
    List<PostSaleSnRepairHistory> selectRecordListBySn(@Param("productSn") String productSn);

    int selectCountBySn(@Param("productSn") String productSn);


    PostSaleSnRepairHistory selectRecordByItemId(@Param("itemId") Integer itemId);
}
