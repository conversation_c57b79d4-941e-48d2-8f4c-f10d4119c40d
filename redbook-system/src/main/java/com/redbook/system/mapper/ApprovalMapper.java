package com.redbook.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.redbook.system.domain.Approval;

import java.util.List;
/**
 * 审批Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-30
 */
public interface ApprovalMapper extends BaseMapper<Approval>
{
    /**
     * 查询审批
     * 
     * @param id 审批主键
     * @return 审批
     */
    Approval selectApprovalById(Long id);

    /**
     * 查询审批列表
     * 
     * @param approval 审批
     * @return 审批集合
     */
    List<Approval> selectApprovalList(Approval approval);

    /**
     * 新增审批
     * 
     * @param approval 审批
     * @return 结果
     */
    int insertApproval(Approval approval);

    /**
     * 修改审批
     * 
     * @param approval 审批
     * @return 结果
     */
    int updateApproval(Approval approval);

    /**
     * 删除审批
     * 
     * @param id 审批主键
     * @return 结果
     */
    int deleteApprovalById(Long id);

    /**
     * 批量删除审批
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteApprovalByIds(Long[] ids);

    Approval selectByapprovalNo(String approvalNo);
}
