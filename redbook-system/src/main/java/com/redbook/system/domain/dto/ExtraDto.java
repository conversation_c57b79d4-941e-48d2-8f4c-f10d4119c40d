package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel(value = "附件",description = "附件拼装的json")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExtraDto {
    @ApiModelProperty(name = "name",value = "文件名")
    private String name;
    @ApiModelProperty(name = "url",value = "地址")
    private String url;
}
