package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
@ApiModel("代金券审批代理商 dto")
@Data
public class VoucherApprovalAgentDto {
    @ApiModelProperty(name = "agentId",value = "代理商id")
    private Long agentId;
    @ApiModelProperty(name ="voucher",value = "代金券金额")
    private BigDecimal voucher;
}
