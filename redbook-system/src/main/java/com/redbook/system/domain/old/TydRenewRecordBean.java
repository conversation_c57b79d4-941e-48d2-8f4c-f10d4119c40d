package com.redbook.system.domain.old;

import java.io.Serializable;

/**
 * tyd_renew_record
 * <AUTHOR>
public class TydRenewRecordBean implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 签约人id
     */
    private Integer contractorId;

    /**
     * 区域
     */
    private String district;

    /**
     * 体验中心id
     */
    private Integer agentId;

    /**
     * 会员user_id
     */
    private String memberUserId;

    /**
     * 首次续费1，非首次0
     */
    private Boolean firstRenew;

    /**
     * 阶段：1一阶，2二阶，3三阶
     */
    private Boolean renewStage;

    /**
     * 续费时长：1 MONTH，3 MONTH，6MONTH，12 MONTH
     */
    private String renewDuration;

    /**
     * 支付金额
     */
    private Integer payMoney;
    /**
     * 活动id
     */
    private Integer activityContentId;
    /**
     * 支付时间
     */
    private String payTime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getContractorId() {
        return contractorId;
    }

    public void setContractorId(Integer contractorId) {
        this.contractorId = contractorId;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public String getMemberUserId() {
        return memberUserId;
    }

    public void setMemberUserId(String memberUserId) {
        this.memberUserId = memberUserId;
    }

    public Boolean getFirstRenew() {
        return firstRenew;
    }

    public void setFirstRenew(Boolean firstRenew) {
        this.firstRenew = firstRenew;
    }

    public Boolean getRenewStage() {
        return renewStage;
    }

    public void setRenewStage(Boolean renewStage) {
        this.renewStage = renewStage;
    }

    public String getRenewDuration() {
        return renewDuration;
    }

    public void setRenewDuration(String renewDuration) {
        this.renewDuration = renewDuration;
    }

    public Integer getPayMoney() {
        return payMoney;
    }

    public void setPayMoney(Integer payMoney) {
        this.payMoney = payMoney;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public Integer getActivityContentId() {
        return activityContentId;
    }

    public void setActivityContentId(Integer activityContentId) {
        this.activityContentId = activityContentId;
    }
}