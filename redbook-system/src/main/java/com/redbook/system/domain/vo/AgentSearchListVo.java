package com.redbook.system.domain.vo;

import com.redbook.common.core.domain.entity.AgentInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@ApiModel(reference = "代理商查询响应信息", description = "AgentSearchListVo")
@Data
@Builder
public class AgentSearchListVo {
    @ApiModelProperty(value = "代理商列表")
    private List<AgentInfo> agentInfoList;
}
