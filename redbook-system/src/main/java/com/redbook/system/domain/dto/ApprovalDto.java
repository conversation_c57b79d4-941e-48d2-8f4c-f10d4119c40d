package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("审批")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalDto {
    @ApiModelProperty(name = "approvalId",value = "审批单id")
    private Long approvalId;
    @ApiModelProperty(name = "approvalIdList",value = "审批单id列表")
    private List<Long> approvalIdList;
    @ApiModelProperty(name = "status",value = "-2:取消   -3:拒绝   1通过")
    private Integer status;
    @ApiModelProperty(name = "msg",value = "审批信息")
    private String msg;

}
