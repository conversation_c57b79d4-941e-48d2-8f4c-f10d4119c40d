package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;


/**
 * 售后故障分类对象 post_sale_fault_classify
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@ApiModel("售后故障分类")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleFaultClassify extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /** 故障名称 */
    @TableField(value = "classify_name")
    @Excel(name = "故障名称")
    @ApiModelProperty(name = "classifyName",value= "故障名称" )
    private String classifyName;


    /** 父级id */
    @TableField(value = "parent_id")
    @Excel(name = "父级id")
    @ApiModelProperty(name = "parentId",value= "父级id" )
    private Integer parentId;
    /** 故障排序 */
    @TableField(value = "level")
    @Excel(name = "故障级别")
    @ApiModelProperty(name = "level",value= "故障级别" )
    private Long level;

    /** 故障排序 */
    @TableField(value = "sort_order")
    @Excel(name = "故障排序")
    @ApiModelProperty(name = "sortOrder",value= "故障排序" )
    private Long sortOrder;


    /** 故障描述 */
    @TableField(value = "fault_desc")
    @Excel(name = "故障描述")
    @ApiModelProperty(name = "faultDesc",value= "故障描述" )
    private String faultDesc;


    /** 故障统计数量 */
    @TableField(value = "fault_num")
    @Excel(name = "故障统计数量")
    @ApiModelProperty(name = "faultNum",value= "故障统计数量" )
    private Long faultNum;

    /** 删除状态，这里假删 */
    @TableField(value = "is_delete")
    @Excel(name = "删除状态，这里假删")
    @ApiModelProperty(name = "isDelete",value= "删除状态，这里假删" )
    private Long isDelete;


    /** 删除状态，这里假删 */
    @TableField(exist = false)
    @ApiModelProperty(name = "childs",value= "子类" )
    private List<PostSaleFaultClassify> childs;

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId()
    {
        return id;
    }
    public void setClassifyName(String classifyName) 
    {
        this.classifyName = classifyName;
    }

    public String getClassifyName() 
    {
        return classifyName;
    }
    public void setParentId(Integer parentId)
    {
        this.parentId = parentId;
    }

    public Integer getParentId()
    {
        return parentId;
    }
    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }
    public void setFaultDesc(String faultDesc) 
    {
        this.faultDesc = faultDesc;
    }

    public String getFaultDesc() 
    {
        return faultDesc;
    }
    public void setFaultNum(Long faultNum) 
    {
        this.faultNum = faultNum;
    }

    public Long getFaultNum() 
    {
        return faultNum;
    }

    public void setIsDelete(Long isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Long getIsDelete() 
    {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("classifyName", getClassifyName())
            .append("parentId", getParentId())
            .append("sortOrder", getSortOrder())
            .append("faultDesc", getFaultDesc())
            .append("faultNum", getFaultNum())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("isDelete", getIsDelete())
            .toString();
    }
}
