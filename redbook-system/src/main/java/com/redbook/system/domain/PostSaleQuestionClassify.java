package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 售后问题分类对象 post_sale_question_classify
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@ApiModel("售后问题分类")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleQuestionClassify extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 问题类型名称 */
    @TableField(value = "classify_name")
    @Excel(name = "问题类型名称")
    @ApiModelProperty(name = "classifyName",value= "问题类型名称" )
    private String classifyName;


    /** 删除状态，这里假删 */
    @TableField(value = "is_delete")
    @Excel(name = "删除状态，这里假删")
    @ApiModelProperty(name = "isDelete",value= "删除状态，这里假删" )
    private Long isDelete;


    /** 文章数量 */
    @TableField(value = "article_num")
    @Excel(name = "文章数量")
    @ApiModelProperty(name = "articleNum",value= "文章数量" )
    private Long articleNum;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setClassifyName(String classifyName) 
    {
        this.classifyName = classifyName;
    }

    public String getClassifyName() 
    {
        return classifyName;
    }

    public void setIsDelete(Long isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Long getIsDelete() 
    {
        return isDelete;
    }
    public void setArticleNum(Long articleNum) 
    {
        this.articleNum = articleNum;
    }

    public Long getArticleNum() 
    {
        return articleNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("classifyName", getClassifyName())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("isDelete", getIsDelete())
            .append("articleNum", getArticleNum())
            .toString();
    }
}
