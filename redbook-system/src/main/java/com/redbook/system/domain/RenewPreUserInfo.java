package com.redbook.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@ApiModel("续费之前的用户信息")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RenewPreUserInfo {
    @Excel(name = "用户id")
    @ApiModelProperty(name = "userId", value = "用户id")
    private String userId;

    /**
     * 到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "expirationDate", value = "到期日期")
    private Date expirationDate;


    /**
     * 会员类型 -1PC体验会员 0体验会员 1普通会员 2vip会员 3超级会员
     */
    @Excel(name = "会员类型 -1PC体验会员 0体验会员 1普通会员 2vip会员 3超级会员  -10：所有体验会员（-1、0） 10：所有正式会员（1、2、3）")
    @ApiModelProperty(name = "memberType", value = "会员类型 -1PC体验会员 0体验会员 1普通会员 2vip会员 3超级会员  -10：所有体验会员（-1、0） 10：所有正式会员（1、2、3）")
    private Integer memberType;


    /**
     * 小学学段到期日期，如果为空表示没有购买
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "小学学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage1ExpirationDate", value = "小学学段到期日期，如果为空表示没有购买")
    private Date stage1ExpirationDate;


    /**
     * 初中学段到期日期，如果为空表示没有购买
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初中学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage2ExpirationDate", value = "初中学段到期日期，如果为空表示没有购买")
    private Date stage2ExpirationDate;


    /**
     * 高中学段到期日期，如果为空表示没有购买
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "高中学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage3ExpirationDate", value = "高中学段到期日期，如果为空表示没有购买")
    private Date stage3ExpirationDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "大学学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage4ExpirationDate", value = "大学学段到期日期，如果为空表示没有购买")
    private Date stage4ExpirationDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出国学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage5ExpirationDate", value = "出国学段到期日期，如果为空表示没有购买")
    private Date stage5ExpirationDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "小升初学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage11ExpirationDate", value = "小升初学段到期日期，如果为空表示没有购买")
    private Date stage11ExpirationDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "初升高学段到期日期，如果为空表示没有购买", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "stage21ExpirationDate", value = "初升高学段到期日期，如果为空表示没有购买")
    private Date stage21ExpirationDate;

    /**
     * 所属学段（1小学/2初中/3高中）
     */
    @Excel(name = "所属学段", readConverterExp = "1=小学/2初中/3高中/4大学/5出国/11小升初/21初升高")
    @ApiModelProperty(name = "stage", value = "所属学段")
    private Integer stage;


    /**
     * 第一次购买会员日期（成为正式会员时间）
     */
    @Excel(name = "第一次购买会员日期", readConverterExp = "成=为正式会员时间")
    @ApiModelProperty(name = "firstPurchaseDate", value = "第一次购买会员日期")
    private Date firstPurchaseDate;


    /**
     * 最后一次购买会员日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后一次购买会员日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "lastPurchaseDate", value = "最后一次购买会员日期")
    private Date lastPurchaseDate;
}
