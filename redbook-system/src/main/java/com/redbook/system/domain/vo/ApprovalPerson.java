package com.redbook.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@ApiModel(value = "审批人")
@Data
@Builder
public class ApprovalPerson {
    @ApiModelProperty(value = "第一审批人")
    private String firstPersonName;
    @ApiModelProperty(value = "部门负责人")
    private String leaderPersonName;
    @ApiModelProperty(value = "最终审批人")
    private String finalPersonName;
}
