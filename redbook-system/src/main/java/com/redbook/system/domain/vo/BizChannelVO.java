package com.redbook.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 渠道对象 biz_channel
 * 
 * <AUTHOR>
 * @date 2024-03-22
 */
@ApiModel("渠道VO")
@Data
public class BizChannelVO
{
    private static final long serialVersionUID = 1L;

    /** 渠道id */
    @ApiModelProperty(name = "channelId",value= "渠道id" )
    private Long channelId;

    /** 渠道名称 */
    @ApiModelProperty(name = "channelName",value= "渠道名称" )
    private String channelName;

}
