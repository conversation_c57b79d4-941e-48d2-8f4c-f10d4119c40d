package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 账号申请审批对象 account_apply_approval
 * 
 * <AUTHOR>
 * @date 2023-06-13
 */
@ApiModel("账号申请审批")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AccountApplyApproval extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 代理商id */
    @TableField(value = "agent_id")
    @Excel(name = "代理商id")
    @ApiModelProperty(name = "agentId",value= "代理商id" )
    private Long agentId;


    /** 所属学段（1小学/2初中/3高中） */
    @TableField(value = "stage")
    @Excel(name = "所属学段", readConverterExp = "1=小学/2初中/3高中/4 大学/5 出国/11 小升初/21初升高/99少儿")
    @ApiModelProperty(name = "stage",value= "所属学段" )
    private Integer stage;


    /** 账号类型【0体验会员 1普通会员 2vip会员 3超级会员 4:学管账号】 */
    @TableField(value = "account_type")
    @Excel(name = "账号类型【0体验会员 1普通会员 2vip会员 3超级会员 4:学管账号 5:教管账号】")
    @ApiModelProperty(name = "accountType",value= "账号类型【0体验会员 1普通会员 2vip会员 3超级会员 4:学管账号 5:教管账号】" )
    private Integer accountType;


    /** 申请数量 */
    @TableField(value = "apply_count")
    @Excel(name = "申请数量")
    @ApiModelProperty(name = "applyCount",value= "申请数量" )
    private Integer applyCount;


    /** 启用数量 */
    @TableField(value = "enable_count")
    @Excel(name = "启用数量")
    @ApiModelProperty(name = "enableCount",value= "启用数量" )
    private Integer enableCount;


    /** 最晚启用日期 */
    @TableField(value = "latest_activation_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最晚启用日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "latestActivationDate",value= "最晚启用日期" )
    private Date latestActivationDate;


    /** 可用时间 */
    @TableField(value = "available_time_num")
    @Excel(name = "可用时间")
    @ApiModelProperty(name = "availableTimeNum",value= "可用时间" )
    private Long availableTimeNum;


    /** 可用时间类型【1:天 2:月】 */
    @TableField(value = "available_time_type")
    @Excel(name = "可用时间类型【1:天 2:月】")
    @ApiModelProperty(name = "availableTimeType",value= "可用时间类型【1:天 2:月】" )
    private Integer availableTimeType;


    /** 记录编号 */
    @TableField(value = "record_no")
    @Excel(name = "记录编号")
    @ApiModelProperty(name = "recordNo",value= "记录编号" )
    private String recordNo;


    /** 备注 */
    @TableField(value = "remark")
    @Excel(name = "备注")
    @ApiModelProperty(name = "remark",value= "备注" )
    private String remark;

    @TableField(value = "extra")
    @Excel(name = "附加")
    @ApiModelProperty(name = "extra",value= "附加" )
    private String extra;
    /** 审批表id */
    @TableField(value = "approval_id")
    @Excel(name = "审批表id")
    @ApiModelProperty(name = "approvalId",value= "审批表id" )
    private Long approvalId;
    /** 第一审批人 */
    @TableField(value = "approval_id")
    @Excel(name = "第一审批人id")
    @ApiModelProperty(name = "firstApproverId",value= "第一审批人id" )
    private String firstApproverId;
    /** 最终审批人 */
    @TableField(value = "final_approver_id")
    @Excel(name = "最终审批人id")
    @ApiModelProperty(name = "finalApproverId",value= "最终审批人id" )
    private String finalApproverId;

    @TableField(value = "create_by")
    @Excel(name = "创建人id")
    @ApiModelProperty(name = "createById",value = "创建审批人id")
    private String createById;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAgentId(Long agentId) 
    {
        this.agentId = agentId;
    }

    public Long getAgentId() 
    {
        return agentId;
    }

    public void setApplyCount(Integer applyCount)
    {
        this.applyCount = applyCount;
    }

    public Integer getApplyCount()
    {
        return applyCount;
    }
    public void setEnableCount(Integer enableCount)
    {
        this.enableCount = enableCount;
    }

    public Integer getEnableCount()
    {
        return enableCount;
    }
    public void setLatestActivationDate(Date latestActivationDate) 
    {
        this.latestActivationDate = latestActivationDate;
    }

    public Date getLatestActivationDate() 
    {
        return latestActivationDate;
    }
    public void setAvailableTimeNum(Long availableTimeNum) 
    {
        this.availableTimeNum = availableTimeNum;
    }

    public Long getAvailableTimeNum() 
    {
        return availableTimeNum;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public Integer getAvailableTimeType() {
        return availableTimeType;
    }

    public void setAvailableTimeType(Integer availableTimeType) {
        this.availableTimeType = availableTimeType;
    }

    public void setRecordNo(String recordNo)
    {
        this.recordNo = recordNo;
    }

    public String getRecordNo() 
    {
        return recordNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setApprovalId(Long approvalId)
    {
        this.approvalId = approvalId;
    }

    public Long getApprovalId() 
    {
        return approvalId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agentId", getAgentId())
            .append("stage", getStage())
            .append("accountType", getAccountType())
            .append("applyCount", getApplyCount())
            .append("enableCount", getEnableCount())
            .append("latestActivationDate", getLatestActivationDate())
            .append("availableTimeNum", getAvailableTimeNum())
            .append("availableTimeType", getAvailableTimeType())
            .append("recordNo", getRecordNo())
            .append("remark", getRemark())
            .append("approvalId", getApprovalId())
            .append("createTime", getCreateTime())
            .toString();
    }
}
