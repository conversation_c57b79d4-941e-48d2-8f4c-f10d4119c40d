package com.redbook.system.domain.pay;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 支付回调日志对象 post_sale_pay_callback_log
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
@ApiModel("支付回调日志")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSalePayCallbackLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 商户订单号 */
    @TableField(value = "order_no")
    @Excel(name = "商户订单号")
    @ApiModelProperty(name = "orderNo",value= "商户订单号" )
    private String orderNo;


    /** 微信支付单号 */
    @TableField(value = "transaction_id")
    @Excel(name = "微信支付单号")
    @ApiModelProperty(name = "transactionId",value= "微信支付单号" )
    private String transactionId;


    /** 回调原始报文 */
    @TableField(value = "notify_content")
    @Excel(name = "回调原始报文")
    @ApiModelProperty(name = "notifyContent",value= "回调原始报文" )
    private String notifyContent;


    /** 处理状态 0-未处理 1-已处理 2-处理失败 */
    @TableField(value = "status")
    @Excel(name = "处理状态 0-未处理 1-已处理 2-处理失败")
    @ApiModelProperty(name = "status",value= "处理状态 0-未处理 1-已处理 2-处理失败" )
    private Integer status;


    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setTransactionId(String transactionId) 
    {
        this.transactionId = transactionId;
    }

    public String getTransactionId() 
    {
        return transactionId;
    }
    public void setNotifyContent(String notifyContent) 
    {
        this.notifyContent = notifyContent;
    }

    public String getNotifyContent() 
    {
        return notifyContent;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("transactionId", getTransactionId())
            .append("notifyContent", getNotifyContent())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .toString();
    }
}
