package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 代理商操作日志对象 agent_oper_log
 *
 * <AUTHOR>
 * @date 2022-11-16
 */
@ApiModel("代理商操作日志")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AgentOperLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 代理商id
     */
    @Excel(name = "代理商id")
    @ApiModelProperty(name = "agentId", value = "代理商id")
    @TableField(value = "agent_id")
    private Long agentId;

    /**
     * 类型 1服务 2沟通
     */
    @Excel(name = "类型 1服务 2沟通")
    @ApiModelProperty(name = "type", value = "类型 1服务 2沟通")
    @TableField(value = "type")
    private Integer type;

    /**
     * 操作类型
     */
    @Excel(name = "操作类型")
    @ApiModelProperty(name = "modifyType", value = "操作类型")
    @TableField(value = "modify_type")
    private String modifyType;

    /**
     * 内容
     */
    @Excel(name = "内容")
    @ApiModelProperty(name = "content", value = "内容")
    @TableField(value = "content")
    private String content;

    /**
     * 附件json格式
     */
    @Excel(name = "附件json格式")
    @ApiModelProperty(name = "extra", value = "附件json格式")
    @TableField(value = "extra")
    private String extra;

    /**
     * 操作人,联系人
     */
    @Excel(name = "操作人,联系人")
    @ApiModelProperty(name = "linkUser", value = "操作人,联系人")
    @TableField(value = "link_user")
    private String linkUser;

    @TableField(exist = false)
    @ApiModelProperty(name = "userName", value = "操作人,联系人姓名")
    private String userName;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setModifyType(String modifyType) {
        this.modifyType = modifyType;
    }

    public String getModifyType() {
        return modifyType;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getExtra() {
        return extra;
    }

    public void setLinkUser(String linkUser) {
        this.linkUser = linkUser;
    }

    public String getLinkUser() {
        return linkUser;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("agentId", getAgentId())
                .append("type", getType())
                .append("modifyType", getModifyType())
                .append("content", getContent())
                .append("extra", getExtra())
                .append("linkUser", getLinkUser())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
