package com.redbook.system.domain.vo.postSale.sf;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
@ApiModel("公共响应参数")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SfApiResponse {
    @ApiModelProperty(name = "apiErrorMsg",value= "接口调用异常信息" )
    private String apiErrorMsg;
    @ApiModelProperty(name = "apiResponseID",value= "接口响应Id" )
    private String apiResponseID;
    @ApiModelProperty(name = "apiResultCode",value= "接口响应状态，A1000表示接口调用正常" )
    private String apiResultCode;
    @ApiModelProperty(name = "apiResultData",value= "返回的数据" )
    private String apiResultData;

}
