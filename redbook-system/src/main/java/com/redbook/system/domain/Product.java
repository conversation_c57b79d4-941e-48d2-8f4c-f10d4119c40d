package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;


/**
 * 商品对象 product
 *
 * <AUTHOR>
 * @date 2023-05-09
 */
@ApiModel("商品")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class Product extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * 商品ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 类别ID
     */
    @TableField(value = "category_id")
    @Excel(name = "类别ID")
    @ApiModelProperty(name = "categoryId", value = "类别ID")
    private Integer categoryId;

    @TableField(exist = false)
    @ApiModelProperty(name = "categoryName", value = "类别名称")
    private String categoryName;


    /**
     * 商品名称
     */
    @TableField(value = "name")
    @Excel(name = "商品名称")
    @ApiModelProperty(name = "name", value = "商品名称")
    private String name;


    /**
     * 是否预售，0表示否，1表示是
     */
    @TableField(value = "is_presell")
    @Excel(name = "是否预售，0表示否，1表示是")
    @ApiModelProperty(name = "isPresell", value = "是否预售，0表示否，1表示是")
    private Integer isPresell;


    /**
     * 商品图片
     */
    @TableField(value = "image")
    @Excel(name = "商品图片")
    @ApiModelProperty(name = "image", value = "商品图片")
    private String image;

    @ApiModelProperty(name = "status", value = "商品状态 0 下架 1 上架 -1 删除")
    @Excel(name = "商品状态 0 下架 1 上架 -1 删除")
    @TableField(value = "status")
    private Integer status;

    /**
     * 商品描述
     */
    @TableField(value = "description")
    @Excel(name = "商品描述")
    @ApiModelProperty(name = "description", value = "商品描述")
    private String description;


    /**
     * 商品重量
     */
    @TableField(value = "weight")
    @Excel(name = "商品重量")
    @ApiModelProperty(name = "weight", value = "商品重量")
    private BigDecimal weight;


    /**
     * 商品价格
     */
    @TableField(value = "price")
    @Excel(name = "商品价格")
    @ApiModelProperty(name = "price", value = "商品价格")
    private BigDecimal price;


    /**
     * 运费
     */
    @TableField(value = "shipping_fee")
    @Excel(name = "运费")
    @ApiModelProperty(name = "shippingFee", value = "运费")
    private BigDecimal shippingFee;


    /**
     * 运费类型 0 免邮 1 单件计费 2 多件计费
     */
    @TableField(value = "shipping_type")
    @Excel(name = "运费类型 0 免邮 1 单件计费 2 多件计费")
    @ApiModelProperty(name = "shippingType", value = "运费类型 0 免邮 1 单件计费 2 多件计费 3 到付")
    private Integer shippingType;


    /**
     * 满多少件免邮费
     */
    @TableField(value = "free_shipping_count")
    @Excel(name = "满多少件免邮费")
    @ApiModelProperty(name = "freeShippingCount", value = "满多少件免邮费 -1不免")
    private Integer freeShippingCount;

    @ApiModelProperty(name = "hideStock", value = "是否隐藏库存")
    @TableField(value = "hide_stock")
    private Boolean hideStock;

    @ApiModelProperty(name = "limitQuantity", value = "限购数量 null表示不限购")
    @TableField(value = "limit_quantity")
    private Integer limitQuantity;

    @ApiModelProperty(name = "virtualGoods", value = "是否是虚拟商品")
    @TableField(value = "virtual_goods")
    private Boolean virtualGoods;

    @TableField(exist = false)
    @ApiModelProperty(name = "productSizeInventoryList", value = "商品尺码库存")
    private List<ProductSizeInventory> productSizeInventoryList;

    @TableField(exist = false)
    @ApiModelProperty(name = "agentProductInfo", value = "区域商城商品信息")
    private ProductAgent agentProductInfo;

    @ApiModelProperty(name = "showSystem", value = "展示系统：0：运管+小程序 1：仅运管 2：仅小程序")
    @Excel(name = "展示系统：0：运管+小程序 1：仅运管 2：仅小程序")
    @TableField(value = "show_system")
    private Integer showSystem;
    @TableField(exist = false)
    @ApiModelProperty(name = "showSystemType", value = "展示系统：运管:manage 、小程序:postSale")
    private String showSystemType;

    @ApiModelProperty(name = "inventory", value = "库存数量")
    @TableField(exist = false)
    private Integer inventory;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setIsPresell(Integer isPresell) {
        this.isPresell = isPresell;
    }

    public Integer getIsPresell() {
        return isPresell;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getImage() {
        return image;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
    }

    public BigDecimal getShippingFee() {
        return shippingFee;
    }

    public void setShippingType(Integer shippingType) {
        this.shippingType = shippingType;
    }

    public Integer getShippingType() {
        return shippingType;
    }

    public void setFreeShippingCount(Integer freeShippingCount) {
        this.freeShippingCount = freeShippingCount;
    }

    public Integer getFreeShippingCount() {
        return freeShippingCount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<ProductSizeInventory> getProductSizeInventoryList() {
        return productSizeInventoryList;
    }

    public void setProductSizeInventoryList(List<ProductSizeInventory> productSizeInventoryList) {
        this.productSizeInventoryList = productSizeInventoryList;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("categoryId", getCategoryId()).append("name", getName()).append("isPresell", getIsPresell()).append("image", getImage()).append("description", getDescription()).append("weight", getWeight()).append("status", getStatus()).append("price", getPrice()).append("shippingFee", getShippingFee()).append("shippingType", getShippingType()).append("freeShippingCount", getFreeShippingCount()).append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy()).append("updateTime", getUpdateTime()).toString();
    }
}
