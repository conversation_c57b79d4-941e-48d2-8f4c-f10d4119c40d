package com.redbook.system.domain;

import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;


/**
 * 供应商对象 supply
 *
 * <AUTHOR>
 * @date 2022-11-14
 */
@ApiModel("供应商")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class Supply extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    @ApiModelProperty(name = "supplyName", value = "供应商名称")
    @NotBlank(message = "供应商名称不能为空")
    @Size(min = 0, max = 30, message = "供应商名称长度不能超过30个字符")
    private String supplyName;

    /**
     * 联系人名称
     */
    @Excel(name = "联系人名称")
    @ApiModelProperty(name = "contactsName", value = "联系人名称")
    @NotBlank(message = "联系人名称不能为空")
    @Size(min = 0, max = 30, message = "联系人名称长度不能超过30个字符")
    private String contactsName;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    @ApiModelProperty(name = "contactsPhone", value = "联系人电话")
    @NotBlank(message = "联系人电话不能为空")
    @Pattern(regexp = "^[1][3,4,5,7,8][0-9]{9}$", message = "手机号格式不正确")
    private String contactsPhone;

    /**
     * 联系人地址
     */
    @Excel(name = "联系人地址")
    @ApiModelProperty(name = "contactsAddress", value = "联系人地址")
    @NotBlank(message = "联系人地址不能为空")
    @Size(min = 0, max = 100, message = "联系人地址长度不能超过30个字符")
    private String contactsAddress;

    /**
     * 状态（0正常 1删除）
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=删除")
    @ApiModelProperty(name = "status", value = "状态")
    private String status;

    @Excel(name = "产量")
    @ApiModelProperty(name = "number", value = "产量")
    private Integer number;
}
