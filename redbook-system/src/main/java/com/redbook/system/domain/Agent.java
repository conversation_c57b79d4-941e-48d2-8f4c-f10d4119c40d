package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import com.redbook.common.utils.TimeUtils;
import com.redbook.system.domain.vo.MangerVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDate;
import java.time.Period;
import java.util.Date;


/**
 * 代理商对象 agent
 *
 * <AUTHOR>
 * @date 2022-11-10
 */
@ApiModel("代理商")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Agent extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * $column.columnComment
     */
    @TableField(value = "aid")
    private String aid;

    /**
     * 代理商注册code
     */
    @Excel(name = "代理商注册code ")
    @ApiModelProperty(name = "code", value = "代理商注册code")
    @TableField(value = "code")
    private String code;

    /**
     * 区域id
     */
    @Excel(name = "区域id")
    @ApiModelProperty(name = "areaId", value = "区域id")
    @TableField(value = "area_id")
    private Long areaId;

    /**
     * 代理商名称
     */
    @Excel(name = "代理商名称")
    @ApiModelProperty(name = "name", value = "代理商名称")
    @TableField(value = "name")
    private String name;

    @ApiModelProperty(name = "alias", value = "代理商别名")
    @TableField(value = "alias")
    private String alias;

    /**
     * 父级id -1为总部签约
     */
    @Excel(name = "父级id -1为总部签约")
    @ApiModelProperty(name = "parentId", value = "父级id -1为总部签约")
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 合同级别
     */
    @Excel(name = "合同级别")
    @ApiModelProperty(name = "agreementLevel", value = "合同级别 1省 2市 3县 4店")
    @TableField(value = "agreement_level")
    private Long agreementLevel;

    /**
     * 代理商登记 a b c等
     */
    @Excel(name = "代理商登记 a b c等")
    @ApiModelProperty(name = "level", value = "代理商登记 a b c等")
    @TableField(value = "level")
    private String level;

    /**
     * 0正常 1退盟 2删除
     */
    @Excel(name = "0正常 1退盟 2删除")
    @ApiModelProperty(name = "status", value = "0正常 1退盟 2删除")
    @TableField(value = "status")
    private Long status;

    /**
     * 签约类型 公司 个人
     */
    @Excel(name = "签约类型 公司 个人")
    @ApiModelProperty(name = "type", value = "签约类型 公司 个人")
    @TableField(value = "type")
    private String type;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    @ApiModelProperty(name = "companyName", value = "公司名称")
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    @ApiModelProperty(name = "taxCode", value = "公司代码")
    @TableField(value = "tax_code")
    private String taxCode;

    /**
     * 个人签约名称
     */
    @Excel(name = "个人签约名称")
    @ApiModelProperty(name = "personName", value = "个人签约名称")
    @TableField(value = "person_name")
    private String personName;

    /**
     * 个人签约手机号
     */
    @Excel(name = "个人签约手机号")
    @ApiModelProperty(name = "phone", value = "个人签约手机号")
    @TableField(value = "phone")
    private String phone;

    /**
     * 身份证
     */
    @Excel(name = "身份证")
    @ApiModelProperty(name = "idCard", value = "身份证")
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 签约开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签约开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "agreementStart", value = "签约开始时间")
    @TableField(value = "agreement_start")
    private Date agreementStart;

    /**
     * 签约到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签约到期时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "agreementEnd", value = "签约到期时间")
    @TableField(value = "agreement_end")
    private Date agreementEnd;

    /**
     * 账号到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "账号到期时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "accountEnd", value = "账号到期时间")
    @TableField(value = "account_end")
    private Date accountEnd;

    @ApiModelProperty(name = "priceScheme", value = "是否享受价格优惠 0否 1是")
    @TableField(value = "price_scheme")
    private Integer priceScheme;
    @ApiModelProperty(name = "giftCount", value = "赠送硬件个数")
    @TableField(value = "gift_count")
    private Integer giftCount;
    /**
     * $column.columnComment
     */
    @ApiModelProperty(name = "contactPerson", value = "联系人，负责人账号")
    @TableField(value = "contact_person")
    private Long contactPerson;
    @ApiModelProperty(name = "contactPersonBean", value = "负责人")
    @TableField(exist = false)
    private MangerVo contactPersonBean;


    @ApiModelProperty(name = "shopCountLevel1", value = "一级专卖店数量")
    @TableField(value = "shop_count_level1")
    private Integer shopCountLevel1;

    @ApiModelProperty(name = "shopCountLevel2", value = "二级专卖店数量")
    @TableField(value = "shop_count_level2")
    private Integer shopCountLevel2;

    @ApiModelProperty(name = "shopCountLevelTotal", value = "专卖店总数量")
    @TableField(exist = false)
    private Integer shopCountLevelTotal;

    @ApiModelProperty(name = "shopCountLevelTotalAsc", value = "店面总数量排序：asc正序，desc倒序")
    @TableField(exist = false)
    private String shopCountLevelTotalAsc;

//    @ApiModelProperty(name = "parent", value = "上级代理商")
//    @TableField(exist = false)
//    private AgentInfo parent;
    @ApiModelProperty(name = "managerNames", value = "经理姓名列表 用“，”分割")
    @TableField(exist = false)
    private String managerNames;
    @ApiModelProperty(name = "year", value = "经营年限")
    @TableField(exist = false)
    private String year;
    @ApiModelProperty(name = "agentAccount", value = "代理商账户")
    @TableField(exist = false)
    private AgentAccount agentAccount;

    @ApiModelProperty(value = "正式会员数量")
    @TableField(exist = false)
    private Integer formalUserCount;
    @ApiModelProperty(value = "体验会员数量")
    @TableField(exist = false)
    private Integer experienceUserCount;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getAid() {
        return aid;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLevel() {
        return level;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setAgreementStart(Date agreementStart) {
        this.agreementStart = agreementStart;
    }

    public Date getAgreementStart() {
        return agreementStart;
    }

    public void setAgreementEnd(Date agreementEnd) {
        this.agreementEnd = agreementEnd;
    }

    public Date getAgreementEnd() {
        return agreementEnd;
    }

    public void setContactPerson(Long contactPerson) {
        this.contactPerson = contactPerson;
    }

    public Long getContactPerson() {
        return contactPerson;
    }

    public String getYear() {
        if (agreementStart == null || agreementEnd == null) {
            return year;
        }
        if (this.getAgreementEnd().before(new Date())) {
            LocalDate end = TimeUtils.date2LocalDate(agreementEnd);
            LocalDate start = TimeUtils.date2LocalDate(agreementStart);
            return Period.between(start, end).getYears() + "年";
        }
        LocalDate start = TimeUtils.date2LocalDate(agreementStart);
        return Period.between(start, LocalDate.now()).getYears() + "年";
    }

    public void setYear(String year) {
        this.year = year;
    }

    public void setShopCountLevel1(Integer shopCountLevel1) {
        this.shopCountLevel1 = shopCountLevel1;
    }
    public Integer getShopCountLevel1() {
        return this.shopCountLevel1;
    }

    public void setShopCountLevel2(Integer shopCountLevel2) {
        this.shopCountLevel2 = shopCountLevel2;
    }
    public Integer getShopCountLevel2() {
        return this.shopCountLevel2;
    }

    public String getShopCountLevelTotalAsc() {
        return shopCountLevelTotalAsc;
    }

    public void setShopCountLevelTotalAsc(String shopCountLevelTotalAsc) {
        this.shopCountLevelTotalAsc = shopCountLevelTotalAsc;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("aid", getAid())
                .append("code", getCode())
                .append("areaId", getAreaId())
                .append("name", getName())
                .append("parentId", getParentId())
                .append("level", getLevel())
                .append("type", getType())
                .append("companyName", getCompanyName())
                .append("taxCode", getTaxCode())
                .append("personName", getPersonName())
                .append("phone", getPhone())
                .append("idCard", getIdCard())
                .append("agreementStart", getAgreementStart())
                .append("agreementEnd", getAgreementEnd())
                .append("contactPerson", getContactPerson())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }

}
