package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 小红本操作日志对象 redbook_tablet_operate_log
 * 
 * <AUTHOR>
 * @date 2024-03-07
 */
@ApiModel("小红本操作日志")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RedbookTabletOperateLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 设备SN号码 */
    @TableField(value = "SN")
    @Excel(name = "设备SN号码")
    @ApiModelProperty(name = "sn",value= "设备SN号码" )
    private String sn;


    /** 操作类型:转移、销售、修改硬件状态、清理使用痕迹、回收到总部、清空转移记录 */
    @TableField(value = "operate_type")
    @Excel(name = "操作类型")
    @ApiModelProperty(name = "operateType",value= "操作类型:转移、销售、修改硬件状态、清理使用痕迹、回收到总部、清空转移记录" )
    private String operateType;


    /** 操作描述 */
    @TableField(value = "operate_desc")
    @Excel(name = "操作描述")
    @ApiModelProperty(name = "operateDesc",value= "操作描述" )
    private String operateDesc;


    /** 原因 */
    @TableField(value = "reason")
    @Excel(name = "原因")
    @ApiModelProperty(name = "reason",value= "原因" )
    private String reason;


    /** 操作人 */
    @TableField(value = "operate_user")
    @Excel(name = "操作人")
    @ApiModelProperty(name = "operateUser",value= "操作人" )
    private String operateUser;

    @ApiModelProperty(name = "operateUserName",value= "操作人姓名" )
    private String operateUserName;



    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn() 
    {
        return sn;
    }
    public void setOperateType(String operateType)
    {
        this.operateType = operateType;
    }

    public String getOperateType()
    {
        return operateType;
    }
    public void setOperateDesc(String operateDesc)
    {
        this.operateDesc = operateDesc;
    }

    public String getOperateDesc() 
    {
        return operateDesc;
    }
    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    public String getReason() 
    {
        return reason;
    }
    public void setOperateUser(String operateUser) 
    {
        this.operateUser = operateUser;
    }

    public String getOperateUser() 
    {
        return operateUser;
    }

    public String getOperateUserName() {
        return operateUserName;
    }

    public void setOperateUserName(String operateUserName) {
        this.operateUserName = operateUserName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sn", getSn())
            .append("operateType", getOperateType())
            .append("operateDesc", getOperateDesc())
            .append("reason", getReason())
            .append("operateUser", getOperateUser())
            .append("createTime", getCreateTime())
            .toString();
    }
}
