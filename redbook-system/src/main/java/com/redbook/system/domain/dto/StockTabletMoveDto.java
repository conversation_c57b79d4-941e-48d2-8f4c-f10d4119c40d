package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(reference = "StockTabletMoveDto")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StockTabletMoveDto {

    @ApiModelProperty(name = "ids", value = "选中的设备id")
    @NotEmpty(message = "请选择设备id")
    private List<Integer> ids;

    @ApiModelProperty(name = "agentId", value = "代理商id")
    @NotNull(message = "代理商id不能为空")
    @Min(1)
    private Integer agentId;

    @ApiModelProperty(name = "exclusiveShopId", value = "专卖店id")
    private Integer exclusiveShopId;

    @ApiModelProperty(name = "desc", value = "备注")
    @NotEmpty(message = "备注不能为空")
    private String desc;

}
