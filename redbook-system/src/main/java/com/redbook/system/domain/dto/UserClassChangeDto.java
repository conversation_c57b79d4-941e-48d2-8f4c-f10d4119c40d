package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;
@ApiModel(value = "用户变更班级")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserClassChangeDto {
    @ApiModelProperty("代理商id")
    @NotNull(message = "代理商id不能为空")
    private String aid;
    @ApiModelProperty("专卖店id")
    private Integer exclusiveShopId;
    @ApiModelProperty("班级id")
    private String classId;
    @ApiModelProperty("老师id")
    private String teacherId;
    @ApiModelProperty("用户id列表")
    private List<String>userId;
}
