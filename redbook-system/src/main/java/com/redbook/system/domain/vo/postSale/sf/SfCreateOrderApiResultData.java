package com.redbook.system.domain.vo.postSale.sf;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("公共响应参数")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SfCreateOrderApiResultData {

    /**
     * success : true
     * errorCode : S0000
     * errorMsg : null
     * msgData : {"orderId":"JX2025041500006","originCode":"010","destCode":"379","filterResult":2,"remark":"","url":null,"paymentLink":null,"isUpstairs":null,"isSpecialWarehouseService":null,"mappingMark":null,"agentMailno":null,"returnExtraInfoList":null,"waybillNoInfoList":[{"waybillType":1,"waybillNo":"SF7444496232592","boxNo":null,"length":null,"width":null,"height":null,"weight":null,"volume":null}],"routeLabelInfo":[{"code":"1000","routeLabelData":{"waybillNo":"SF7444496232592","sourceTransferCode":"010W","sourceCityCode":"010","sourceDeptCode":"010","sourceTeamCode":"","destCityCode":"379","destDeptCode":"379AK","destDeptCodeMapping":"","destTeamCode":"021","destTeamCodeMapping":"","destTransferCode":"379","destRouteLabel":"379AK-010B同","proName":"","cargoTypeCode":"C201","limitTypeCode":"T4","expressTypeCode":"B1","codingMapping":"D26","codingMappingOut":"","xbFlag":"0","printFlag":"000000000","twoDimensionCode":"MMM={'k1':'379','k2':'379AK','k3':'021','k4':'T4','k5':'SF7444496232592','k6':'','k7':'b8faf86e'}","proCode":"特快","printIcon":"00010000","abFlag":"","destPortCode":"","destCountry":"","destPostCode":"","goodsValueTotal":"","currencySymbol":"","cusBatch":"","goodsNumber":"","errMsg":"","checkCode":"b8faf86e","proIcon":"","fileIcon":"","fbaIcon":"","icsmIcon":"","destGisDeptCode":"379AK","newIcon":null,"sendAreaCode":null,"destinationStationCode":null,"sxLabelDestCode":null,"sxDestTransferCode":null,"sxCompany":null,"newAbFlag":null,"destAddrKeyWord":"","rongType":null,"waybillIconList":null},"message":"SF7444496232592:"}],"contactInfoList":null,"sendStartTm":null,"customerRights":null,"expressTypeId":null}
     */
    @ApiModelProperty(name = "success", value = "true 请求成功，false 请求失败")
    private boolean success;
    @ApiModelProperty(name = "errorCode", value = "错误编码，S0000成功")
    private String errorCode;
    @ApiModelProperty(name = "errorMsg", value = "错误描述")
    private Object errorMsg;
    @ApiModelProperty(name = "msgData", value = "返回的详细数据")
    private MsgDataBean msgData;

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class MsgDataBean {
        /**
         * orderId : JX2025041500006
         * originCode : 010
         * destCode : 379
         * filterResult : 2
         * remark :
         * url : null
         * paymentLink : null
         * isUpstairs : null
         * isSpecialWarehouseService : null
         * mappingMark : null
         * agentMailno : null
         * returnExtraInfoList : null
         * waybillNoInfoList : [{"waybillType":1,"waybillNo":"SF7444496232592","boxNo":null,"length":null,"width":null,"height":null,"weight":null,"volume":null}]
         * routeLabelInfo : [{"code":"1000","routeLabelData":{"waybillNo":"SF7444496232592","sourceTransferCode":"010W","sourceCityCode":"010","sourceDeptCode":"010","sourceTeamCode":"","destCityCode":"379","destDeptCode":"379AK","destDeptCodeMapping":"","destTeamCode":"021","destTeamCodeMapping":"","destTransferCode":"379","destRouteLabel":"379AK-010B同","proName":"","cargoTypeCode":"C201","limitTypeCode":"T4","expressTypeCode":"B1","codingMapping":"D26","codingMappingOut":"","xbFlag":"0","printFlag":"000000000","twoDimensionCode":"MMM={'k1':'379','k2':'379AK','k3':'021','k4':'T4','k5':'SF7444496232592','k6':'','k7':'b8faf86e'}","proCode":"特快","printIcon":"00010000","abFlag":"","destPortCode":"","destCountry":"","destPostCode":"","goodsValueTotal":"","currencySymbol":"","cusBatch":"","goodsNumber":"","errMsg":"","checkCode":"b8faf86e","proIcon":"","fileIcon":"","fbaIcon":"","icsmIcon":"","destGisDeptCode":"379AK","newIcon":null,"sendAreaCode":null,"destinationStationCode":null,"sxLabelDestCode":null,"sxDestTransferCode":null,"sxCompany":null,"newAbFlag":null,"destAddrKeyWord":"","rongType":null,"waybillIconList":null},"message":"SF7444496232592:"}]
         * contactInfoList : null
         * sendStartTm : null
         * customerRights : null
         * expressTypeId : null
         */
        @ApiModelProperty(name = "orderId", value = "客户订单号")
        private String orderId;
        @ApiModelProperty(name = "originCode", value = "原寄地区域代码，可用于顺丰 电子运单标签打印")
        private String originCode;
        @ApiModelProperty(name = "destCode", value = "目的地区域代码，可用于顺丰 电子运单标签打印")
        private String destCode;
        @ApiModelProperty(name = "filterResult", value = "筛单结果： 1：人工确认 2：可收派 3：不可以收派")
        private int filterResult;
        @ApiModelProperty(name = "remark", value = "如果filter_result=3时为必填， 不可以收派的原因代码： 1：收方超范围 2：派方超范围 3：其它原因 高峰管控提示信息 【数字】：【高峰管控提示信息】 (如 4：温馨提示 ，1：春运延时)")
        private String remark;
        @ApiModelProperty(name = "url", value = "二维码URL （用于CX退货操作的URL")
        private Object url;
        @ApiModelProperty(name = "paymentLink", value = "用于第三方支付运费的URL")
        private Object paymentLink;
        @ApiModelProperty(name = "isUpstairs", value = "是否送货上楼 1:是")
        private Object isUpstairs;
        @ApiModelProperty(name = "isSpecialWarehouseService", value = "true 包含特殊仓库增值服务")
        private Object isSpecialWarehouseService;
        @ApiModelProperty(name = "mappingMark", value = "")
        private Object mappingMark;
        @ApiModelProperty(name = "agentMailno", value = "")
        private Object agentMailno;
        @ApiModelProperty(name = "returnExtraInfoList", value = "返回信息扩展属性")
        private Object returnExtraInfoList;
        @ApiModelProperty(name = "contactInfoList", value = "")
        private Object contactInfoList;
        @ApiModelProperty(name = "sendStartTm", value = "")
        private Object sendStartTm;
        @ApiModelProperty(name = "customerRights", value = "")
        private Object customerRights;
        @ApiModelProperty(name = "expressTypeId", value = "")
        private Object expressTypeId;
        @ApiModelProperty(name = "waybillNoInfoList", value = "顺丰运单号")
        private List<WaybillNoInfoListBean> waybillNoInfoList;
        @ApiModelProperty(name = "routeLabelInfo", value = "路由标签，除少量特殊场景用户外，其余均会返回")
        private List<RouteLabelInfoBean> routeLabelInfo;

        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @Data
        public static class WaybillNoInfoListBean {
            /**
             * waybillType : 1
             * waybillNo : SF7444496232592
             * boxNo : null
             * length : null
             * width : null
             * height : null
             * weight : null
             * volume : null
             */

            private int waybillType;
            private String waybillNo;
            private Object boxNo;
            private Object length;
            private Object width;
            private Object height;
            private Object weight;
            private Object volume;


        }

        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @Data
        public static class RouteLabelInfoBean {
            /**
             * code : 1000
             * routeLabelData : {"waybillNo":"SF7444496232592","sourceTransferCode":"010W","sourceCityCode":"010","sourceDeptCode":"010","sourceTeamCode":"","destCityCode":"379","destDeptCode":"379AK","destDeptCodeMapping":"","destTeamCode":"021","destTeamCodeMapping":"","destTransferCode":"379","destRouteLabel":"379AK-010B同","proName":"","cargoTypeCode":"C201","limitTypeCode":"T4","expressTypeCode":"B1","codingMapping":"D26","codingMappingOut":"","xbFlag":"0","printFlag":"000000000","twoDimensionCode":"MMM={'k1':'379','k2':'379AK','k3':'021','k4':'T4','k5':'SF7444496232592','k6':'','k7':'b8faf86e'}","proCode":"特快","printIcon":"00010000","abFlag":"","destPortCode":"","destCountry":"","destPostCode":"","goodsValueTotal":"","currencySymbol":"","cusBatch":"","goodsNumber":"","errMsg":"","checkCode":"b8faf86e","proIcon":"","fileIcon":"","fbaIcon":"","icsmIcon":"","destGisDeptCode":"379AK","newIcon":null,"sendAreaCode":null,"destinationStationCode":null,"sxLabelDestCode":null,"sxDestTransferCode":null,"sxCompany":null,"newAbFlag":null,"destAddrKeyWord":"","rongType":null,"waybillIconList":null}
             * message : SF7444496232592:
             */
            @ApiModelProperty(name = "code", value = "返回调用结果，1000：调用成功； 其他调用失败")
            private String code;
            @ApiModelProperty(name = "routeLabelData", value = "路由标签数据详细数据，除少量特殊场景用户外，其余均会返回")
            private RouteLabelDataBean routeLabelData;
            @ApiModelProperty(name = "message", value = "失败异常描述")
            private String message;

            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            @Data
            public static class RouteLabelDataBean {
                /**
                 * waybillNo : SF7444496232592
                 * sourceTransferCode : 010W
                 * sourceCityCode : 010
                 * sourceDeptCode : 010
                 * sourceTeamCode :
                 * destCityCode : 379
                 * destDeptCode : 379AK
                 * destDeptCodeMapping :
                 * destTeamCode : 021
                 * destTeamCodeMapping :
                 * destTransferCode : 379
                 * destRouteLabel : 379AK-010B同
                 * proName :
                 * cargoTypeCode : C201
                 * limitTypeCode : T4
                 * expressTypeCode : B1
                 * codingMapping : D26
                 * codingMappingOut :
                 * xbFlag : 0
                 * printFlag : 000000000
                 * twoDimensionCode : MMM={'k1':'379','k2':'379AK','k3':'021','k4':'T4','k5':'SF7444496232592','k6':'','k7':'b8faf86e'}
                 * proCode : 特快
                 * printIcon : 00010000
                 * abFlag :
                 * destPortCode :
                 * destCountry :
                 * destPostCode :
                 * goodsValueTotal :
                 * currencySymbol :
                 * cusBatch :
                 * goodsNumber :
                 * errMsg :
                 * checkCode : b8faf86e
                 * proIcon :
                 * fileIcon :
                 * fbaIcon :
                 * icsmIcon :
                 * destGisDeptCode : 379AK
                 * newIcon : null
                 * sendAreaCode : null
                 * destinationStationCode : null
                 * sxLabelDestCode : null
                 * sxDestTransferCode : null
                 * sxCompany : null
                 * newAbFlag : null
                 * destAddrKeyWord :
                 * rongType : null
                 * waybillIconList : null
                 */
                @ApiModelProperty(name = "waybillNo", value = "运单号")
                private String waybillNo;
                @ApiModelProperty(name = "sourceTransferCode", value = "原寄地中转场")
                private String sourceTransferCode;
                @ApiModelProperty(name = "sourceCityCode", value = "原寄地城市代码")
                private String sourceCityCode;
                @ApiModelProperty(name = "sourceDeptCode", value = "原寄地网点代码")
                private String sourceDeptCode;
                @ApiModelProperty(name = "sourceTeamCode", value = "原寄地单元区域")
                private String sourceTeamCode;
                @ApiModelProperty(name = "destCityCode", value = "目的地城市代码, eg:755")
                private String destCityCode;
                @ApiModelProperty(name = "destDeptCode", value = "目的地网点代码, eg:755AQ")
                private String destDeptCode;
                @ApiModelProperty(name = "destDeptCodeMapping", value = "目的地网点代码映射码")
                private String destDeptCodeMapping;
                @ApiModelProperty(name = "destTeamCode", value = "目的地单元区域, eg:001")
                private String destTeamCode;
                @ApiModelProperty(name = "destTeamCodeMapping", value = "目的地单元区域映射码")
                private String destTeamCodeMapping;
                @ApiModelProperty(name = "destTransferCode", value = "目的地中转场")
                private String destTransferCode;
                @ApiModelProperty(name = "destRouteLabel", value = "若返回路由标签，则此项必会返回。如果手打是一段码，检查是否地址异常。打单时的路由标签信息如果是大网的路由标签,这里的值是目的地网点代码,如果 是同城配的路由标签,这里的值是根据同城配的设置映射出来的值,不同的配置结果会不一样,不能根据-符号切分(如:上海同城配,可能是:集散点-目的地网点-接驳点,也有可能是目的地网点代码-集散点-接驳点)")
                private String destRouteLabel;
                @ApiModelProperty(name = "proName", value = "产品名称 对应RLS:pro_name")
                private String proName;
                @ApiModelProperty(name = "cargoTypeCode", value = "快件内容: 如:C816、SP601")
                private String cargoTypeCode;
                @ApiModelProperty(name = "limitTypeCode", value = "时效代码, 如:T4")
                private String limitTypeCode;
                @ApiModelProperty(name = "expressTypeCode", value = "产品类型,如:B1")
                private String expressTypeCode;
                @ApiModelProperty(name = "codingMapping", value = "入港映射码 eg:S10 地址详细必会返回")
                private String codingMapping;
                @ApiModelProperty(name = "codingMappingOut", value = "出港映射码")
                private String codingMappingOut;
                @ApiModelProperty(name = "xbFlag", value = "XB标志 0:不需要打印XB 1:需要打印XB")
                private String xbFlag;
                @ApiModelProperty(name = "printFlag", value = "打印标志 返回值总共有9位,每位只 有0和1两种,0表示按丰密 面单默认的规则,1是显示, 顺序如下,如111110000 表示打印寄方姓名、寄方 电话、寄方公司名、寄方 地址和重量,收方姓名、收 方电话、收方公司和收方 地址按丰密面单默认规则 1:寄方姓名 2:寄方电话 3:寄方公司名 4:寄方地址 5:重量 6:收方姓名 7:收方电话 8:收方公司名 9:收方地址")
                private String printFlag;
                @ApiModelProperty(name = "twoDimensionCode", value = "二维码 根据规则生成字符串信息, 格式为MMM={‘k1’:’(目的 地中转场代码)’,‘k2’:’(目的 地原始网点代码)’,‘k3’:’(目 的地单元区域)’,‘k4’:’(附件 通过三维码(express_type_code、 limit_type_code、 cargo_type_code)映射时效类型)’,‘k5’:’(运单 号)’,‘k6’:’(AB标识)’,‘k7’:’( 校验码)’}")
                private String twoDimensionCode;
                @ApiModelProperty(name = "proCode", value = "时效类型: 值为二维码中的K4")
                private String proCode;
                @ApiModelProperty(name = "printIcon", value = "打印图标,根据托寄物判断需 要打印的图标(重货,蟹类,生鲜,易碎，Z标) 返回值有8位，每一位只有0和1两种， 0表示按运单默认的规则， 1表示显示。后面两位默认0备用。 顺序如下：重货,蟹类,生鲜,易碎,医药类,Z标,酒标,0 如：00000000表示不需要打印重货，蟹类，生鲜，易碎 ,医药,Z标,酒标,备用")
                private String printIcon;
                @ApiModelProperty(name = "abFlag", value = "AB标")
                private String abFlag;
                @ApiModelProperty(name = "destPortCode", value = "目的地口岸代码")
                private String destPortCode;
                @ApiModelProperty(name = "destCountry", value = "目的国别(国别代码如:JP)")
                private String destCountry;
                @ApiModelProperty(name = "destPostCode", value = "目的地邮编")
                private String destPostCode;
                @ApiModelProperty(name = "goodsValueTotal", value = "总价值(保留两位小数,数字类型,可补位)")
                private String goodsValueTotal;
                @ApiModelProperty(name = "currencySymbol", value = "币种")
                private String currencySymbol;
                @ApiModelProperty(name = "cusBatch", value = "")
                private String cusBatch;
                @ApiModelProperty(name = "goodsNumber", value = "件数")
                private String goodsNumber;
                @ApiModelProperty(name = "errMsg", value = "查询出现异常时返回信息。 返回代码: 0 系统异常 1 未找到面单")
                private String errMsg;
                @ApiModelProperty(name = "checkCode", value = "")
                private String checkCode;
                @ApiModelProperty(name = "proIcon", value = "")
                private String proIcon;
                @ApiModelProperty(name = "fileIcon", value = "")
                private String fileIcon;
                @ApiModelProperty(name = "fbaIcon", value = "")
                private String fbaIcon;
                @ApiModelProperty(name = "icsmIcon", value = "")
                private String icsmIcon;
                @ApiModelProperty(name = "destGisDeptCode", value = "")
                private String destGisDeptCode;
                @ApiModelProperty(name = "newIcon", value = "")
                private Object newIcon;
                @ApiModelProperty(name = "sendAreaCode", value = "")
                private Object sendAreaCode;
                @ApiModelProperty(name = "destinationStationCode", value = "")
                private Object destinationStationCode;
                @ApiModelProperty(name = "sxLabelDestCode", value = "")
                private Object sxLabelDestCode;
                @ApiModelProperty(name = "sxDestTransferCode", value = "")
                private Object sxDestTransferCode;
                @ApiModelProperty(name = "sxCompany", value = "")
                private Object sxCompany;
                @ApiModelProperty(name = "newAbFlag", value = "")
                private Object newAbFlag;
                @ApiModelProperty(name = "destAddrKeyWord", value = "")
                private String destAddrKeyWord;
                @ApiModelProperty(name = "rongType", value = "")
                private Object rongType;
                @ApiModelProperty(name = "waybillIconList", value = "")
                private Object waybillIconList;
            }
        }
    }
}
