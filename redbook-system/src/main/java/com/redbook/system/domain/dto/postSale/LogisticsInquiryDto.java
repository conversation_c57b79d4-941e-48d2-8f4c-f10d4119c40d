package com.redbook.system.domain.dto.postSale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("审批")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LogisticsInquiryDto {
    @ApiModelProperty(name = "orderId", value = "订单id")
    private Integer orderId;

    @ApiModelProperty(name = "itemId", value = "条目id")
    private Integer itemId;

    @ApiModelProperty(name = "expressCompanyId", value = "快递公司id")
    private Integer expressCompanyId;

    @ApiModelProperty(name = "expressNo", value = "快递单号")
    private String expressNo;

    @ApiModelProperty(name = "type 1:寄件 2：回寄", value = "类型")
    private Integer type;
}
