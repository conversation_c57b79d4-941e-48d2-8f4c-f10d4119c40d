package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 代理商管理映射对象 agent_mapping
 * 
 * <AUTHOR>
 * @date 2022-11-09
 */
@ApiModel("代理商管理映射")
@Builder
@Data
public class AgentMapping extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 角色id */
    @Excel(name = "角色id")
    @ApiModelProperty(name = "角色id" )
    @TableField(value = "role_id")
    private Long roleId;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(name = "用户id" )
    @TableField(value = "user_id")
    private Long userId;

    /** 代理商id */
    @Excel(name = "代理商id")
    @ApiModelProperty(name = "代理商id" )
    @TableField(value = "agent_id")
    private Long agentId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRoleId(Long roleId) 
    {
        this.roleId = roleId;
    }

    public Long getRoleId() 
    {
        return roleId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setAgentId(Long agentId) 
    {
        this.agentId = agentId;
    }

    public Long getAgentId() 
    {
        return agentId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("roleId", getRoleId())
            .append("userId", getUserId())
            .append("agentId", getAgentId())
            .toString();
    }
}
