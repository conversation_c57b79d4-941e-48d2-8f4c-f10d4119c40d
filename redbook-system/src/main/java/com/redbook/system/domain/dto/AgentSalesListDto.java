package com.redbook.system.domain.dto;

import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentSalesListDto extends BaseEntity {
    /** 合同级别 */
    @ApiModelProperty(name = "agreementLevel",value= "合同级别 1省 2市 3县" )
    private Long agreementLevel;
    @ApiModelProperty(name = "startDate",value = "开始日期")
    private String startDate;
    @ApiModelProperty(name = "endDate",value = "截至日期")
    private String endDate;
    @ApiModelProperty(name = "agentStatus", value = "代理商状态 0正常 1退盟 2删除")
    private Long agentStatus;
    @ApiModelProperty(name = "queryScope", value = "查询范围 0代理商销量（默认） 1专卖店销量")
    private Integer queryScope;
    @ApiModelProperty(name = "system", value = "系统 SYNC:同步，KID:儿童 其它:全部")
    private String system;
}
