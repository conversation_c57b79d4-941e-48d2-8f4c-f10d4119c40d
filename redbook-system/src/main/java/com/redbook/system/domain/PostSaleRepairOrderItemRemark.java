package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 寄修单条目备注对象 post_sale_repair_order_item_remark
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@ApiModel("寄修单条目备注")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleRepairOrderItemRemark extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 订单物品条目id
     */
    @TableField(value = "item_id")
    @Excel(name = "订单物品条目id")
    @ApiModelProperty(name = "remarkItemId", value = "订单物品条目id")
    private Integer itemId;


    /**
     * 额外信息
     */
    @TableField(value = "extra")
    @Excel(name = "额外信息")
    @ApiModelProperty(name = "extra", value = "额外信息")
    private String extra;


    /**
     * 1:报废 2：报价异议 3：放弃维修
     */
    @TableField(value = "type")
    @Excel(name = "1:报废 2：报价异议 3：放弃维修")
    @ApiModelProperty(name = "type", value = "1:报废 2：报价异议 3：放弃维修")
    private Integer type;


}
