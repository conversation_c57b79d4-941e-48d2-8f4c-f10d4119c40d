package com.redbook.system.domain.market;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-22 16:48
 */
@ApiModel("咨询夹图片")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ConsultPictureFolderDTO {

    @ApiModelProperty(name = "childList",value= "图片记录列表" )
    private List<Child> childList;

    @Data
    public static class Child{
        @ApiModelProperty(name = "id",value= "主键id" )
        private Integer id;
        @ApiModelProperty(name = "sort",value= "排序" )
        private Integer sort;
    }


}
