package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 代理商活动-商品对象 agent_activity_item
 * 
 * <AUTHOR>
 * @date 2025-02-11
 */
@ApiModel("代理商活动-商品")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AgentActivityItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /** 活动表id */
    @TableField(value = "activity_id")
    @Excel(name = "活动表id")
    @ApiModelProperty(name = "activityId",value= "活动表id" )
    private Integer activityId;


    /** 商品ID */
    @TableField(value = "goods_id")
    @Excel(name = "商品ID")
    @ApiModelProperty(name = "goodsId",value= "商品ID" )
    private Integer goodsId;

    @TableField(exist = false)
    @ApiModelProperty(name = "goodsIdList",value= "多个商品ID" )
    private List<Integer> goodsIdList;

}
