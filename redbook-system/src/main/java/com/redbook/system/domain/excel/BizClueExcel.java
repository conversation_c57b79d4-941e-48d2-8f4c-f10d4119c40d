package com.redbook.system.domain.excel;

import com.redbook.common.annotation.Excel;
import lombok.Data;

/**
 * 线索excel导入类
 * <AUTHOR>
 * @date 2024-03-27 10:35
 */
@Data
public class BizClueExcel{

    /** 客户名称 */
    @Excel(name = "线索姓名",required = true)
    private String customerName;

    /** 联系电话 */
    @Excel(name = "电话",required = true)
    private String phone;

    /** 渠道名称 */
    @Excel(name = "渠道名称",required = true)
    private String channelName;

    /** 获取方式：1电销，2地推，3其他 */
    @Excel(name = "获取方式",readConverterExp="1=电销,2=地推,3=其他",required = true)
    private Integer customerSource;


    /**
     * ExcelUtil导入方法赋值此参数
     * 接收导入前的校验信息，必须有这个字段
     * 行号
     */
    private Integer rowNum;

    /**
     * ExcelUtil导入方法赋值此参数
     * 接收导入前的校验信息，必须有这个字段
     * 错误信息
     */
    private String errorMsg;

}
