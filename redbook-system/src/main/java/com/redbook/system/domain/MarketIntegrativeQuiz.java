package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;


/**
 * 代理商网络营销-综合测评对象 market_integrative_quiz
 * 
 * <AUTHOR>
 * @date 2025-02-19
 */
@ApiModel("代理商网络营销-综合测评")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MarketIntegrativeQuiz extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 代理商id */
    @TableField(value = "agent_id")
    @Excel(name = "代理商id")
    @ApiModelProperty(name = "agentId",value= "代理商id" )
    private Long agentId;
    @ApiModelProperty(name = "exclusiveShopId",value= "专卖店id" )
    private Integer exclusiveShopId;
    @ApiModelProperty(name = "shopCode",value= "专卖店码" )
    private String shopCode;

    /** 测评类型 */
    @TableField(value = "quiz_type")
    @Excel(name = "测评类型")
    @ApiModelProperty(name = "quizType",value= "测评类型" )
    private String quizType;


    /** 测评名称 */
    @TableField(value = "quiz_name")
    @Excel(name = "测评名称")
    @ApiModelProperty(name = "quizName",value= "测评名称" )
    private String quizName;

    @TableField(value = "course_name")
    @Excel(name = "版本课程单元名称")
    @ApiModelProperty(name = "courseName",value= "版本课程单元名称" )
    private String courseName;

    @TableField(value = "course_name")
    @Excel(name = "版本课程单元map")
    @ApiModelProperty(name = "courseMap",value= "版本课程单元map" )
    private String courseMap;

    @ApiModelProperty(name = "versionId",value= "版本id" )
    private Integer versionId;
    @ApiModelProperty(name = "courseId",value= "课程id" )
    private Integer courseId;
    @ApiModelProperty(name = "unitIdList",value= "单元list" )
    private List<Integer> unitIdList;


    /** 礼品图片 */
    @TableField(value = "img_url")
    @Excel(name = "礼品图片")
    @ApiModelProperty(name = "imgUrl",value= "礼品图片" )
    private String imgUrl;

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    public Integer getCourseId() {
        return courseId;
    }

    public void setCourseId(Integer courseId) {
        this.courseId = courseId;
    }

    public List<Integer> getUnitIdList() {
        return unitIdList;
    }

    public void setUnitIdList(List<Integer> unitIdList) {
        this.unitIdList = unitIdList;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAgentId(Long agentId) 
    {
        this.agentId = agentId;
    }

    public Long getAgentId() 
    {
        return agentId;
    }
    public void setQuizType(String quizType) 
    {
        this.quizType = quizType;
    }

    public String getQuizType() 
    {
        return quizType;
    }
    public void setQuizName(String quizName) 
    {
        this.quizName = quizName;
    }

    public String getQuizName() 
    {
        return quizName;
    }
    public void setImgUrl(String imgUrl) 
    {
        this.imgUrl = imgUrl;
    }

    public String getImgUrl() 
    {
        return imgUrl;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getCourseMap() {
        return courseMap;
    }

    public void setCourseMap(String courseMap) {
        this.courseMap = courseMap;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agentId", getAgentId())
            .append("quizType", getQuizType())
            .append("quizName", getQuizName())
            .append("imgUrl", getImgUrl())
            .append("createTime", getCreateTime())
            .toString();
    }
}
