package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 寄修单条目维修物料使用对象 post_sale_repair_order_item_spare_part
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@ApiModel("寄修单条目维修物料使用")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleRepairOrderItemSparePart extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;
    private Integer itemId;
    private Integer sparePartId;
    private Integer useNum;
    private BigDecimal sparePartCost;
    private BigDecimal sparePartSalePrice;
    private String sparePartName;

}
