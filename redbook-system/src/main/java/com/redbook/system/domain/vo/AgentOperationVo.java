package com.redbook.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "代理商经营情况信息", description = "AgentOperationVo")
@Data
@Builder
public class AgentOperationVo {
    @ApiModelProperty(value = "余额")
    private BigDecimal amount;
    @ApiModelProperty(value = "小红本订购数量")
    private Integer redBookOrderNum;
    @ApiModelProperty(value = "小红本销售数量")
    private Integer redBookSaleNum;
    @ApiModelProperty(value = "小红本正式用户")
    private Integer redBookUserNum;
    @ApiModelProperty(value = "小红本体验用户")
    private Integer redBookUserExpNum;
}
