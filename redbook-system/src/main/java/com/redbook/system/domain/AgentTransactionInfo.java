package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 代理商交易记录对象 agent_transaction_info
 * 
 * <AUTHOR>
 * @date 2022-11-22
 */
@ApiModel("代理商交易记录")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AgentTransactionInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 校区id */
    @Excel(name = "校区id")
    @ApiModelProperty(name = "agentId",value= "校区id" )
    private Long agentId;

    /** 交易类型 */
    @Excel(name = "交易类型")
    @ApiModelProperty(name = "transactionType",value= "交易类型" )
    private String transactionType;

    /** 资金类型 */
    @Excel(name = "资金类型")
    @ApiModelProperty(name = "fundType",value= "资金类型" )
    private Integer fundType;

    /** 订单号 */
    @Excel(name = "订单号")
    @ApiModelProperty(name = "indentNumber",value= "订单号" )
    private String indentNumber;

    /** 金额 */
    @Excel(name = "金额")
    @ApiModelProperty(name = "money",value= "金额" )
    private BigDecimal money;

    /** 收支类型 0收入 1支出 */
    @Excel(name = "收支类型 0收入 1支出")
    @ApiModelProperty(name = "paymentType",value= "收支类型 0收入 1支出" )
    private Integer paymentType;

    /** 余额 */
    @Excel(name = "余额")
    @ApiModelProperty(name = "balance",value= "余额" )
    private BigDecimal balance;

    /** 交易时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交易时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "transactionTime",value= "交易时间" )
    private Date transactionTime;

    /** 充值方式 */
    @Excel(name = "充值方式")
    @ApiModelProperty(name = "topUpWayId",value= "充值方式" )
    private Integer topUpWayId;

    /** 充值款项类别 */
    @Excel(name = "充值款项类别")
    @ApiModelProperty(name = "topUpTypeId",value= "充值款项类别" )
    private Integer topUpTypeId;

    @ApiModelProperty(name = "refundStatus",value= "退款状态 0未退款，1已退款（只针对支出）" )
    private Boolean refundStatus;

    @ApiModelProperty(name = "invoiceStatus",value= "开发票状态 0未开，1已开（只针对30天前的支出）" )
    private Boolean invoiceStatus;

    @TableField(exist = false)
    private String startTime;
    @TableField(exist = false)
    private String endTime;
    @TableField(exist = false)
    @ApiModelProperty(name = "exclusiveShopName",value= "专卖店名称" )
    private String exclusiveShopName;



    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setAgentId(Long agentId)
    {
        this.agentId = agentId;
    }

    public Long getAgentId()
    {
        return agentId;
    }
    public void setTransactionType(String transactionType)
    {
        this.transactionType = transactionType;
    }

    public String getTransactionType()
    {
        return transactionType;
    }
    public void setFundType(Integer fundType)
    {
        this.fundType = fundType;
    }

    public Integer getFundType()
    {
        return fundType;
    }
    public void setIndentNumber(String indentNumber)
    {
        this.indentNumber = indentNumber;
    }

    public String getIndentNumber()
    {
        return indentNumber;
    }
    public void setMoney(BigDecimal money)
    {
        this.money = money;
    }

    public BigDecimal getMoney()
    {
        return money;
    }
    public void setPaymentType(Integer paymentType)
    {
        this.paymentType = paymentType;
    }

    public Integer getPaymentType()
    {
        return paymentType;
    }
    public void setBalance(BigDecimal balance)
    {
        this.balance = balance;
    }

    public BigDecimal getBalance()
    {
        return balance;
    }
    public void setTransactionTime(Date transactionTime)
    {
        this.transactionTime = transactionTime;
    }

    public Date getTransactionTime()
    {
        return transactionTime;
    }
    public void setTopUpWayId(Integer topUpWayId)
    {
        this.topUpWayId = topUpWayId;
    }

    public Integer getTopUpWayId()
    {
        return topUpWayId;
    }
    public void setTopUpTypeId(Integer topUpTypeId)
    {
        this.topUpTypeId = topUpTypeId;
    }

    public Integer getTopUpTypeId()
    {
        return topUpTypeId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("agentId", getAgentId())
            .append("transactionType", getTransactionType())
            .append("fundType", getFundType())
            .append("indentNumber", getIndentNumber())
            .append("money", getMoney())
            .append("paymentType", getPaymentType())
            .append("balance", getBalance())
            .append("transactionTime", getTransactionTime())
            .append("topUpWayId", getTopUpWayId())
            .append("topUpTypeId", getTopUpTypeId())
            .toString();
    }
}
