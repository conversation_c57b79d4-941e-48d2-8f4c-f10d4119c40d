package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 寄修单条目签收对象 post_sale_repair_order_item_sign
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@ApiModel("寄修单条目签收")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleRepairOrderItemSign extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 寄修单id */
    @TableField(value = "order_id")
    @Excel(name = "寄修单id")
    @ApiModelProperty(name = "orderId",value= "寄修单id" )
    private Long orderId;


    /** 寄修单条目id */
    @TableField(value = "item_id")
    @Excel(name = "寄修单条目id")
    @ApiModelProperty(name = "itemId",value= "寄修单条目id" )
    private Long itemId;


    /** 设备类型，1:整套（平板+键盘） 2:仅平板 3:仅键盘 4:耳机 */
    @TableField(value = "device_type")
    @Excel(name = "设备类型，1:整套", readConverterExp = "平=板+键盘")
    @ApiModelProperty(name = "deviceType",value= "设备类型，1:整套" )
    private Long deviceType;


    /** 物品清单，用|分割，1:平板主机 2:键盘 3:流量卡 4:充电器 5:充电线 6:彩色包装盒 7:耳机  99:其他*/
    @TableField(value = "product_types")
    @Excel(name = "物品清单，用|分割，1:平板主机 2:键盘 3:流量卡 4:充电器 5:充电线 6:彩色包装盒 7:耳机  99:其他")
    @ApiModelProperty(name = "productTypes",value= "物品清单，用|分割，1:平板主机 2:键盘 3:流量卡 4:充电器 5:充电线 6:彩色包装盒 7:耳机  99:其他" )
    private String productTypes;

    @TableField(value = "batch_id")
    @Excel(name = "产品型号，复用redbook_tablet_batch")
    @ApiModelProperty(name = "batchId",value= "产品型号，复用复用redbook_tablet_batch" )
    private Long batchId;

    /** 产品型号，复用redbook_tablet_model */
    @TableField(value = "model_id")
    @Excel(name = "产品型号，复用redbook_tablet_model")
    @ApiModelProperty(name = "modelId",value= "产品型号，复用redbook_tablet_model" )
    private Long modelId;


    /** 产品sn码 */
    @TableField(value = "product_sn")
    @Excel(name = "产品sn码")
    @ApiModelProperty(name = "productSn",value= "产品sn码" )
    private Long productSn;


    /** 激活日期(首次用户登录日期) */
    @TableField(value = "activation_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "激活日期(首次用户登录日期)", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "activationDate",value= "激活日期(首次用户登录日期)" )
    private Date activationDate;


    /** 保修期到期日，销售日期+1年 */
    @TableField(value = "expiration_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "保修期到期日，销售日期+1年", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "expirationDate",value= "保修期到期日，销售日期+1年" )
    private Date expirationDate;


    /** device_type=1时使用，键盘型号，复用redbook_tablet_model */
    @TableField(value = "model2_id")
    @Excel(name = "device_type=1时使用，键盘型号，复用redbook_tablet_model")
    @ApiModelProperty(name = "model2Id",value= "device_type=1时使用，键盘型号，复用redbook_tablet_model" )
    private String model2Id;


    /** device_type=1时使用，键盘sn码 */
    @TableField(value = "product_sn2")
    @Excel(name = "device_type=1时使用，键盘sn码")
    @ApiModelProperty(name = "productSn2",value= "device_type=1时使用，键盘sn码" )
    private Long productSn2;


    /** 物品选择其他时的说明 */
    @TableField(value = "product_other_text")
    @Excel(name = "物品选择其他时的说明")
    @ApiModelProperty(name = "productOtherText",value= "物品选择其他时的说明" )
    private String productOtherText;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setDeviceType(Long deviceType) 
    {
        this.deviceType = deviceType;
    }

    public Long getDeviceType() 
    {
        return deviceType;
    }
    public void setProductTypes(String productTypes) 
    {
        this.productTypes = productTypes;
    }

    public String getProductTypes() 
    {
        return productTypes;
    }
    public void setModelId(Long modelId) 
    {
        this.modelId = modelId;
    }

    public Long getModelId() 
    {
        return modelId;
    }
    public void setProductSn(Long productSn) 
    {
        this.productSn = productSn;
    }

    public Long getProductSn() 
    {
        return productSn;
    }
    public void setActivationDate(Date activationDate) 
    {
        this.activationDate = activationDate;
    }

    public Date getActivationDate() 
    {
        return activationDate;
    }
    public void setExpirationDate(Date expirationDate) 
    {
        this.expirationDate = expirationDate;
    }

    public Date getExpirationDate() 
    {
        return expirationDate;
    }
    public void setModel2Id(String model2Id) 
    {
        this.model2Id = model2Id;
    }

    public String getModel2Id() 
    {
        return model2Id;
    }
    public void setProductSn2(Long productSn2) 
    {
        this.productSn2 = productSn2;
    }

    public Long getProductSn2() 
    {
        return productSn2;
    }
    public void setProductOtherText(String productOtherText) 
    {
        this.productOtherText = productOtherText;
    }

    public String getProductOtherText() 
    {
        return productOtherText;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("itemId", getItemId())
            .append("deviceType", getDeviceType())
            .append("productTypes", getProductTypes())
            .append("modelId", getModelId())
            .append("productSn", getProductSn())
            .append("activationDate", getActivationDate())
            .append("expirationDate", getExpirationDate())
            .append("model2Id", getModel2Id())
            .append("productSn2", getProductSn2())
            .append("productOtherText", getProductOtherText())
            .toString();
    }
}
