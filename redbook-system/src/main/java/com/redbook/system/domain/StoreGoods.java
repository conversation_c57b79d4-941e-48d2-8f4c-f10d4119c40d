package com.redbook.system.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-02-13 10:46
 */
@ApiModel("商品")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class StoreGoods
{
    private static final long serialVersionUID = 1L;


    private Integer id;
    @ApiModelProperty(name = "aid",value= "aid" )
    private String aid;
    @ApiModelProperty(name = "name",value= "商品名称" )
    private String name;
    @ApiModelProperty(name = "status",value= "状态" )
    private Integer status;
    @ApiModelProperty(name = "goods_type",value= "goods_type类型" )
    private Integer goodsType;
    @ApiModelProperty(name = "store_type",value= "store_type类型" )
    private Integer storeType;
    @ApiModelProperty(name = "exclusiveShopId",value= "专卖店id" )
    private Integer exclusiveShopId;
    @ApiModelProperty(name = "image_url",value= "图片" )
    private String imageUrl;
    @ApiModelProperty(name = "desc",value= "补充信息" )
    private String desc;
    @ApiModelProperty(name = "label",value= "标签" )
    private Integer label;
    @ApiModelProperty(name = "diamonds",value= "钻石" )
    private Integer diamonds;
    @ApiModelProperty(name = "sort",value= "排序" )
    private Integer sort;
    @ApiModelProperty(name = "user_id_fk",value= "user_id_fk" )
    private Integer userIdFk;
    @ApiModelProperty(name = "price",value= "价格" )
    private Integer price;
    @ApiModelProperty(name = "price_type",value= "price_type价格类型" )
    private Integer priceType;
    @ApiModelProperty(name = "stock",value= "stock" )
    private Integer stock;
    @ApiModelProperty(name = "updown",value= "updown" )
    private Integer updown;




}
