package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 小红本设备工作日志记录对象 redbook_tablet_work_log_record
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@ApiModel("小红本设备工作日志记录")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RedbookTabletWorkLogRecord
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 设备SN号码 */
    @TableField(value = "SN")
    @Excel(name = "设备SN号码")
    @ApiModelProperty(name = "sn",value= "设备SN号码" )
    private String sn;


    /** 状态：0 未启动，1 抓取中，2 打包上传中，3 重新上传，4 上传成功 */
    @TableField(value = "status")
    @Excel(name = "状态：0 未启动，1 抓取中，2 打包上传中，3 重新上传，4 上传成功")
    @ApiModelProperty(name = "status",value= "状态：0 未启动，1 抓取中，2 打包上传中，3 重新上传，4 上传成功" )
    private Integer status;


    /** 日志类型:MobileLog 1,ModemLog 2,NetWorkLog 4,GPSLog 16（1/2分隔） */
    @TableField(value = "log_type")
    @Excel(name = "日志类型:MobileLog 1,ModemLog 2,NetWorkLog 4,GPSLog 16", readConverterExp = "1/2分隔")
    @ApiModelProperty(name = "logType",value= "日志类型:MobileLog 1,ModemLog 2,NetWorkLog 4,GPSLog 16" )
    private String logType;


    /** 日志文件OSS链接 */
    @TableField(value = "log_oss_link")
    @Excel(name = "日志文件OSS链接")
    @ApiModelProperty(name = "logOssLink",value= "日志文件OSS链接" )
    private String logOssLink;


    /** 操作人 */
    @TableField(value = "operate_user")
    @Excel(name = "操作人")
    @ApiModelProperty(name = "operateUser",value= "操作人" )
    private String operateUser;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time")
    private Date createTime;


    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSn(String sn)
    {
        this.sn = sn;
    }

    public String getSn()
    {
        return sn;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setLogType(String logType)
    {
        this.logType = logType;
    }

    public String getLogType()
    {
        return logType;
    }
    public void setLogOssLink(String logOssLink)
    {
        this.logOssLink = logOssLink;
    }

    public String getLogOssLink()
    {
        return logOssLink;
    }
    public void setOperateUser(String operateUser)
    {
        this.operateUser = operateUser;
    }

    public String getOperateUser()
    {
        return operateUser;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("sn", getSn())
                .append("status", getStatus())
                .append("logType", getLogType())
                .append("logOssLink", getLogOssLink())
                .append("operateUser", getOperateUser())
                .append("createTime", getCreateTime())
                .toString();
    }
}
