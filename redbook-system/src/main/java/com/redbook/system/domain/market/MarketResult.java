package com.redbook.system.domain.market;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-02-20 10:40
 */
public class MarketResult implements Serializable {


    private static final long serialVersionUID = 1L;


    private Integer code;
    private String msg;
    private String data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
