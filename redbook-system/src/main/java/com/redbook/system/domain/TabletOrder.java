package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 硬件订单对象 tablet_order
 *
 * <AUTHOR>
 * @date 2022-11-28
 */
@ApiModel("硬件订单")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TabletOrder extends BaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户
     */
    @Excel(name = "用户")
    @ApiModelProperty(name = "userId", value = "用户")
    private Long userId;

    /**
     * 代理商
     */
    @Excel(name = "代理商")
    @ApiModelProperty(name = "aid", value = "代理商")
    private String aid;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    @ApiModelProperty(name = "indentNumber", value = "订单号")
    private String indentNumber;

    /**
     * 订购数量
     */
    @Excel(name = "订购数量")
    @ApiModelProperty(name = "orderCount", value = "订购数量")
    private Integer orderCount;

    @Excel(name = "订购型号")
    @ApiModelProperty(name = "orderModelId", value = "订购型号")
    private Long orderModelId;

    @ApiModelProperty(name = "orderModelName", value = "订购型号名称")
    private String orderModelName;
    /**
     * 物流单号
     */
    @Excel(name = "物流单号")
    @ApiModelProperty(name = "trackingNumber", value = "物流单号")
    private String trackingNumber;

    /**
     * 物流公司名
     */
    @Excel(name = "物流公司名")
    @ApiModelProperty(name = "trackingName", value = "物流公司名")
    private String trackingName;

    /**
     * 收货人
     */
    @Excel(name = "收货人")
    @ApiModelProperty(name = "reciever", value = "收货人")
    private String reciever;

    /**
     * 收货人电话
     */
    @Excel(name = "收货人电话")
    @ApiModelProperty(name = "recieverPhone", value = "收货人电话")
    private String recieverPhone;

    /**
     * 收货地址
     */
    @Excel(name = "收货地址")
    @ApiModelProperty(name = "recieverAddess", value = "收货地址")
    private String recieverAddess;

    /**
     * 1已完成 2取消（待发货0，待收货3）
     */
    @Excel(name = "1已完成 2取消", readConverterExp = "待发货0，待收货3")
    @ApiModelProperty(name = "state", value = "1已完成 2取消 0待发货 3待收货")
    private Integer state;

    /**
     * 课程款
     */
    @Excel(name = "课程款")
    @ApiModelProperty(name = "courseMoney", value = "课程款")
    private BigDecimal courseMoney;

    /**
     * 课程款类型
     */
    @Excel(name = "课程款类型")
    @ApiModelProperty(name = "courseMoneyType", value = "课程款类型")
    private Integer courseMoneyType;

    /**
     * 硬件id集合
     */
    @Excel(name = "硬件id集合")
    @ApiModelProperty(name = "tabletIds", value = "硬件id集合", example = "1,2")
    private String tabletIds;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "time", value = "时间")
    private Date time;

    /**
     *
     */
    @Excel(name = "")
    @ApiModelProperty(name = "isSended", value = "")
    private String isSended;

    /**
     * 发货时间
     */
    @Excel(name = "发货时间")
    @ApiModelProperty(name = "sendGoodsTime", value = "发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendGoodsTime;

    /**
     * 附件json格式
     */
    @Excel(name = "附件json格式")
    @ApiModelProperty(name = "extra", value = "附件json格式")
    private String extra;
    @ApiModelProperty(name = "agentName", value = "代理商名称")
    private String agentName;
    @ApiModelProperty(name = "contactPerson", value = "代理商负责人")
    private String contactPerson;
    @ApiModelProperty(name = "payInfo", value = "支付信息")
    private PayInfo payInfo;


    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getAid() {
        return aid;
    }

    public void setIndentNumber(String indentNumber) {
        this.indentNumber = indentNumber;
    }

    public String getIndentNumber() {
        return indentNumber;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingName(String trackingName) {
        this.trackingName = trackingName;
    }

    public String getTrackingName() {
        return trackingName;
    }

    public void setReciever(String reciever) {
        this.reciever = reciever;
    }

    public String getReciever() {
        return reciever;
    }

    public void setRecieverPhone(String recieverPhone) {
        this.recieverPhone = recieverPhone;
    }

    public String getRecieverPhone() {
        return recieverPhone;
    }

    public void setRecieverAddess(String recieverAddess) {
        this.recieverAddess = recieverAddess;
    }

    public String getRecieverAddess() {
        return recieverAddess;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getState() {
        return state;
    }

    public void setCourseMoney(BigDecimal courseMoney) {
        this.courseMoney = courseMoney;
    }

    public BigDecimal getCourseMoney() {
        return courseMoney;
    }

    public void setCourseMoneyType(Integer courseMoneyType) {
        this.courseMoneyType = courseMoneyType;
    }

    public Integer getCourseMoneyType() {
        return courseMoneyType;
    }

    public void setTabletIds(String tabletIds) {
        this.tabletIds = tabletIds;
    }

    public String getTabletIds() {
        return tabletIds;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Date getTime() {
        return time;
    }

    public void setIsSended(String isSended) {
        this.isSended = isSended;
    }

    public String getIsSended() {
        return isSended;
    }

    public Date getSendGoodsTime() {
        return sendGoodsTime;
    }

    public void setSendGoodsTime(Date sendGoodsTime) {
        this.sendGoodsTime = sendGoodsTime;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getExtra() {
        return extra;
    }

    public Long getOrderModelId() {
        return orderModelId;
    }

    public void setOrderModelId(Long orderModelId) {
        this.orderModelId = orderModelId;
    }

    public String getOrderModelName() {
        return orderModelName;
    }

    public void setOrderModelName(String orderModelName) {
        this.orderModelName = orderModelName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("aid", getAid())
                .append("indentNumber", getIndentNumber())
                .append("orderCount", getOrderCount())
                .append("trackingNumber", getTrackingNumber())
                .append("trackingName", getTrackingName())
                .append("reciever", getReciever())
                .append("recieverPhone", getRecieverPhone())
                .append("recieverAddess", getRecieverAddess())
                .append("state", getState())
                .append("courseMoney", getCourseMoney())
                .append("courseMoneyType", getCourseMoneyType())
                .append("tabletIds", getTabletIds())
                .append("time", getTime())
                .append("isSended", getIsSended())
                .append("sendGoodsTime", getSendGoodsTime())
                .append("extra", getExtra())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
