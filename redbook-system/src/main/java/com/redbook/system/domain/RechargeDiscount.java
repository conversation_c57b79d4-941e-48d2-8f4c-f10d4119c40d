package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


/**
 * 充值优惠对象 recharge_discount
 *
 * <AUTHOR>
 * @date 2023-10-12
 */
@ApiModel("充值优惠")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RechargeDiscount extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * 款项类型 ID
     */
    @TableId(value = "fundTypeId", type = IdType.AUTO)
    private Long fundtypeid;


    /**
     * 开始时间
     */
    @TableField(value = "startTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "starttime", value = "开始时间")
    private Date startTime;


    /**
     * 结束时间
     */
    @TableField(value = "endTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "endtime", value = "结束时间")
    private Date endTime;

    @TableField(exist = false)
    @ApiModelProperty(name = "discountConfigs", value = "充值优惠配置")
    private List<RechargeDiscountConfig> discountConfigs;


    public void setFundtypeid(Long fundtypeid) {
        this.fundtypeid = fundtypeid;
    }

    public Long getFundtypeid() {
        return fundtypeid;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<RechargeDiscountConfig> getDiscountConfigs() {
        return discountConfigs;
    }

    public void setDiscountConfigs(List<RechargeDiscountConfig> discountConfigs) {
        this.discountConfigs = discountConfigs;
    }


}
