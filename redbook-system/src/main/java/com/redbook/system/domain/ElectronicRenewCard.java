package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 电子续费卡对象 electronic_renew_card
 *
 * <AUTHOR>
 * @date 2023-10-16
 */
@ApiModel("电子续费卡")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ElectronicRenewCard extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 代理商ID
     */
    @TableField(value = "agent_id")
    @Excel(name = "代理商ID")
    @ApiModelProperty(name = "agentId", value = "代理商ID")
    private Long agentId;

    @TableField(value = "exclusive_shop_id")
    @Excel(name = "专卖店id")
    @ApiModelProperty(name = "exclusiveShopId", value = "专卖店id")
    private Integer exclusiveShopId;

    /**
     * 购买的代理商ID
     */
    @TableField(value = "buy_agent_id")
    @Excel(name = "购买的代理商ID")
    @ApiModelProperty(name = "buyAgentId", value = "购买的代理商ID")
    private Long buyAgentId;


    /**
     * 卡号
     */
    @TableField(value = "card_number")
    @Excel(name = "卡号")
    @ApiModelProperty(name = "cardNumber", value = "卡号")
    private String cardNumber;


    /**
     * 密码
     */
    @TableField(value = "password")
    @Excel(name = "密码")
    @ApiModelProperty(name = "password", value = "密码")
    private String password;


    /**
     * 续费时长
     */
    @TableField(value = "renew_time_len")
    @Excel(name = "续费时长")
    @ApiModelProperty(name = "renewTimeLen", value = "续费时长")
    private Integer renewTimeLen;


    /**
     * 购买日期
     */
    @TableField(value = "buy_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "购买日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "buyDate", value = "购买日期")
    private Date buyDate;


    /**
     * 过期日期
     */
    @TableField(value = "expire_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "expireDate", value = "过期日期")
    private Date expireDate;


    /**
     * 使用时间
     */
    @TableField(value = "usage_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(name = "usageTime", value = "使用时间")
    private Date usageTime;


    /**
     * 会员号
     */
    @TableField(value = "user_id")
    @Excel(name = "会员号")
    @ApiModelProperty(name = "userId", value = "会员号")
    private String userId;


    /**
     * 会员姓名
     */
    @TableField(value = "user_name")
    @Excel(name = "会员姓名")
    @ApiModelProperty(name = "userName", value = "会员姓名")
    private String userName;


    /**
     * 续费学段
     */
    @TableField(value = "renew_stage")
    @Excel(name = "续费学段")
    @ApiModelProperty(name = "renewStage", value = "续费学段")
    private Integer renewStage;


    /**
     * 续前日期
     */
    @TableField(value = "renew_before_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "续前日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "renewBeforeDate", value = "续前日期")
    private Date renewBeforeDate;


    /**
     * 续后日期
     */
    @TableField(value = "renew_after_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "续后日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "renewAfterDate", value = "续后日期")
    private Date renewAfterDate;
    @TableField(value = "status")
    @ApiModelProperty(name = "status", value = "状态 0 未使用 1 已使用 2已退费")
    private Integer status;
    @TableField(value = "money")
    @ApiModelProperty(name = "money", value = "金额")
    private BigDecimal money;
    //订单号
    @TableField(value = "indent_number")
    @ApiModelProperty(name = "indentNumber", value = "订单号")
    private String indentNumber;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setBuyAgentId(Long buyAgentId) {
        this.buyAgentId = buyAgentId;
    }

    public Long getBuyAgentId() {
        return buyAgentId;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPassword() {
        return password;
    }

    public void setRenewTimeLen(Integer renewTimeLen) {
        this.renewTimeLen = renewTimeLen;
    }

    public Integer getRenewTimeLen() {
        return renewTimeLen;
    }

    public void setBuyDate(Date buyDate) {
        this.buyDate = buyDate;
    }

    public Date getBuyDate() {
        return buyDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setUsageTime(Date usageTime) {
        this.usageTime = usageTime;
    }

    public Date getUsageTime() {
        return usageTime;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserName() {
        return userName;
    }

    public void setRenewStage(Integer renewStage) {
        this.renewStage = renewStage;
    }

    public Integer getRenewStage() {
        return renewStage;
    }

    public void setRenewBeforeDate(Date renewBeforeDate) {
        this.renewBeforeDate = renewBeforeDate;
    }

    public Date getRenewBeforeDate() {
        return renewBeforeDate;
    }

    public void setRenewAfterDate(Date renewAfterDate) {
        this.renewAfterDate = renewAfterDate;
    }

    public Date getRenewAfterDate() {
        return renewAfterDate;
    }

    public Integer getExclusiveShopId() {
        return exclusiveShopId;
    }

    public void setExclusiveShopId(Integer exclusiveShopId) {
        this.exclusiveShopId = exclusiveShopId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("agentId", getAgentId())
                .append("buyAgentId", getBuyAgentId())
                .append("cardNumber", getCardNumber())
                .append("password", getPassword())
                .append("renewTimeLen", getRenewTimeLen())
                .append("buyDate", getBuyDate())
                .append("expireDate", getExpireDate())
                .append("usageTime", getUsageTime())
                .append("userId", getUserId())
                .append("userName", getUserName())
                .append("renewStage", getRenewStage())
                .append("renewBeforeDate", getRenewBeforeDate())
                .append("renewAfterDate", getRenewAfterDate())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
