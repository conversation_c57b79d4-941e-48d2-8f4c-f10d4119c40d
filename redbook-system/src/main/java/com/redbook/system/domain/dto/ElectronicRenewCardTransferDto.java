package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("电子续费卡转移")
public class ElectronicRenewCardTransferDto {
    @ApiModelProperty(name = "ids",value= "电子续费卡id" )
    private List<Long> ids;
    @ApiModelProperty(value = "专卖店id")
    private Integer exclusiveShopId;
    @ApiModelProperty(name = "agentId",value= "代理商id" )
    private Long agentId;
}
