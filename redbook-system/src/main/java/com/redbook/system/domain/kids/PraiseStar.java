package com.redbook.system.domain.kids;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 夸夸星语对象 praise_star
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@ApiModel("夸夸星语")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("lollipop_growth.praise_star")
public class PraiseStar extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 主键ID (自增) */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;


    /** 夸夸星语名称 */
    @TableField(value = "name")
    @Excel(name = "夸夸星语名称")
    @ApiModelProperty(name = "name",value= "夸夸星语名称" )
    private String name;


    /** 缩略图图片路径或URL */
    @TableField(value = "thumbnail_img")
    @Excel(name = "缩略图图片路径或URL")
    @ApiModelProperty(name = "thumbnailImg",value= "缩略图图片路径或URL" )
    private String thumbnailImg;


    /** 效果图路径 */
    @TableField(value = "effect_img")
    @Excel(name = "效果图路径")
    @ApiModelProperty(name = "effectImg",value= "效果图路径" )
    private String effectImg;


    /** 描述信息 */
    @TableField(value = "description")
    @Excel(name = "描述信息")
    @ApiModelProperty(name = "description",value= "描述信息" )
    private String description;


    /** 状态 (例如: 0=禁用/下架, 1=启用/上架) */
    @TableField(value = "status")
    @Excel(name = "状态 (例如: 0=禁用/下架, 1=启用/上架)")
    @ApiModelProperty(name = "status",value= "状态 (例如: 0=禁用/下架, 1=启用/上架)" )
    private String status;


    /** 排序值 (值越小通常越靠前) */
    @TableField(value = "sort")
    @Excel(name = "排序值 (值越小通常越靠前)")
    @ApiModelProperty(name = "sort",value= "排序值 (值越小通常越靠前)" )
    private Long sort;


    /** 创建时间戳 */
    @TableField(value = "ctime")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间戳", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "ctime",value= "创建时间戳" )
    private Date ctime;


    /** 最后更新时间戳 */
    @TableField(value = "utime")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后更新时间戳", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "utime",value= "最后更新时间戳" )
    private Date utime;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setThumbnailImg(String thumbnailImg) 
    {
        this.thumbnailImg = thumbnailImg;
    }

    public String getThumbnailImg() 
    {
        return thumbnailImg;
    }
    public void setEffectImg(String effectImg) 
    {
        this.effectImg = effectImg;
    }

    public String getEffectImg() 
    {
        return effectImg;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setSort(Long sort) 
    {
        this.sort = sort;
    }

    public Long getSort() 
    {
        return sort;
    }
    public void setCtime(Date ctime) 
    {
        this.ctime = ctime;
    }

    public Date getCtime() 
    {
        return ctime;
    }
    public void setUtime(Date utime) 
    {
        this.utime = utime;
    }

    public Date getUtime() 
    {
        return utime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("thumbnailImg", getThumbnailImg())
            .append("effectImg", getEffectImg())
            .append("description", getDescription())
            .append("status", getStatus())
            .append("sort", getSort())
            .append("ctime", getCtime())
            .append("utime", getUtime())
            .toString();
    }
}
