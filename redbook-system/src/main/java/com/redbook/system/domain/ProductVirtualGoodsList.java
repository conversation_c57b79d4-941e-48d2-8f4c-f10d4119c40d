package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;


/**
 * 虚拟商品，例如：微信红包封面对象 product_virtual_goods_list
 * 
 * <AUTHOR>
 * @date 2024-01-30
 */
@ApiModel("虚拟商品，例如：微信红包封面")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProductVirtualGoodsList extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /** 商品id */
    @TableField(value = "product_id")
//    @Excel(name = "商品id")
    @ApiModelProperty(name = "productId",value= "商品id" )
    private Long productId;


    /** 虚拟商品信息 */
    @TableField(value = "goods_info")
    @Excel(name = "虚拟商品信息")
    @ApiModelProperty(name = "goodsInfo",value= "虚拟商品信息" )
    private String goodsInfo;


    /** 0库存未出售，1已出售 */
    @TableField(value = "status")
    @Excel(name = "状态（库存/出售）",readConverterExp="0=库存,1=已出售")
    @ApiModelProperty(name = "status",value= "0库存未出售，1已出售" )
    private Integer status;


    /** 订单号 */
    @TableField(value = "order_no")
    @Excel(name = "已出售的显示对应订单号")
    @ApiModelProperty(name = "orderNo",value= "订单号" )
    private String orderNo;


    /** 导入时间 */
    @TableField(value = "import_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "导入时间", width = 30, dateFormat = "yyyy-MM-dd hh:mm:ss")
    @ApiModelProperty(name = "importTime",value= "导入时间" )
    private Date importTime;


    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    public void setGoodsInfo(String goodsInfo) 
    {
        this.goodsInfo = goodsInfo;
    }

    public String getGoodsInfo() 
    {
        return goodsInfo;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setImportTime(Date importTime) 
    {
        this.importTime = importTime;
    }

    public Date getImportTime() 
    {
        return importTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productId", getProductId())
            .append("goodsInfo", getGoodsInfo())
            .append("status", getStatus())
            .append("orderNo", getOrderNo())
            .append("importTime", getImportTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
