package com.redbook.system.domain.dto;

import com.redbook.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@ApiModel(value = "修改代理商")
@AllArgsConstructor
@NoArgsConstructor
public class UpdateAgentDto {
    @ApiModelProperty(name = "id", value = "代理商id")
    private Long id;
    /**
     * 代理商登记 a b c等
     */
    @ApiModelProperty(name = "level", value = "代理商登记 a b c等")
    private String level;

    /**
     * 合同级别
     */
    @Excel(name = "合同级别")
    private Long agreementLevel;

    @ApiModelProperty(name = "areaId", value = "区域id")
    @NotNull(message = "区域id不能为空")
    private Long areaId;

    @ApiModelProperty(name = "alias", value = "别名")
    private String alias;
    /**
     * 签约类型 1公司 2个人
     */
    @ApiModelProperty(name = "type", value = "签约类型 1公司 2个人")
    private String type;

    /**
     * 公司名称
     */
    @ApiModelProperty(name = "companyName", value = "公司名称")
    private String companyName;

    /**
     * 公司代码
     */
    @ApiModelProperty(name = "taxCode", value = "公司代码")
    private String taxCode;

    /**
     * 个人签约名称
     */
    @ApiModelProperty(name = "personName", value = "个人签约名称")
    private String personName;

    /**
     * 个人签约手机号
     */
    @ApiModelProperty(name = "phone", value = "个人签约手机号")
    private String phone;

    /**
     * 身份证
     */
    @ApiModelProperty(name = "idCard", value = "身份证")
    private String idCard;
    /* 签约时间
     */
    @ApiModelProperty(name = "agreementStart", value = "签约时间")
    private String agreementStart;
    /**
     * 签约到期时间
     */
    @ApiModelProperty(name = "agreementEnd", value = "签约到期时间")
    private String agreementEnd;
    /**
     *
     * 账号到期时间
     */
    @ApiModelProperty(name = "accountEnd", value = "账号到期时间")
    private String accountEnd;


    @ApiModelProperty(name = "contactPerson", value = "负责人")
    private String contactPerson;

    @ApiModelProperty(name = "contactPersonPhone", value = "负责人电话")
    private String contactPersonPhone;

    @ApiModelProperty(name = "extraJson", value = "附件上传后的json")
    private String extraJson;
}
