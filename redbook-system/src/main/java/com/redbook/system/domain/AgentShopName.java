package com.redbook.system.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@ApiModel("代理商活动")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AgentShopName
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(name = "name",value= "代理商名称" )
    private String agentName;


    @ApiModelProperty(name = "name",value= "专卖店名称" )
    private String shopName;

}
