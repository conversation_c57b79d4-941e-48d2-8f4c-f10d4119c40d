package com.redbook.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RedBookDailyData implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "时间")
    String minuteStr;
    @ApiModelProperty(value = "在线数")
    Long onLineNum;
    @ApiModelProperty(value = "登录数")
    Long loginNum;
}