package com.redbook.system.domain.dto;

import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "代理商交易统计", description = "代理商交易统计")
@Builder
public class TransactionStatisticsDto  extends BaseEntity {
    @ApiModelProperty(name = "transactionStartDate",value = "交易开始日期")
    private String transactionStartDate;
    @ApiModelProperty(name = "transactionEndDate",value = "交易截至日期")
    private String transactionEndDate;
}
