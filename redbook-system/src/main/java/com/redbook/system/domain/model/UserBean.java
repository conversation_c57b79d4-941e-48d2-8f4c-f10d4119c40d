//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.redbook.system.domain.model;

import com.redbook.system.domain.UserInfo;
import com.redbook.system.domain.UserOtherInfoBean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UserBean implements Serializable {
	private static final long serialVersionUID = 2864013004286880613L;
	private Integer id;
	private String userId;
	private String username;
	private String nickName;
//	private String studentNo;
	private String openId;
	private String password;
	private String passwordText;
	private Integer sex;
	private Date birthday;
	private String idCard;
	private int userType;
	private Date registerDate;
	private Date accessDate;
	private String timeLen;
	private Date lastAccessDate;
	private String email;
	private String phone;
	private String mobilePhone;
	private boolean mobilePhoneBinding = false;
	private String qq;
	private String aid;
	private Object agent;
	private int classId;
	private Object classBean;
	private String signature;
//	private String isDelete;
	private String headImageUrl;
//	private String headPendantImg;
//	private String indexSkinImg;
	private List<Integer> purchaseVirtualGoodsIdList = null;
//	private String teachId;
//	private String purchase;
	private List<Integer> purchaseProgramIdList = new ArrayList();
	private List<Integer> purchaseSeriesIdList = new ArrayList();
	private List<Integer> purchaseParentIdList = new ArrayList();
//	private int reviewType;
//	private String reviewModule;
	private List<Integer> reviewModuleList = new ArrayList();
//	private Integer grade;
//	private String gradeClass;
//	private Float lastScore;
//	private String address;
//	private String referrerId;
//	private Integer isAward;
//	private Integer credits;
//	private Integer integral;
	private String sessionId;
//	private String school;
//	private String effectQuizFinish;
//	private Integer needStrengthen;
//	private Integer popularity;
	private int creditsLevel = 1;
	private int nextLevelCredits = 100;
	private int weekMedal = -1;
	private boolean weekMedalUpgradeTip = false;
//	private Integer reissueCard;
	private List<String> programQualificationNewlyGetList = new ArrayList();
	private List<String> engineGearChangeList = new ArrayList();
	private String levelQualification = "";
	private String reissueCardFlag = "";
//	private String commedityCode = null;
	private String browserUserAgent = "";
	private String serverName = "";
	private UserOtherInfoBean otherInfoBean = null;
	private Boolean isRedBookLogin = false;
	private final String tableSuffix = null;
	private String commedityCode = null;
	private String purchase;
	public String getPurchase() {
		return this.purchase;
	}
	private String address;
	private Integer integral;
	public String getAddress() {
		return this.address;
	}
	public Integer getIntegral() {
		return this.integral;
	}

	private UserInfo redBookUser;

	public void setIntegral(Integer integral) {
		this.integral = integral;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public void setPurchase(String purchase) {
		if (purchase.indexOf("vip,") != -1) {
			this.purchase = "vip,";
		} else {
			this.purchase = purchase;
		}

	}
	public UserBean() {
	}

	public String getReissueCardFlag() {
		return this.reissueCardFlag;
	}

	public void setReissueCardFlag(String reissueCardFlag) {
		this.reissueCardFlag = reissueCardFlag;
	}

	public String getLevelQualification() {
		return this.levelQualification;
	}

	public void setLevelQualification(String levelQualification) {
		this.levelQualification = levelQualification;
	}

	public List<String> getProgramQualificationNewlyGetList() {
		return this.programQualificationNewlyGetList;
	}

	public void setProgramQualificationNewlyGetList(List<String> programQualificationNewlyGetList) {
		this.programQualificationNewlyGetList = programQualificationNewlyGetList;
	}

	public void addProgramQualificationNewlyGetInfo(String str) {
		this.programQualificationNewlyGetList.add(str);
	}

	public List<String> getEngineGearChangeList() {
		return this.engineGearChangeList;
	}

	public void setEngineGearChangeList(List<String> engineGearChangeList) {
		this.engineGearChangeList = engineGearChangeList;
	}

	public void addEngineGearChangeList(String str) {
		this.engineGearChangeList.add(str);
	}

	public static long getSerialversionuid() {
		return 2864013004286880613L;
	}

//	public Integer getReissueCard() {
//		return this.reissueCard;
//	}
//
//	public void setReissueCard(Integer reissueCard) {
//		this.reissueCard = reissueCard;
//	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
		if (id != null) {
		}

	}

	public String getUserId() {
		return this.userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUsername() {
		return this.username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getNickName() {
		return this.nickName;
	}

	public void setNickName(String nickName) {
		this.nickName = nickName;
	}

//	public String getStudentNo() {
//		return this.studentNo;
//	}

//	public void setStudentNo(String studentNo) {
//		this.studentNo = studentNo;
//	}


	public Object getAgent() {
		return agent;
	}

	public void setAgent(Object agent) {
		this.agent = agent;
	}

	public Object getClassBean() {
		return classBean;
	}

	public void setClassBean(Object classBean) {
		this.classBean = classBean;
	}

	public UserInfo getRedBookUser() {
		return redBookUser;
	}

	public void setRedBookUser(UserInfo redBookUser) {
		this.redBookUser = redBookUser;
	}

	public String getOpenId() {
		return this.openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getPassword() {
		return this.password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getPasswordText() {
		return this.passwordText;
	}

	public void setPasswordText(String passwordText) {
		this.passwordText = passwordText;
	}

	public Integer getSex() {
		return this.sex;
	}

	public void setSex(Integer sex) {
		this.sex = sex;
	}

	public Date getBirthday() {
		return this.birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public String getIdCard() {
		return this.idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public int getUserType() {
		return this.userType;
	}

	public void setUserType(int userType) {
		this.userType = userType;
	}

	public Date getRegisterDate() {
		return this.registerDate;
	}

	public void setRegisterDate(Date registerDate) {
		this.registerDate = registerDate;
	}

	public Date getAccessDate() {
		return this.accessDate;
	}

	public void setAccessDate(Date accessDate) {
		this.accessDate = accessDate;
	}

	public String getTimeLen() {
		return this.timeLen;
	}

	public void setTimeLen(String timeLen) {
		this.timeLen = timeLen;
	}

	public Date getLastAccessDate() {
		return this.lastAccessDate;
	}

	public void setLastAccessDate(Date lastAccessDate) {
		this.lastAccessDate = lastAccessDate;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getMobilePhone() {
		return this.mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getQq() {
		return this.qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}


	public String getAid() {
		return this.aid;
	}

	public void setAid(String aid) {
		this.aid = aid;
	}

	public int getClassId() {
		return this.classId;
	}

	public void setClassId(int classId) {
		this.classId = classId;
	}



	public String getSignature() {
		return this.signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

//	public String getIsDelete() {
//		return this.isDelete;
//	}
//
//	public void setIsDelete(String isDelete) {
//		this.isDelete = isDelete;
//	}

	public String getHeadImageUrl() {
		return this.headImageUrl;
	}

	public void setHeadImageUrl(String headImageUrl) {
		this.headImageUrl = headImageUrl;
	}

//	public String getHeadPendantImg() {
//		return this.headPendantImg;
//	}
//
//	public void setHeadPendantImg(String headPendantImg) {
//		this.headPendantImg = headPendantImg;
//	}

//	public String getIndexSkinImg() {
//		return this.indexSkinImg;
//	}
//
//	public void setIndexSkinImg(String indexSkinImg) {
//		this.indexSkinImg = indexSkinImg;
//	}

	public List<Integer> getPurchaseVirtualGoodsIdList() {
		return this.purchaseVirtualGoodsIdList;
	}

	public void setPurchaseVirtualGoodsIdList(List<Integer> purchaseVirtualGoodsIdList) {
		this.purchaseVirtualGoodsIdList = purchaseVirtualGoodsIdList;
	}

//	public String getTeachId() {
//		return this.teachId;
//	}
//
//	public void setTeachId(String teachId) {
//		this.teachId = teachId;
//	}

	public String getPhone() {
		return this.phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

//	public String getPurchase() {
//		return this.purchase;
//	}
//
//	public void setPurchase(String purchase) {
//		if (purchase.indexOf("vip,") != -1) {
//			this.purchase = "vip,";
//		} else {
//			this.purchase = purchase;
//		}
//
//	}

	public List<Integer> getPurchaseProgramIdList() {
		return this.purchaseProgramIdList;
	}

	public void setPurchaseProgramIdList(List<Integer> purchaseProgramIdList) {
		this.purchaseProgramIdList = purchaseProgramIdList;
	}

	public List<Integer> getPurchaseSeriesIdList() {
		return this.purchaseSeriesIdList;
	}

	public void setPurchaseSeriesIdList(List<Integer> purchaseSeriesIdList) {
		this.purchaseSeriesIdList = purchaseSeriesIdList;
	}

	public List<Integer> getPurchaseParentIdList() {
		return this.purchaseParentIdList;
	}

	public void setPurchaseParentIdList(List<Integer> purchaseParentIdList) {
		this.purchaseParentIdList = purchaseParentIdList;
	}

//	public int getReviewType() {
//		return this.reviewType;
//	}
//
//	public void setReviewType(int reviewType) {
//		this.reviewType = reviewType;
//	}


//	}

	public List<Integer> getReviewModuleList() {
		return this.reviewModuleList;
	}

	public void setReviewModuleList(List<Integer> reviewModuleList) {
		this.reviewModuleList = reviewModuleList;
	}

//	public String getReferrerId() {
//		return this.referrerId;
//	}
//
//	public void setReferrerId(String referrerId) {
//		this.referrerId = referrerId;
//	}

//	public Integer getIsAward() {
//		return this.isAward;
//	}
//
//	public void setIsAward(Integer isAward) {
//		this.isAward = isAward;
//	}

//	public Integer getCredits() {
//		return this.credits;
//	}
//
//	public void setCredits(Integer credits) {
//		this.credits = credits;
//		if (0 <= this.credits && this.credits <= 99) {
//			this.creditsLevel = 1;
//			this.nextLevelCredits = 100;
//		} else if (100 <= this.credits && this.credits <= 299) {
//			this.creditsLevel = 2;
//			this.nextLevelCredits = 300;
//		} else if (300 <= this.credits && this.credits <= 599) {
//			this.creditsLevel = 3;
//			this.nextLevelCredits = 600;
//		} else if (600 <= this.credits && this.credits <= 999) {
//			this.creditsLevel = 4;
//			this.nextLevelCredits = 1000;
//		} else if (1000 <= this.credits && this.credits <= 1499) {
//			this.creditsLevel = 5;
//			this.nextLevelCredits = 1500;
//		} else if (1500 <= this.credits && this.credits <= 1999) {
//			this.creditsLevel = 6;
//			this.nextLevelCredits = 2000;
//		} else if (2000 <= this.credits && this.credits <= 2499) {
//			this.creditsLevel = 7;
//			this.nextLevelCredits = 2500;
//		} else if (2500 <= this.credits && this.credits <= 2999) {
//			this.creditsLevel = 8;
//			this.nextLevelCredits = 3000;
//		} else if (3000 <= this.credits && this.credits <= 3499) {
//			this.creditsLevel = 9;
//			this.nextLevelCredits = 3500;
//		} else if (3500 <= this.credits && this.credits <= 3999) {
//			this.creditsLevel = 10;
//			this.nextLevelCredits = 4000;
//		} else {
//			Integer number;
//			if (this.credits >= 4000 && this.credits <= 13999) {
//				number = (this.credits - 4000) / 1000;
//				this.creditsLevel = 11 + number;
//				this.nextLevelCredits = 5000 + 1000 * number;
//			} else if (this.credits >= 14000) {
//				number = (this.credits - 14000) / 2000;
//				this.creditsLevel = 21 + number;
//				this.nextLevelCredits = 16000 + 2000 * number;
//			}
//		}
//
//	}

//	public Integer getIntegral() {
//		return this.integral;
//	}

//	public void setIntegral(Integer integral) {
//		this.integral = integral;
//	}

	public String getSessionId() {
		return this.sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
//
//	public String getSchool() {
//		return this.school;
//	}
//
//	public void setSchool(String school) {
//		this.school = school;
//	}

//	public String getEffectQuizFinish() {
//		return this.effectQuizFinish;
//	}
//
//	public void setEffectQuizFinish(String effectQuizFinish) {
//		this.effectQuizFinish = effectQuizFinish;
//	}

//	public Integer getNeedStrengthen() {
//		return this.needStrengthen;
//	}
//
//	public void setNeedStrengthen(Integer needStrengthen) {
//		this.needStrengthen = needStrengthen;
//	}

	public int getCreditsLevel() {
		return this.creditsLevel;
	}

	public void setCreditsLevel(int creditsLevel) {
		this.creditsLevel = creditsLevel;
	}

	public int getNextLevelCredits() {
		return this.nextLevelCredits;
	}

	public void setNextLevelCredits(int nextLevelCredits) {
		this.nextLevelCredits = nextLevelCredits;
	}

	public int getWeekMedal() {
		return this.weekMedal;
	}

	public int setWeekMedal(int weekIntegral) {
		int nowWeekMedal = 0;
		if (weekIntegral < 100) {
			nowWeekMedal = 0;
		} else if (weekIntegral >= 100 && weekIntegral < 200) {
			nowWeekMedal = 1;
		} else if (weekIntegral >= 200 && weekIntegral < 300) {
			nowWeekMedal = 2;
		} else if (weekIntegral >= 300 && weekIntegral < 600) {
			nowWeekMedal = 3;
		} else if (weekIntegral >= 600 && weekIntegral < 1000) {
			nowWeekMedal = 4;
		} else if (weekIntegral >= 1000 && weekIntegral < 1500) {
			nowWeekMedal = 5;
		} else if (weekIntegral >= 1500 && weekIntegral < 2000) {
			nowWeekMedal = 6;
		} else if (weekIntegral >= 2000) {
			nowWeekMedal = 7;
		}

		if (this.weekMedal != -1 && this.weekMedal != nowWeekMedal) {
			this.weekMedalUpgradeTip = true;
		}

		this.weekMedal = nowWeekMedal;
		return this.weekMedal;
	}

	public boolean isWeekMedalUpgradeTip() {
		return this.weekMedalUpgradeTip;
	}

	public void setWeekMedalUpgradeTip(boolean weekMedalUpgradeTip) {
		this.weekMedalUpgradeTip = weekMedalUpgradeTip;
	}

//	public Integer getPopularity() {
//		return this.popularity;
//	}
//
//	public void setPopularity(Integer popularity) {
//		this.popularity = popularity;
//	}
//
//	public String getCommedityCode() {
//		return this.commedityCode;
//	}
//
//	public void setCommedityCode(String commedityCode) {
//		this.commedityCode = commedityCode;
//	}

	public boolean isMobilePhoneBinding() {
		return this.mobilePhoneBinding;
	}

	public void setMobilePhoneBinding(boolean mobilePhoneBinding) {
		this.mobilePhoneBinding = mobilePhoneBinding;
	}

	public UserOtherInfoBean getOtherInfoBean() {
		return this.otherInfoBean;
	}

	public void setOtherInfoBean(UserOtherInfoBean otherInfoBean) {
		this.otherInfoBean = otherInfoBean;
	}

	public String getBrowserUserAgent() {
		return this.browserUserAgent;
	}

	public void setBrowserUserAgent(String browserUserAgent) {
		this.browserUserAgent = browserUserAgent;
	}

	public String getServerName() {
		return this.serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	public Boolean getRedBookLogin() {
		return this.isRedBookLogin;
	}

	public void setRedBookLogin(Boolean redBookLogin) {
		this.isRedBookLogin = redBookLogin;
	}


	public String getTableSuffix() {
		return this.tableSuffix;
	}

	public String getCommedityCode() {
		return commedityCode;
	}

	public void setCommedityCode(String commedityCode) {
		this.commedityCode = commedityCode;
	}
}
