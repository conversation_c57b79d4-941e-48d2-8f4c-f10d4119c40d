package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 线索转介绍对象 biz_clue_referral
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
@ApiModel("线索转介绍")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BizClueReferral extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 转介绍id */
    @TableId(value = "clue_referral_id", type = IdType.AUTO)
    private Long clueReferralId;


    /** 当前人线索表id */
    @TableField(value = "clue_id")
    @Excel(name = "当前人线索表id")
    @ApiModelProperty(name = "clueId",value= "当前人线索表id" )
    private Long clueId;


    /** 邀约来的人线索表id */
    @TableField(value = "guest_clue_id")
    @Excel(name = "邀约来的人线索表id")
    @ApiModelProperty(name = "guestClueId",value= "邀约来的人线索表id" )
    private Long guestClueId;




    /** 创建者id */
    @TableField(value = "create_by_id")
    @Excel(name = "创建者id")
    @ApiModelProperty(name = "createById",value= "创建者id" )
    private String createById;

}
