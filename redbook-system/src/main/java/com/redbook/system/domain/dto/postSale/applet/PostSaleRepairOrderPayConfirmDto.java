package com.redbook.system.domain.dto.postSale.applet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel("寄修单支付确认请求包")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PostSaleRepairOrderPayConfirmDto implements Serializable {
    @ApiModelProperty(name = "orderId", value = "支付的寄修单id")
    private Integer orderId;
    @ApiModelProperty(name = "itemIdList", value = "支付的寄修单条目id列表")
    private List<Integer> itemIdList;
    @ApiModelProperty(name = "totalFee", value = "支付的总金额")
    private BigDecimal totalFee;
    private  String userId;
    private int userType;
    private Date payTime;

}
