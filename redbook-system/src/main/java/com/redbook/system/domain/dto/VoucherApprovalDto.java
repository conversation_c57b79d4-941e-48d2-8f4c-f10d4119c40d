package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@ApiModel("代金券审批")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VoucherApprovalDto {
    @ApiModelProperty(name = "agentIds",value = "代理商列表")
    private List< VoucherApprovalAgentDto>agents;
    @ApiModelProperty(name ="voucherType",value = "代金券类型")
    private String voucherType;
    @ApiModelProperty(name = "remark",value = "备注")
    private String remark;
    @ApiModelProperty(name = "extra",value = "附件")
    private String extra;

}

