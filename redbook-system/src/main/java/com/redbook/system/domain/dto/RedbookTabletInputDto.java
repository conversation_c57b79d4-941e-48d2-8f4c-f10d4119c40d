package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
@Data
@ApiModel(reference = "RedbookTabletInputVo")
@AllArgsConstructor
@NoArgsConstructor
public class RedbookTabletInputDto {

    @ApiModelProperty(name = "supplyId", value = "供应商id")
    @NotNull(message = "供应商不能为空")
    @Min(1)
    private Integer supplyId;

    @ApiModelProperty(name = "modelId", value = "型号id")
    @NotNull(message = "型号不能为空")
    @Min(1)
    private Integer modelId;

    @ApiModelProperty(name = "batchId", value = "批次id")
    @NotNull(message = "批次不能为空")
    @Min(1)
    private Integer batchId;

    @ApiModelProperty(name = "file", value = "文件")
    private MultipartFile file;

}
