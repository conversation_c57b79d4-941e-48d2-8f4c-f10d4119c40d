package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@ApiModel("建店申请审批")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalCreateExclusiveShopDto {
    @ApiModelProperty("所属代理商ID" )
    private Long agentId;

    @ApiModelProperty("级别，1：一级/直营店；2：二级/合作店" )
    private Integer level;

    @ApiModelProperty("专卖店名称" )
    private String name;

    @ApiModelProperty("专卖店地址" )
    private String address;

    @ApiModelProperty( "门头照片地址" )
    private String photoFrontDoor;

    @ApiModelProperty( "前台照片地址" )
    private String photoFrontDesk;

    @ApiModelProperty( "其他照片地址，多个用|分割，限制最多5个" )
    private String photoOthers;

    @ApiModelProperty( "合同文件地址，多个用|分割，限制最多5个" )
    private String contract;

    @ApiModelProperty( "开店日期" )
    private Date openDate;

    @ApiModelProperty( "到期日期" )
    private Date endDate;

    @ApiModelProperty( "店铺面积" )
    private Integer squareNumber;
    @ApiModelProperty( "预计开店日期" )
    private Date expectOpenDate;
    @ApiModelProperty( "装修前照片地址" )
    private String photoFinishFormer;
    @ApiModelProperty( "证书照片地址" )
    private String photoCertificate;

    @ApiModelProperty( "预生成的专卖店编码，前端忽略" )
    private String code;

    @ApiModelProperty( "预生成的专卖店装修编码，前端忽略" )
    private String esCode;

}

