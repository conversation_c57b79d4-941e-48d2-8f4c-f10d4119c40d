package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;


/**
 * 动态价格优惠档位对象 redbook_member_rebates
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("动态价格优惠档位")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RedbookMemberRebates extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** $column.columnComment */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /** 销量范围下限（包含） */
    @TableField(value = "sales_range_min")
    @Excel(name = "销量范围下限", readConverterExp = "包=含")
    @ApiModelProperty(name = "salesRangeMin",value= "销量范围下限" )
    private Long salesRangeMin;


    /** 销量范围上限（包含） */
    @TableField(value = "sales_range_max")
    @Excel(name = "销量范围上限", readConverterExp = "包=含")
    @ApiModelProperty(name = "salesRangeMax",value= "销量范围上限" )
    private Long salesRangeMax;


    /** 动态价格优惠比例（%） */
    @TableField(value = "rebate_percentage")
    @Excel(name = "动态价格优惠比例", readConverterExp = "%=")
    @ApiModelProperty(name = "rebatePercentage",value= "动态价格优惠比例" )
    private BigDecimal rebatePercentage;


    /** 动态价格优惠后的价格（元） */
    @TableField(value = "rebate_price")
    @Excel(name = "动态价格优惠后的价格", readConverterExp = "元=")
    @ApiModelProperty(name = "rebatePrice",value= "动态价格优惠后的价格" )
    private BigDecimal rebatePrice;


    /** 档位名称（如P13） */
    @TableField(value = "level_name")
    @Excel(name = "档位名称", readConverterExp = "如=P13")
    @ApiModelProperty(name = "levelName",value= "档位名称" )
    private String levelName;



    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSalesRangeMin(Long salesRangeMin) 
    {
        this.salesRangeMin = salesRangeMin;
    }

    public Long getSalesRangeMin() 
    {
        return salesRangeMin;
    }
    public void setSalesRangeMax(Long salesRangeMax) 
    {
        this.salesRangeMax = salesRangeMax;
    }

    public Long getSalesRangeMax() 
    {
        return salesRangeMax;
    }
    public void setRebatePercentage(BigDecimal rebatePercentage) 
    {
        this.rebatePercentage = rebatePercentage;
    }

    public BigDecimal getRebatePercentage() 
    {
        return rebatePercentage;
    }
    public void setRebatePrice(BigDecimal rebatePrice) 
    {
        this.rebatePrice = rebatePrice;
    }

    public BigDecimal getRebatePrice() 
    {
        return rebatePrice;
    }
    public void setLevelName(String levelName) 
    {
        this.levelName = levelName;
    }

    public String getLevelName() 
    {
        return levelName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("salesRangeMin", getSalesRangeMin())
            .append("salesRangeMax", getSalesRangeMax())
            .append("rebatePercentage", getRebatePercentage())
            .append("rebatePrice", getRebatePrice())
            .append("levelName", getLevelName())
            .toString();
    }
}
