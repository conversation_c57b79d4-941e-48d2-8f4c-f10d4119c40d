package com.redbook.system.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel(value = "口语打卡争霸赛抽奖数据列表请求包")
@AllArgsConstructor
@NoArgsConstructor
public class ActivityUserDrawRecordListDto {
    @ApiParam(name = "活动基础id", required = true)
    private Integer activityBaseId;

    @ApiParam(name = "活动内容id", required = true)
    private Integer activityContentId;

    @ApiParam(name = "用户id", required = true)
    private String userId;
}
