package com.redbook.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.redbook.common.annotation.Excel;
import com.redbook.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 代理商钱包账户对象 agent_account
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@ApiModel("代理商钱包账户")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AgentAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 代理商id
     */
    @Excel(name = "代理商id")
    @ApiModelProperty(name = "agentId", value = "代理商id")
    @TableField(value = "agent_id")
    private Long agentId;

    /**
     * 支付密码
     */
    @Excel(name = "支付密码")
    @ApiModelProperty(name = "payPassword", value = "支付密码")
    @TableField(value = "pay_password")
    private String payPassword;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    @ApiModelProperty(name = "phone", value = "手机号")
    @TableField(value = "phone")
    private String phone;

    /**
     * 手机是否验证过 0否 1是
     */
    @Excel(name = "手机是否验证过 0否 1是")
    @ApiModelProperty(name = "validated", value = "手机是否验证过 0否 1是")
    @TableField(value = "validated")
    private Integer validated;

    /**
     * 续约款
     */
    @Excel(name = "续约款")
    @ApiModelProperty(name = "renewMoney", value = "续约款")
    @TableField(value = "renew_money")
    private BigDecimal renewMoney;

    /**
     * 代金券
     */
    @Excel(name = "期末代金券")
    @ApiModelProperty(name = "voucherMoney", value = "期末代金券")
    @TableField(value = "voucher_money")
    private BigDecimal voucherMoney;
    /**
     * 保证金
     */
    @Excel(name = "保证金")
    @ApiModelProperty(name = "depositMoney", value = "保证金")
    @TableField(value = "deposit_money")
    private BigDecimal depositMoney;
    /**
     * 初始化状态 0未初始化 1已初始化
     */
    @Excel(name = "初始化状态 0未初始化 1已初始化")
    @ApiModelProperty(name = "state", value = "初始化状态 0未初始化 1已初始化")
    @TableField(value = "state")
    private String state;

    @ApiModelProperty("返佣开启状态 0未返佣 1已返佣")
    @Excel(name = "返佣开启状态")
    @TableField(value = "rebate_status")
    private Boolean rebateStatus;
    /**
     * 硬件款
     */
    @Excel(name = "硬件款")
    @ApiModelProperty(name = "hardwareMoney", value = "硬件款")
    @TableField(value = "hardware_money")
    private BigDecimal hardwareMoney;
    /**
     * 会员款
     */
    @Excel(name = "期末会员款")
    @ApiModelProperty(name = "memberMoney", value = "期末会员款")
    @TableField(value = "member_money")
    private BigDecimal memberMoney;

    @Excel(name = "期初代金券")
    @ApiModelProperty(name = "voucherMoney", value = "期初代金券")
    @TableField(value = "new_voucher_money")
    private BigDecimal newVoucherMoney;

    @Excel(name = "期初会员款")
    @ApiModelProperty(name = "new_member_money", value = "期初会员款")
    @TableField(value = "new_member_money")
    private BigDecimal newMemberMoney;

    @Excel(name = "汇总")
    @ApiModelProperty(name = "totalMoney", value = "汇总")
    @TableField(value = "total_money")
    private BigDecimal totalMoney;

    @ApiModelProperty("经理姓名列表 用“，”分割")
    @TableField(exist = false)
    private String managerNames;
    @ApiModelProperty("经营状态 0正常 1退盟 2删除")
    @TableField(exist = false)
    private Long status;
    @ApiModelProperty(value = "负责人姓名")
    @TableField(exist = false)
    private String contactPersonName;
    @ApiModelProperty("代理商分级")
    @TableField(exist = false)
    private String level;

    public BigDecimal getTotalMoney() {
        if (memberMoney == null) return new BigDecimal(0);
        //返回所有款项总和
        return new BigDecimal(0).add(renewMoney).add(voucherMoney).add(depositMoney).add(hardwareMoney).add(memberMoney).add(newVoucherMoney).add(newMemberMoney);
    }
}
