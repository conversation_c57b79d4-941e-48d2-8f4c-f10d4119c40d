package com.redbook.system.service;

import com.redbook.system.domain.RedbookMemberPrices;

import java.util.List;

/**
 * 会员续费价格Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IRedbookMemberPricesService 
{
    /**
     * 查询会员续费价格
     * 
     * @param id 会员续费价格主键
     * @return 会员续费价格
     */
     RedbookMemberPrices selectRedbookMemberPricesById(Long id);

    /**
     * 查询会员续费价格列表
     * 
     * @param redbookMemberPrices 会员续费价格
     * @return 会员续费价格集合
     */
     List<RedbookMemberPrices> selectRedbookMemberPricesList(RedbookMemberPrices redbookMemberPrices);

    /**
     * 新增会员续费价格
     * 
     * @param redbookMemberPrices 会员续费价格
     * @return 结果
     */
     int insertRedbookMemberPrices(RedbookMemberPrices redbookMemberPrices);

    /**
     * 修改会员续费价格
     * 
     * @param redbookMemberPrices 会员续费价格
     * @return 结果
     */
     int updateRedbookMemberPrices(RedbookMemberPrices redbookMemberPrices);

    /**
     * 批量删除会员续费价格
     * 
     * @param ids 需要删除的会员续费价格主键集合
     * @return 结果
     */
     int deleteRedbookMemberPricesByIds(Long[] ids);

    /**
     * 删除会员续费价格信息
     * 
     * @param id 会员续费价格主键
     * @return 结果
     */
     int deleteRedbookMemberPricesById(Long id);
}
