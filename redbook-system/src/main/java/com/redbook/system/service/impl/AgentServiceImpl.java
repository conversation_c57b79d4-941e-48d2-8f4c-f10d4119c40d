package com.redbook.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.core.domain.entity.AgentInfo;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.domain.entity.SysUser;
import com.redbook.common.core.domain.model.LoginUser;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.IdentityGenerator;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.TimeUtils;
import com.redbook.common.utils.sign.Md5Utils;
import com.redbook.system.domain.*;
import com.redbook.system.domain.dto.*;
import com.redbook.system.domain.vo.*;
import com.redbook.system.mapper.*;
import com.redbook.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 代理商Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@Service
@Transactional
public class AgentServiceImpl extends ServiceImpl<AgentMapper, Agent> implements IAgentService {
    @Autowired
    private AgentMapper agentMapper;
    @Autowired
    private AgentMappingMapper agentMappingMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    @Lazy
    private ISysUserService sysUserService;
    @Autowired
    private IAgentOperLogService agentOperLogService;
    @Autowired
    private IAreaService areaService;
    @Autowired
    private AgentAccountMapper agentAccountMapper;
    @Autowired
    IAgentAccountService agentAccountService;
    @Autowired
    IRedbookTabletListService redbookTabletListService;
    @Autowired
    IMemberRenewDao memberRenewDao;
    @Autowired
    RedisCache redisCache;
    @Autowired
    IUserInfoService userInfoService;
    @Autowired
    private IExclusiveShopService exclusiveShopService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    IApprovalService approvalService;
    /**
     * 查询代理商
     *
     * @param id 代理商主键
     * @return 代理商
     */
    @Override
    public Agent selectAgentById(Long id) {
        return agentMapper.selectAgentById(id);
    }

    @Override
    public Agent selectByName(String name) {
        return agentMapper.selectByName(name);
    }

    @Override
    public AgentDetailVo getAgentDetail(Long id) {
        AgentDetailVo build = AgentDetailVo.builder().build();
        Agent agent = agentMapper.selectAgentById(id);
        if (agent != null) {
            if (agent.getContactPerson() != null) {
                SysUser sysUser = sysUserMapper.selectUserById(agent.getContactPerson());
                List<Agent> agentList = getByUserId(sysUser.getUserId());
                agent.setContactPersonBean(MangerVo.builder().nickname(sysUser.getNickName()).userId(sysUser.getUserId()).phone(sysUser.getPhonenumber()).username(sysUser.getUserName()).build());
                if (agentList != null && !agentList.isEmpty())
                    build.setOtherAgentList(agentList.stream().filter(agent1 -> !agent1.getId().equals(id)).map(item -> AgentInfo.builder().id(item.getId()).aid(item.getAid()).name(item.getName()).build()).collect(Collectors.toList()));
            }
            agent.setManagerNames(this.getManagerNamesByAgentId(agent.getId()));
            QueryWrapper<RedbookTabletList> listQueryWrapper = new QueryWrapper<>();
            listQueryWrapper.eq("aid", agent.getAid());
            int all = redbookTabletListService.count(listQueryWrapper);
            listQueryWrapper.isNotNull("sale_date");
            int sale = redbookTabletListService.count(listQueryWrapper);
            AgentAccountSummaryVo agentAccountSummaryVo = agentAccountService.selectAgentAccountVoByAgentId(agent.getId());
            AgentOperationVo agentOperationVo = AgentOperationVo.builder().amount(agentAccountSummaryVo.getTotalMoney()).redBookOrderNum(all).redBookSaleNum(sale)
                    .redBookUserExpNum(userInfoService.selectExperienceUserCount(agent.getAid(), null))
                    .redBookUserNum(userInfoService.selectFormalUserCount(agent.getAid(), null)).build();
            build.setAgent(agent);
            build.setOperation(agentOperationVo);
        }
        return build;
    }

    @Override
    public List<Integer> getAgentIdList(Long mId, String searchValue,Long agentId) {
        return agentMapper.getAgentIdList(mId,searchValue,agentId);
    }

    @Override
    public List<String> getAgentAidList(Long mId, String searchValue, Long agentId) {
        return agentMapper.getAgentAidList(mId,searchValue,agentId);
    }

    /**
     * 查询代理商列表
     *
     * @param agent 代理商
     * @return 代理商
     */
    @Override
    public List<Agent> selectAgentList(Agent agent) {
        List<Agent> agents = agentMapper.selectAgentList(agent);
        agents.forEach(item -> {
            if (item.getContactPerson() != null) {
                SysUser sysUser = sysUserMapper.selectUserById(item.getContactPerson());
                if (sysUser != null) {
                    item.setContactPersonBean(MangerVo.builder().nickname(sysUser.getNickName()).userId(sysUser.getUserId()).phone(sysUser.getPhonenumber()).username(sysUser.getUserName()).build());
                }
            }
            item.setFormalUserCount(userInfoService.selectFormalUserCount(item.getAid(),null));
            item.setExperienceUserCount(userInfoService.selectExperienceUserCount(item.getAid(),null));
            item.setManagerNames(this.getManagerNamesByAgentId(item.getId()));
        });
        return agents;
    }

    @Override
    public List<Agent> getByUserId(Long userId) {
        return agentMapper.getByUserId(userId);
    }

    @Override
    public String getAgentName(Long agentId) {
        String key = "agentName:id:" + agentId;
        String agentName = redisCache.getCacheObject(key);
        if(agentName!=null){
            return agentName;
        }
        Agent agent = this.selectAgentById(agentId);
        if(agent==null){
            return null;
        }
        agentName = agent.getName();
        redisCache.setCacheObject(key,agentName);
        return agentName;
    }

    @Override
    public String getAgentName(String aid) {
        String key = "agentName:aid:" + aid;
        String agentName = redisCache.getCacheObject(key);
        if(agentName!=null){
            return agentName;
        }
        Agent agent = agentMapper.selectAgentByAid(aid);
        if(agent==null){
            return null;
        }
        agentName = agent.getName();
        redisCache.setCacheObject(key,agentName);
        return agentName;
    }

    @Override
    public String getAgentLevel(Long agentId) {
        String key = "agentLevel:id:" + agentId;
        String agentLevel = redisCache.getCacheObject(key);
        if(agentLevel!=null){
            return agentLevel;
        }
        Agent agent = this.selectAgentById(agentId);
        if(agent==null){
            return null;
        }
        agentLevel = agent.getLevel();
        redisCache.setCacheObject(key,agentLevel);
        return agentLevel;
    }

    @Override
    public String getManagerNamesByAgentId(Long agentId) {
        String key = "ManagerNames:agentId:" + agentId;
        Object object = redisCache.getCacheObject(key);
        if (object!=null){
            return (String)object;
        }
        List<String> managerNameList = agentMappingMapper.getManagerNameListByAgentId(agentId);
        StringBuilder builder = new StringBuilder();
        managerNameList.forEach(name -> {
            if(builder.length()>0){
                builder.append("，");
            }
            builder.append(name);
        });
        redisCache.setCacheObject(key,builder.toString(),3, TimeUnit.DAYS);
        return builder.toString();
    }

    @Override
    public String getKeFuAccountByAgentId(Long agentId) {
        return  agentMappingMapper.getKeFuAccountByAgentId(agentId);
    }

    /**
     * 新增代理商
     *
     * @param agent 代理商
     * @return 结果
     */
    @Override
    public int insertAgent(Agent agent) {
        agent.setCreateTime(DateUtils.getNowDate());
        return agentMapper.insertAgent(agent);
    }

    /**
     * 修改代理商
     *
     * @param agent 代理商
     * @return 结果
     */
    @Override
    public int updateAgent(Agent agent) {
        agent.setUpdateTime(DateUtils.getNowDate());
        return agentMapper.updateAgent(agent);
    }

    /**
     * 批量删除代理商
     *
     * @param ids 需要删除的代理商主键
     * @return 结果
     */
    @Override
    public int deleteAgentByIds(Long[] ids) {
        return agentMapper.deleteAgentByIds(ids);
    }

    /**
     * 删除代理商信息
     *
     * @param id 代理商主键
     * @return 结果
     */
    @Override
    public int deleteAgentById(Long id) {
        return agentMapper.deleteAgentById(id);
    }

    @Override
    public AgentMappingListVo agentMappingListVo(Long userId, Long roleId) {
        return AgentMappingListVo.builder()
                .hasMappingList(agentMapper.getByUserId(userId).stream().map(agent -> AgentInfo.builder().id(agent.getId()).aid(agent.getAid()).name(agent.getName()).build()).collect(Collectors.toList()))
                .canMappingList(agentMapper.getNoBindAgent(roleId).stream().map(agent -> AgentInfo.builder().id(agent.getId()).aid(agent.getAid()).name(agent.getName()).build()).collect(Collectors.toList()))
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveAgentMapping(AgentMappingDto dto) {
        SysUser sysUser = sysUserMapper.selectUserById(dto.getUserId());
        if (sysUser != null) {
            agentMappingMapper.deleteByUserId(dto.getUserId());
            dto.getAgentIds().forEach(agentId -> {
                AgentMapping build = AgentMapping.builder().agentId(Long.valueOf(agentId)).userId(dto.getUserId()).roleId(sysUser.getRole().getRoleId()).build();
                agentMappingMapper.insertAgentMapping(build);
            });
            //如果是签约人账号，将代理商对应的负责人关联id修改。
            if(sysUser.getRole().getRoleId()==100){
                //先把之前关联的代理商解除关联
                List<Agent> agents = this.selectAgentList(Agent.builder().contactPerson(sysUser.getUserId()).build());
                agents.forEach(agent -> {
                    agentMapper.updateAgent(Agent.builder().id(agent.getId()).contactPerson(-1L).build());
                });
                //把新代理商进行关联
                dto.getAgentIds().forEach(agentId -> {
                    agentMapper.updateAgent(Agent.builder().id(agentId.longValue()).contactPerson(sysUser.getUserId()).build());
                });
            }
        }
        this.deleteAgentCache();
        return dto.getAgentIds().size();
    }

    @Override
    public void deleteAgentCache() {
        redisCache.deleteKeys("user_params:*");
        redisCache.deleteKeys("user_exclusive_shops0326:*");
        redisCache.deleteKeys("agentIdList:*");
        redisCache.deleteKeys("agentAidList:*");
        redisCache.deleteKeys("agentName:*");
        redisCache.deleteKeys("exclusiveShopIdList:*");
        redisCache.deleteKeys("ManagerNames:*");
    }

    @Override
    @Transactional
    public Boolean addAgent(AddAgentDto dto) {
        if (dto.getParentId() != -1) {
            throw ServiceException.fail("不再支持创建二级代理商！！！");
        }
        Agent agent = new Agent();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser;
        if (dto.getNewAccount()) {
            sysUser = new SysUser();
            sysUser.setPhonenumber(dto.getContactPersonPhone());
            if (sysUserMapper.checkPhoneUnique(dto.getContactPersonPhone()) != null) {
                throw ServiceException.fail("用户已存在，请重新输入");
            }
            sysUser.setUserName(sysUserService.getUniqueUserName());
            sysUser.setSex("1");
            sysUser.setUserType("01");
            sysUser.setNickName(dto.getContactPerson());
            sysUser.setPassword(Md5Utils.hash("123456"));
            if (dto.getParentId() != -1) {
                sysUser.setRoleId(106L);
            } else {
                sysUser.setRoleId(100L);
            }
            if (sysUserService.insertUser(sysUser) < 1) {
                throw ServiceException.fail("用户添加失败");
            }
            sysUser = sysUserMapper.selectUserById(sysUser.getUserId());
        } else {
            sysUser = sysUserMapper.selectUserByUserName(dto.getContactPerson());
        }
        if (sysUser == null || sysUser.getUserId() == null) {
            throw ServiceException.fail("负责人账户不存在");
        }
        if (sysUser.getRole().getRoleId() == 100L && dto.getParentId() != -1) {
            throw ServiceException.fail("账户类型不匹配");
        }
        if (sysUser.getRole().getRoleId() == 106L && dto.getParentId() == -1) {
            throw ServiceException.fail("账户类型不匹配");
        }
        Area area = areaService.selectAreaById(dto.getAreaId());
        if (area == null) {
            throw ServiceException.fail("区域不存在");
        }
        if (!area.getStatus().equals("0") && dto.getAgreementLevel() != 4) {
            throw ServiceException.fail("该区域已被使用");
        }
        if (dto.getAgreementLevel() != 4) {
            area.setStatus("1");
            //更新区域状态
            areaService.updateArea(area);
        }
        Integer priceScheme=0;
        //取上级的定价策略给二级代理商
        if (dto.getParentId()!=-1){
            Agent parent = selectAgentById(dto.getParentId());
            if (parent!=null){
                priceScheme=parent.getPriceScheme();
            }
        }
        dto.setContactPerson(sysUser.getUserId().toString());
        BeanUtil.copyProperties(dto, agent);
        agent.setAid(getUniqueAid(PinyinUtil.getFirstLetter(area.getFullName(), "")));
        agent.setCode(getUniqueCode());
        agent.setAccountEnd(agent.getAgreementEnd());
        agent.setAlias(dto.getAlias());
        agent.setPriceScheme(priceScheme);
        agent.setGiftCount(dto.getGiftCount());
        insertAgent(agent);
        AgentMapping build = AgentMapping.builder().agentId(agent.getId()).userId(sysUser.getUserId()).roleId(sysUser.getRole().getRoleId()).build();
        agentMappingMapper.insertAgentMapping(build);
        if (agent.getParentId() != -1) {
            int count = 0;
            //如果是子代理商，生成代理商映射关系
            Agent parent = getById(agent.getParentId());
            //获取父级的父级
            Agent parent1 = getById(parent.getParentId());
            if (parent1 != null) {
                count++;
                if (parent1.getParentId() != -1) {
                    count++;
                }
            }
            build = AgentMapping.builder().agentId(agent.getId()).userId(parent.getContactPerson()).roleId(parent.getParentId() == -1 ? 100L : count).build();
            agentMappingMapper.insertAgentMapping(build);
        }
        AgentOperLog agentOperLog = new AgentOperLog();
        agentOperLog.setAgentId(agent.getId());
        agentOperLog.setLinkUser(loginUser.getUsername());
        agentOperLog.setType(1);
        agentOperLog.setContent("新建代理商");
        agentOperLogService.insertAgentOperLog(agentOperLog);
        //生成代理商钱包账户
        agentAccountMapper.insertAgentAccount(AgentAccount.builder().phone(sysUser.getPhonenumber()).state("1").payPassword(Md5Utils.hash("123456")).agentId(agent.getId()).build());
        //生成教师账号等
        genAgentTeacherAndUser(agent,null);
        //删除代理商缓存
        this.deleteAgentCache();
        //配赠账号
        approvalService.signingGiftAccount(agent.getId(),dto.getAccountGiftCount1Month(),1);
        approvalService.signingGiftAccount(agent.getId(),dto.getAccountGiftCount3Month(),3);
        approvalService.signingGiftAccount(agent.getId(),dto.getAccountGiftCount6Month(),6);
        approvalService.signingGiftAccount(agent.getId(),dto.getAccountGiftCount12Month(),12);
        return Boolean.TRUE;
    }

    private void genGiftAccount(){

    }

    @Override
    @Transactional
    public int updateAgent(UpdateAgentDto agent) {
        StringBuilder sb = new StringBuilder();
        Agent old = getById(agent.getId());
        SysUser oldUser = sysUserMapper.selectUserById(old.getContactPerson());
        if (oldUser != null) {
            old.setContactPersonBean(MangerVo.builder().nickname(oldUser.getNickName()).userId(oldUser.getUserId()).phone(oldUser.getPhonenumber()).username(oldUser.getUserName()).build());
        }
        Agent edit = new Agent();
        if (agent.getLevel() != null && !agent.getLevel().equals(old.getLevel())) {
            sb.append("代理商分级由").append(old.getLevel()).append("修改为").append(agent.getLevel()).append(";");
            edit.setLevel(agent.getLevel());
        }
        if (agent.getAgreementLevel() != null && !agent.getAgreementLevel().equals(old.getAgreementLevel())) {
            sb.append("代理商协议级别由").append(old.getAgreementLevel() == 1 ? "省" : old.getAgreementLevel() == 2 ? "市" : old.getAgreementLevel() == 3 ? "区县" : old.getAgreementLevel() == 4 ? "店" : "其他").append("修改为").append(agent.getAgreementLevel() == 1 ? "省" : agent.getAgreementLevel() == 2 ? "市" : agent.getAgreementLevel() == 3 ? "区县" : agent.getAgreementLevel() == 4 ? "店" : "其他").append(";");
            edit.setAgreementLevel(agent.getAgreementLevel());
        }
        if (agent.getType() != null && !agent.getType().equals(old.getType())) {
            sb.append("代理商签约类型由").append(old.getType()).append("修改为").append(agent.getType()).append(";");
            edit.setType(agent.getType());
        }
        if (agent.getCompanyName() != null && !agent.getCompanyName().equals(old.getCompanyName())) {
            sb.append("代理商公司名称由").append(old.getCompanyName()).append("修改为").append(agent.getCompanyName()).append(";");
            edit.setCompanyName(agent.getCompanyName());
        }
        if (agent.getTaxCode() != null && !agent.getTaxCode().equals(old.getTaxCode())) {
            sb.append("社会信用代码由").append(old.getTaxCode()).append("修改为").append(agent.getTaxCode()).append(";");
            edit.setTaxCode(agent.getTaxCode());
        }
        if (agent.getPersonName() != null && !agent.getPersonName().equals(old.getPersonName())) {
            sb.append("代理商联系人由").append(old.getPersonName()).append("修改为").append(agent.getPersonName()).append(";");
            edit.setPersonName(agent.getPersonName());
        }
        if (agent.getLevel() != null && !agent.getLevel().equals(old.getLevel())) {
            edit.setLevel(agent.getLevel());
            sb.append("代理商分级由").append(old.getLevel()).append("修改为").append(agent.getLevel()).append(";");
        }
        if (agent.getAlias() != null && !agent.getAlias().equals(old.getAlias())) {
            edit.setAlias(agent.getAlias());
            sb.append("代理商别名由").append(old.getAlias()).append("修改为").append(agent.getAlias()).append(";");
        }
        if (agent.getPhone() != null && !agent.getPhone().equals(old.getPhone())) {
            sb.append("代理商联系人电话由").append(old.getPhone()).append("修改为").append(agent.getPhone()).append(";");
            edit.setPhone(agent.getPhone());
        }
        if (agent.getIdCard() != null && !agent.getIdCard().equals(old.getIdCard())) {
            sb.append("代理商联系人身份证号由").append(old.getIdCard()).append("修改为").append(agent.getIdCard()).append(";");
            edit.setIdCard(agent.getIdCard());
        }
        if (agent.getAgreementStart() != null && !agent.getAgreementStart().equals(TimeUtils.parseDate(old.getAgreementStart(), TimeUtils.FORMAT_STANDARD_DATE))) {
            sb.append("代理商合同签约时间由").append(DateUtils.format(old.getAgreementStart(), TimeUtils.FORMAT_STANDARD_DATE)).append("修改为").append(agent.getAgreementStart()).append(";");
            edit.setAgreementStart(DateUtils.dateTime("yyyy-MM-dd", agent.getAgreementStart()));
        }
        if (agent.getAgreementEnd() != null && !agent.getAgreementEnd().equals(TimeUtils.parseDate(old.getAgreementEnd(), TimeUtils.FORMAT_STANDARD_DATE))) {
            sb.append("代理商合同到期时间由").append(DateUtils.format(old.getAgreementEnd(), TimeUtils.FORMAT_STANDARD_DATE)).append("修改为").append(agent.getAgreementEnd()).append(";");
            edit.setAgreementEnd(DateUtils.dateTime("yyyy-MM-dd", agent.getAccountEnd()));
        }
        if (agent.getAccountEnd() != null && !agent.getAccountEnd().equals(TimeUtils.parseDate(old.getAccountEnd(), TimeUtils.FORMAT_STANDARD_DATE))) {
            sb.append("代理商账号到期时间由").append(DateUtils.format(old.getAccountEnd(), TimeUtils.FORMAT_STANDARD_DATE)).append("修改为").append(agent.getAccountEnd()).append(";");
            updateAccountEndDate("onlyAgent",old.getAid(),null, agent.getAccountEnd());
            edit.setAccountEnd(DateUtils.dateTime("yyyy-MM-dd", agent.getAccountEnd()));
            //判断下属所有专卖店置过期日期，不允许超过代理商账号到期日期
            List<ExclusiveShop> exclusiveShopList = exclusiveShopService.selectExclusiveShopList(ExclusiveShop.builder().agentId(agent.getId()).build());
            exclusiveShopList.forEach(exclusiveShop -> {
                if(exclusiveShop.getEndDate().getTime()>edit.getAccountEnd().getTime()){
                    exclusiveShop.setEndDate(edit.getAccountEnd());
                    exclusiveShopService.updateExclusiveShop(exclusiveShop);
                }
            });
        }
        /*if (agent.getContactPersonPhone() != null && (old.getContactPerson() == null || !agent.getContactPersonPhone().equals(old.getContactPersonBean().getPhone()))) {
            SysUser sysUser = sysUserMapper.checkPhoneUnique(agent.getContactPersonPhone());
            if (sysUser == null) {
                throw ServiceException.fail("联系人账号不存在");
            }
            sysUser=sysUserService.selectUserById(sysUser.getUserId());
            if (sysUser.getRole().getRoleId() != 100&&sysUser.getRole().getRoleId() != 106) {
                throw ServiceException.fail("联系人账号不是代理商账号");
            }
            sb.append("代理商联系人由").append(old.getContactPerson()).append("修改为").append(sysUser.getUserName()).append(";");
            //删除原先的关系
            agentMappingMapper.deleteAgentMappingByUserId(old.getContactPersonBean().getUserId(), agent.getId());
            edit.setContactPerson(sysUser.getUserId());
            AgentMapping build = AgentMapping.builder().agentId(agent.getId()).userId(sysUser.getUserId()).roleId(sysUser.getRole().getRoleId()).build();
            agentMappingMapper.insertAgentMapping(build);
        }*/
        if (agent.getAreaId() != null && !agent.getAreaId().equals(old.getAreaId())) {
            Area area = areaService.selectAreaById(agent.getAreaId());
            Area oldArea = areaService.selectAreaById(old.getAreaId());

            edit.setAreaId(agent.getAreaId());
            if (area == null) {
                throw ServiceException.fail("区域不存在");
            }
            if (!area.getStatus().equals("0")) {
                throw ServiceException.fail("该区域已被使用");
            }
            sb.append("代理商所属区域由").append(oldArea == null ? "未分配 " : oldArea.getFullName()).append("修改为").append(area.getFullName()).append(";");
            if (agent.getAgreementLevel() != 4) {
                area.setStatus("1");
                //更新区域状态
                areaService.updateArea(area);
                if (oldArea != null) {
                    oldArea.setStatus("0");
                    areaService.updateArea(oldArea);
                }

            }
        }
        edit.setId(agent.getId());
        edit.setUpdateTime(DateUtils.getNowDate());
        AgentOperLog agentOperLog = new AgentOperLog();
        if (sb.length() < 1) throw ServiceException.fail("没有修改任何信息");
        agentOperLog.setContent(sb.toString());
        agentOperLog.setAgentId(agent.getId());
        agentOperLog.setLinkUser(SecurityUtils.getLoginUser().getUser().getUserName());
        agentOperLog.setType(1);
        agentOperLog.setExtra(agent.getExtraJson());
        agentOperLog.setModifyType("修改信息");
        //插入操作日志
        agentOperLogService.insertAgentOperLog(agentOperLog);
        //删除代理商缓存
        this.deleteAgentCache();
        return agentMapper.updateAgent(edit);
    }

    @Override
    @Transactional
    public int exit(AgentExitDto dto) {
        Agent agent = getById(dto.getId());
        if (agent == null) {
            throw ServiceException.fail("代理商不存在");
        }
//        selectAgentList(Agent.builder().parentId(agent.getId()).build()).forEach(item -> {
//            exit(AgentExitDto.builder().id(item.getId()).date(dto.getDate()).build());
//        });
        //非店级代理商 退盟时需要将区域状态改为未使用
        if (agent.getAreaId() != null && agent.getAreaId() != -1 && agent.getAgreementLevel() != 4) {
            Area area = areaService.selectAreaById(agent.getAreaId());
            if (area != null) {
                area.setStatus("0");
                areaService.updateArea(area);
            }
        }
        //退盟后在名称后追加退盟字样
        if(agent.getName().indexOf("（退盟）")==-1){
            agent.setName(agent.getName()+"（退盟）");
        }if(agent.getAlias().indexOf("（退盟）")==-1){
            agent.setAlias(agent.getAlias()+"（退盟）");
        }
        agent.setAgreementEnd(DateUtils.dateTime("yyyy-MM-dd", dto.getDate()));
        agent.setAccountEnd(DateUtils.dateTime("yyyy-MM-dd", dto.getDate()));
        agent.setUpdateTime(DateUtils.getNowDate());
        agent.setStatus(1L);
        agent.setAreaId(-1L);
        AgentOperLog agentOperLog = new AgentOperLog();
        agentOperLog.setAgentId(dto.getId());
        agentOperLog.setLinkUser(SecurityUtils.getLoginUser().getUser().getUserName());
        agentOperLog.setType(1);
        agentOperLog.setContent("代理商退盟");
        agentOperLog.setModifyType("退盟");
        //插入操作日志
        agentOperLogService.insertAgentOperLog(agentOperLog);
        //清空账户余额
        agentAccountService.clearBalance(agent.getId());
        //更新学生账户到期时间
        updateAccountEndDate("all",agent.getAid(),null, dto.getDate());
        //解除签约人管理关系
        if (agent.getContactPerson() != null) {
            //解除管理关系
            agentMappingMapper.deleteAgentMappingByUserId(agent.getContactPerson(), agent.getId());
            //删除代理商助理关联关系
            agentMappingMapper.deleteAgentMappingByRoleId(122L, agent.getId());
        }
        //将下属所有专卖店置为过期状态。
        List<ExclusiveShop> exclusiveShopList = exclusiveShopService.selectExclusiveShopList(ExclusiveShop.builder().agentId(agent.getId()).build());
        exclusiveShopList.forEach(exclusiveShop -> {
            exclusiveShop.setStatus(1);
            exclusiveShopService.updateExclusiveShop(exclusiveShop);
        });
        //将下属所有店长置为停用状态。
        SysUser queryShopManager = new SysUser();
        queryShopManager.setAgentId(agent.getId());
        List<SysUser> shopManagerList = userService.selectExclusiveShopManagerList(queryShopManager);
        shopManagerList.forEach(shopManager -> {
            shopManager.setStatus("1");
            shopManager.setRoleId(106L);
            userService.updateUser(shopManager);
        });
        //删除代理商缓存
        this.deleteAgentCache();
        return updateAgent(agent);
    }

    @Override
    @Transactional
    public int renew(AgentRenewDto dto) {
        Agent agent = getById(dto.getId());
        agent.setAgreementEnd(DateUtils.dateTime("yyyy-MM-dd", dto.getDate()));
        agent.setAccountEnd(DateUtils.dateTime("yyyy-MM-dd", dto.getDate()));
        if (agent.getStatus() == 1) {
            agent.setStatus(0L);
            Long agentId = agent.getId();
            while (agentId != null && agentId != -1) {
                Agent agent1 = getById(agentId);
                agentId = agent1.getParentId();
                SysUser sysUser = sysUserMapper.selectUserById(agent1.getContactPerson());
                if (sysUser != null) {
                    //添加管理关系
                    agentMappingMapper.insertAgentMapping(AgentMapping.builder().agentId(agent.getId()).userId(sysUser.getUserId()).roleId(sysUser.getRole().getRoleId()).build());
                }
            }
        }
        agent.setUpdateTime(DateUtils.getNowDate());
        AgentOperLog agentOperLog = new AgentOperLog();
        agentOperLog.setAgentId(dto.getId());
        agentOperLog.setLinkUser(SecurityUtils.getLoginUser().getUser().getUserName());
        agentOperLog.setType(1);
        agentOperLog.setContent("代理商续约："+dto.getDate());
        agentOperLog.setModifyType("续约");
        //插入操作日志
        agentOperLogService.insertAgentOperLog(agentOperLog);
        //更新学生账户到期时间
        updateAccountEndDate("onlyAgent",agent.getAid(),null, dto.getDate());
        //删除代理商缓存
        this.deleteAgentCache();
        //判断下属所有专卖店过期日期是否超过了代理商到期日期。（支持续约日期改为以前日期）
        List<ExclusiveShop> exclusiveShopList = exclusiveShopService.selectExclusiveShopList(ExclusiveShop.builder().agentId(agent.getId()).build());
        exclusiveShopList.forEach(exclusiveShop -> {
            //如果专卖店到期日期晚于代理商到期日期，需要置为停止运营状态
            if(exclusiveShop.getStatus().intValue()==0 && exclusiveShop.getEndDate().getTime()>agent.getAccountEnd().getTime()){
                exclusiveShop.setStatus(1);
                exclusiveShopService.updateExclusiveShop(exclusiveShop);
            }
        });
        return updateAgent(agent);
    }

    private String getUniqueAid(String aid) {
        LambdaQueryWrapper<Agent> agentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        int num = 0;
        agentLambdaQueryWrapper.eq(Agent::getAid, aid);
        if (count(agentLambdaQueryWrapper) != 0) {
            while (count(agentLambdaQueryWrapper) != 0) {
                num++;
                String tmp = aid + num;
                agentLambdaQueryWrapper.clear();
                agentLambdaQueryWrapper.eq(Agent::getAid, tmp);
            }
            return aid + num;
        }
        return aid;
    }

    private String getUniqueCode() {
        LambdaQueryWrapper<Agent> agentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        while (true) {
            String code = "TYZX" + IdentityGenerator.randomString(4);
            agentLambdaQueryWrapper.clear();
            agentLambdaQueryWrapper.eq(Agent::getCode, code);
            if (count(agentLambdaQueryWrapper) == 0) return code;
        }
    }
    @Override
    public void genAgentTeacherAndUser(Agent agent,ExclusiveShop exclusiveShop) {
        Date endDate = agent.getAccountEnd();
        Integer exclusiveShopId = null;
        if (exclusiveShop != null) {
            exclusiveShopId = exclusiveShop.getId();
            endDate = exclusiveShop.getEndDate();
        }
        boolean teacher = true;
        String teachId = null;
        int classId = 0;
        while (teacher) {
            try {
                teachId = "thb" + IdentityGenerator.randomString(5);
            } catch (Exception e) {
            }
            if (agentMapper.checkHasTeacherUser(teachId) < 1) {
                agentMapper.createTeacherDefaultClass(agent.getAid(),teachId);
                classId = Integer.parseInt(userInfoService.getClassList(teachId).get(0).getId());
                agentMapper.insertTeacherUser(agent.getAid(),exclusiveShopId, teachId,classId, TimeUtils.date2LocalDate(endDate));
                teacher = false;
            }
        }
        boolean studentVip = true;
        String userId = null;
        while (studentVip) {
            try {
                userId = "svip" + IdentityGenerator.randomString(5);
            } catch (Exception e) {
            }
            if (agentMapper.checkHasStudentUser(userId) < 1) {
                agentMapper.insertStudentVipUser(agent.getAid(),exclusiveShopId,teachId,classId, userId, TimeUtils.date2LocalDate(endDate));
                studentVip = false;
            }
        }
    }

    /**
     * 更新超级会员和学管师到期时间
     * @param updateScope 影响范围  all:代理商下所有超级会员 onlyAgent:只有代理商下的，排除专卖店   onlyExclusiveShop：专卖店的超级会员
     * @param aid
     * @param exclusiveShopId
     * @param date
     */
    @Override
    public void updateAccountEndDate(String updateScope,String aid,Integer exclusiveShopId, String date) {
        agentMapper.updateTeacherEndDate(updateScope,aid, exclusiveShopId, TimeUtils.string2LocalDate(date, TimeUtils.FORMAT_STANDARD_DATE));
        agentMapper.updateSvipStudentEndDate(updateScope,aid, exclusiveShopId, TimeUtils.string2LocalDate(date, TimeUtils.FORMAT_STANDARD_DATE));
    }

    @Override
    public int setAgentGiftCount(SetAgentGiftCountDto agent) {
        Long id = agent.getId();
        if(id==null){
            throw ServiceException.fail("代理商id为空");
        }
        Agent agentDb = agentMapper.selectAgentById(id);
        if(agentDb==null){
            throw ServiceException.fail("未查询到代理商信息");
        }
        Integer giftCount = agent.getGiftCount();
        if(giftCount==null){
            throw ServiceException.fail("请输入要配置的赠送硬件数量");
        }
        //配赠的硬件台数必须是5台倍数
        if (giftCount%5!=0){
            throw ServiceException.fail("配赠的硬件台数必须是5台倍数");
        }

//        if(giftCount<10||giftCount%10>0){
//            throw ServiceException.fail("配赠的硬件台数必须是10台或者10台倍数");
//        }
        int i = agentMapper.setAgentGiftCount(id, agent.getGiftCount());
        if(i>0){
            AgentOperLog agentOperLog = new AgentOperLog();
            agentOperLog.setAgentId(agent.getId());
            agentOperLog.setType(1);
            agentOperLog.setModifyType("手动记录");
            agentOperLog.setContent("设置代理商赠送的硬件数量为："+giftCount+"个");
            LoginUser loginUser = SecurityUtils.getLoginUser();
            agentOperLog.setLinkUser(loginUser.getUsername());
            agentOperLogService.insertAgentOperLog(agentOperLog);
        }
        return i;
    }

    @Override
    public int setGiftAccount(SetAgentGiftAccountDto agent) {
        //配赠账号
        Long id = agent.getId();
        if(id==null){
            throw ServiceException.fail("代理商id为空");
        }
        Agent agentDb = agentMapper.selectAgentById(id);
        if(agentDb==null){
            throw ServiceException.fail("未查询到代理商信息");
        }
        int i = approvalService.signingGiftAccount(id, agent.getAccountGiftCount1Month(), 1);
        int i1 = approvalService.signingGiftAccount(id, agent.getAccountGiftCount3Month(), 3);
        int i2 = approvalService.signingGiftAccount(id, agent.getAccountGiftCount6Month(), 6);
        int i3 = approvalService.signingGiftAccount(id, agent.getAccountGiftCount12Month(), 12);
        if(i+i1+i2+i3>0){
            AgentOperLog agentOperLog = new AgentOperLog();
            agentOperLog.setAgentId(agent.getId());
            agentOperLog.setType(1);
            agentOperLog.setModifyType("手动记录");

            StringBuffer stringBuffer=new StringBuffer();
            if(agent.getAccountGiftCount1Month()!=null&&agent.getAccountGiftCount1Month()>0){
                stringBuffer.append("一个月课程"+agent.getAccountGiftCount1Month()+"个");
            }
            if(agent.getAccountGiftCount3Month()!=null&&agent.getAccountGiftCount3Month()>0){
                if(stringBuffer.length()>0){
                    stringBuffer.append("、");
                }
                stringBuffer.append("三个月课程"+agent.getAccountGiftCount3Month()+"个");
            }
            if(agent.getAccountGiftCount6Month()!=null&&agent.getAccountGiftCount6Month()>0){
                if(stringBuffer.length()>0){
                    stringBuffer.append("、");
                }
                stringBuffer.append("六个月课程"+agent.getAccountGiftCount6Month()+"个");
            }
            if(agent.getAccountGiftCount12Month()!=null&&agent.getAccountGiftCount12Month()>0){
                if(stringBuffer.length()>0){
                    stringBuffer.append("、");
                }
                stringBuffer.append("12个月课程"+agent.getAccountGiftCount12Month()+"个");
            }
            agentOperLog.setContent("设置代理商会员课程:"+ stringBuffer);
            LoginUser loginUser = SecurityUtils.getLoginUser();
            agentOperLog.setLinkUser(loginUser.getUsername());
            agentOperLogService.insertAgentOperLog(agentOperLog);
        }
        return i+i1+i2+i3;
    }

    @Override
    public Agent getByAid(String aid) {
        return agentMapper.selectByAid(aid);
    }


    @Override
    public List<Map<String, Object>> MemberPaymentInfo(MemberPaymentInfoDto memberPaymentInfoDto) {
        return memberRenewDao.MemberPaymentInfo(memberPaymentInfoDto);
    }

    @Override
    public AgentAreaVo getAgentArea(Long id) {
        AgentAreaVo agentAreaVo = new AgentAreaVo();
        Agent agent = getById(id);
        agentAreaVo.setId(agent.getId());
        agentAreaVo.setName(agent.getName());
        agentAreaVo.setAid(agent.getAid());
        if (agent.getAreaId() != null && agent.getAreaId() != -1) {
            agentAreaVo.setAreaId(agent.getAreaId());
            Area area = areaService.selectAreaById(agent.getAreaId());
            if (area.getLevel() == 1) {
                agentAreaVo.setProvinceId(area.getId().intValue());
                agentAreaVo.setProvinceName(area.getName());
            }
            if (area.getLevel() == 2) {
                agentAreaVo.setCityId(area.getId().intValue());
                agentAreaVo.setCityName(area.getName());
                Area area1 = areaService.selectAreaById(area.getParentId());
                agentAreaVo.setProvinceId(area1.getId().intValue());
                agentAreaVo.setProvinceName(area1.getName());
            }
            if (area.getLevel() == 3) {
                agentAreaVo.setDistrictId(area.getId().intValue());
                agentAreaVo.setDistrictName(area.getName());
                Area area1 = areaService.selectAreaById(area.getParentId());
                agentAreaVo.setCityId(area1.getId().intValue());
                agentAreaVo.setCityName(area1.getName());
                Area area2 = areaService.selectAreaById(area1.getParentId());
                agentAreaVo.setProvinceId(area2.getId().intValue());
                agentAreaVo.setProvinceName(area2.getName());
            }
        }
        return agentAreaVo;
    }

}
