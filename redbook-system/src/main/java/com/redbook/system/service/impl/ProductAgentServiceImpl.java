package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.system.domain.ProductAgent;
import com.redbook.system.mapper.ProductAgentMapper;
import com.redbook.system.service.IProductAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 区域商城商品状态Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-22
 */
@Service
public class ProductAgentServiceImpl extends ServiceImpl<ProductAgentMapper, ProductAgent> implements IProductAgentService
{
    @Autowired
    private ProductAgentMapper productAgentMapper;

    /**
     * 查询区域商城商品状态
     * 
     * @param id 区域商城商品状态主键
     * @return 区域商城商品状态
     */
    @Override
    public ProductAgent selectProductAgentById(Integer id)
    {
        return productAgentMapper.selectProductAgentById(id);
    }

    @Override
    public ProductAgent selectProductAgent(Long agentId, Integer productId) {
        return productAgentMapper.selectProductAgent(agentId, productId);
    }

    /**
     * 查询区域商城商品状态列表
     * 
     * @param productAgent 区域商城商品状态
     * @return 区域商城商品状态
     */
    @Override
    public List<ProductAgent> selectProductAgentList(ProductAgent productAgent)
    {
        return productAgentMapper.selectProductAgentList(productAgent);
    }

    /**
     * 新增区域商城商品状态
     * 
     * @param productAgent 区域商城商品状态
     * @return 结果
     */
    @Override
    public int insertProductAgent(ProductAgent productAgent)
    {
        return productAgentMapper.insertProductAgent(productAgent);
    }

    /**
     * 修改区域商城商品状态
     * 
     * @param productAgent 区域商城商品状态
     * @return 结果
     */
    @Override
    public int updateProductAgent(ProductAgent productAgent)
    {
        int i = productAgentMapper.updateProductAgent(productAgent);
        if(i<=0){
            i = this.insertProductAgent(productAgent);
        }
        return i;
    }

    /**
     * 批量删除区域商城商品状态
     * 
     * @param ids 需要删除的区域商城商品状态主键
     * @return 结果
     */
    @Override
    public int deleteProductAgentByIds(Integer[] ids)
    {
        return productAgentMapper.deleteProductAgentByIds(ids);
    }

    /**
     * 删除区域商城商品状态信息
     * 
     * @param id 区域商城商品状态主键
     * @return 结果
     */
    @Override
    public int deleteProductAgentById(Integer id)
    {
        return productAgentMapper.deleteProductAgentById(id);
    }

    @Override
    public int changeStatus(Integer[] ids, Integer status) {
        int i = 0;
        for (Integer id : ids) {
            i+=productAgentMapper.updateProductAgent(ProductAgent.builder().id(id).status(status).build());
        }
        return i;
    }
}
