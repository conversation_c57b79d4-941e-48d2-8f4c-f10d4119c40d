package com.redbook.system.service.oldSystem.impl;

import com.redbook.system.mapper.IReceivingAddressManagementDao;
import com.redbook.system.service.oldSystem.IReceivingAddressManagementService;
import com.redbook.system.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service("receivingAddressManagementService")
public class ReceivingAddressManagementServiceImpl implements IReceivingAddressManagementService {
    @Autowired
    IReceivingAddressManagementDao receivingAddressManagementDao;

    @Override
    public List<Map<String, Object>> getAddressList(List agentIdList,Integer uid) {
        return receivingAddressManagementDao.getAddressList(agentIdList,uid);
    }

    @Override
    public boolean insertReceivingAddress(Integer agentId, String aid, String name, String phone, String address,Integer uid) {
        String time = DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");

        return receivingAddressManagementDao.insertReceivingAddress(agentId,aid, name, phone, address, time, uid);
    }

    @Override
    public Map<String, Object> getAddressById(Integer id) {
        return receivingAddressManagementDao.getAddressById(id);
    }

    @Override
    public boolean updateReceivingAddress(Integer id, String name, String phone, String address) {
        String time = DateUtil.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
        return receivingAddressManagementDao.updateReceivingAddress(id, name, phone, address, time);
    }

    @Override
    public boolean deleteReceivingAddress(Integer id) {
        return receivingAddressManagementDao.deleteReceivingAddress(id);
    }

    @Override
    public List<Map<String, Object>> getAddressByAgentId(List<Integer> agentIds) {
        return receivingAddressManagementDao.getAddressByAgentId(agentIds);
    }
}
