package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.system.domain.AgentActivityItem;
import com.redbook.system.mapper.AgentActivityItemMapper;
import com.redbook.system.service.IAgentActivityItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 代理商活动-商品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-11
 */
@Service
public class AgentActivityItemServiceImpl extends ServiceImpl<AgentActivityItemMapper, AgentActivityItem> implements IAgentActivityItemService
{
    @Autowired
    private AgentActivityItemMapper agentActivityItemMapper;

    /**
     * 查询代理商活动-商品
     * 
     * @param id 代理商活动-商品主键
     * @return 代理商活动-商品
     */
    @Override
    public AgentActivityItem selectAgentActivityItemById(Long id)
    {
        return agentActivityItemMapper.selectAgentActivityItemById(id);
    }

    /**
     * 查询代理商活动-商品列表
     * 
     * @param agentActivityItem 代理商活动-商品
     * @return 代理商活动-商品
     */
    @Override
    public List<AgentActivityItem> selectAgentActivityItemList(AgentActivityItem agentActivityItem)
    {
        return agentActivityItemMapper.selectAgentActivityItemList(agentActivityItem);
    }

    /**
     * 新增代理商活动-商品
     * 
     * @param agentActivityItem 代理商活动-商品
     * @return 结果
     */
    @Override
    public int insertAgentActivityItem(AgentActivityItem agentActivityItem)
    {
        return agentActivityItemMapper.insertAgentActivityItem(agentActivityItem);
    }

    /**
     * 修改代理商活动-商品
     * 
     * @param agentActivityItem 代理商活动-商品
     * @return 结果
     */
    @Override
    public int updateAgentActivityItem(AgentActivityItem agentActivityItem)
    {
        return agentActivityItemMapper.updateAgentActivityItem(agentActivityItem);
    }

    @Override
    public int deleteByAgentActivityItem(AgentActivityItem agentActivityItem) {
        return agentActivityItemMapper.deleteByAgentActivityItem(agentActivityItem);
    }

    /**
     * 批量删除代理商活动-商品
     * 
     * @param ids 需要删除的代理商活动-商品主键
     * @return 结果
     */
    @Override
    public int deleteAgentActivityItemByIds(Long[] ids)
    {
        return agentActivityItemMapper.deleteAgentActivityItemByIds(ids);
    }

    /**
     * 删除代理商活动-商品信息
     * 
     * @param id 代理商活动-商品主键
     * @return 结果
     */
    @Override
    public int deleteAgentActivityItemById(Long id)
    {
        return agentActivityItemMapper.deleteAgentActivityItemById(id);
    }
}
