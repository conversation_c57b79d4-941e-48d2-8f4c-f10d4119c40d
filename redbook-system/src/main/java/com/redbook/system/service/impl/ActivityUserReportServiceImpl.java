package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.system.domain.ActivityUserReport;
import com.redbook.system.mapper.ActivityUserReportMapper;
import com.redbook.system.service.IActivityUserReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-27
 */
@Service
public class ActivityUserReportServiceImpl extends ServiceImpl<ActivityUserReportMapper, ActivityUserReport> implements IActivityUserReportService {
    @Autowired
    private ActivityUserReportMapper activityUserReportMapper;

    /**
     * 查询用户报告列表
     *
     * @param activityUserReport 用户报告
     * @return 用户报告
     */
    @Override
    public List<ActivityUserReport> selectActivityUserReportList(ActivityUserReport activityUserReport) {
        return activityUserReportMapper.selectActivityUserReportList(activityUserReport);
    }
}
