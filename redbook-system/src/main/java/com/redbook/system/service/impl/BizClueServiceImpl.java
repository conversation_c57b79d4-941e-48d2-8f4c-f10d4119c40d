package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.constant.HttpStatus;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.domain.model.LoginUser;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.*;
import com.redbook.system.domain.dto.BizCluePostDTO;
import com.redbook.system.domain.excel.BizClueExcel;
import com.redbook.system.domain.vo.*;
import com.redbook.system.enums.ClueOperateLogEnum;
import com.redbook.system.enums.CluePoolTypeEnum;
import com.redbook.system.enums.ClueStatusEnum;
import com.redbook.system.mapper.BizClueMapper;
import com.redbook.system.mapper.ExclusiveShopMapper;
import com.redbook.system.service.*;
import com.redbook.system.util.ComUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 线索Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
@Service
public class BizClueServiceImpl extends ServiceImpl<BizClueMapper, BizClue> implements IBizClueService
{
    @Autowired
    private BizClueMapper bizClueMapper;
    @Autowired
    private IBizClueReferralService referralService;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IBizClueLogService logService;

    @Autowired
    private IBizChannelService channelService;

    @Autowired
    private IAgentUserService agentUserService;
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IExclusiveShopService shopService;

    @Autowired
    private ExclusiveShopMapper exclusiveShopMapper;
    /**
     * 查询线索
     * 
     * @param clueId 线索主键
     * @return 线索
     */
    @Override
    public BizClueEditVO selectBizClueByClueId(Long clueId)
    {
        BizClue bizClue = bizClueMapper.selectBizClueByClueId(clueId);
        BizClueReferral build = BizClueReferral.builder().clueId(bizClue.getClueId()).build();
        List<BizClueReferral> referralList = referralService.selectBizClueReferralList(build);
        //线索基础信息+转介绍人数
        BizClueEditVO bizClueEditVO = new BizClueEditVO();
        BeanUtils.copyProperties(bizClue,bizClueEditVO);
        bizClueEditVO.setClueReferralCount(referralList.size());
        //到期日期
        if(bizClue.getUserId() != null){
            Map<String, Object> studentBasicInfo = userInfoService.getStudentBasicInfo(bizClue.getUserId());
            Date expirationDate = (Date) studentBasicInfo.get("expiration_date");
            bizClueEditVO.setExpirationDate(expirationDate);
        }
        //线索动态列表
        BizClueLog clueLog = BizClueLog.builder().clueId(bizClue.getClueId()).build();
        List<BizClueLogVO> clueLogVOList = logService.selectBizClueLogVOList(clueLog);
        bizClueEditVO.setClueLogList(clueLogVOList);
        return bizClueEditVO;
    }

    /**
     * 查询线索列表
     * 
     * @param bizClueRequestVO 线索
     * @return 线索
     */
    @Override
    public List<BizClueListVO> selectBizClueList(BizClueRequestVO bizClueRequestVO)
    {
        return bizClueMapper.selectBizClueList(bizClueRequestVO);
    }

    @Override
    public List<BizClueReferralVO> selectClueListForReferral(Long clueId) {
        return bizClueMapper.selectClueListForReferral(clueId);
    }

    /**
     * 批量分配：只有共有池、跟进池的线索，才会被分配，并更新线索池为：跟进池
     * @param bizCluePostDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int batchAssign(BizCluePostDTO bizCluePostDTO) {
        List<Integer> cluePoolTypes = new ArrayList<>();
        cluePoolTypes.add(CluePoolTypeEnum.COMMON_POOL.getValue());
        cluePoolTypes.add(CluePoolTypeEnum.FOLLOW_POOL.getValue());
        bizCluePostDTO.setCluePoolTypes(cluePoolTypes);
        //查询校验是否为共有池、跟进池的线索
        List<Long> passIds = this.verifyPostClueId(bizCluePostDTO, cluePoolTypes);
        if(passIds.isEmpty()){
            return 0;
        }
        AgentUser agentUser = agentUserService.selectAgentUserByUserId(bizCluePostDTO.getAgentUserId());
        bizCluePostDTO.setClueIds(passIds.stream().toArray(Long[]::new));
        bizCluePostDTO.setAgentUserName(agentUser.getUserName());
        //线索池更新为跟进池
        bizCluePostDTO.setCluePoolType(CluePoolTypeEnum.FOLLOW_POOL.getValue());
        //批量分配
        int i = bizClueMapper.batchPost(bizCluePostDTO);

        //记录线索动态
        String operaterContent = MessageFormat.format(ClueOperateLogEnum.ASSIGN.getContent(),agentUser.getUserName());
        this.batchInsertClueLog(bizCluePostDTO.getClueIds(), ClueOperateLogEnum.ASSIGN.getOperate(),operaterContent);

        return i;
    }

    /**
     * 批量插入线索动态
     * @param clueIds
     * @param clueLogType
     * @param operaterContent
     */
    public void batchInsertClueLog(Long[] clueIds,String clueLogType,String operaterContent){

        if(clueIds.length == 0){
            return;
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<BizClueLog> list = new ArrayList<>();

        for(int i=0;i<clueIds.length;i++){
            BizClueLog bizClueLog = new BizClueLog();
            bizClueLog.setClueId(clueIds[i]);
            bizClueLog.setClueLogType(clueLogType);
            bizClueLog.setOperaterId(loginUser.getUserId().toString());
            bizClueLog.setOperaterName(loginUser.getUsername());
            bizClueLog.setOperaterContent(operaterContent);
            bizClueLog.setCreateTime(new Date());
            bizClueLog.setInputTime(new Date());
            list.add(bizClueLog);
        }

        if(!list.isEmpty()){
            //批量插入线索动态
            logService.batchInsertBizClueLog(list);
        }

    }

    /**
     * 回收共有池：只有跟进池的线索，才会被回收，并更新线索池为：共有池
     * @param bizCluePostDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int recycleCommonPool(BizCluePostDTO bizCluePostDTO) {
        List<Integer> cluePoolTypes = new ArrayList<>();
        cluePoolTypes.add(CluePoolTypeEnum.FOLLOW_POOL.getValue());
        cluePoolTypes.add(CluePoolTypeEnum.COMMON_POOL.getValue());
        bizCluePostDTO.setCluePoolTypes(cluePoolTypes);
        //查询校验是否为跟进池的线索
        List<Long> passIds = this.verifyPostClueId(bizCluePostDTO, cluePoolTypes);
        if(passIds.isEmpty()){
            return 0;
        }
        //将负责人id，负责人姓名，置空。
        bizCluePostDTO.setAgentUserId("");
        bizCluePostDTO.setAgentUserName("");
        //线索池更新为共有池
        bizCluePostDTO.setCluePoolType(CluePoolTypeEnum.COMMON_POOL.getValue());
        bizCluePostDTO.setClueIds(passIds.stream().toArray(Long[]::new));
        //批量回收
        int i = bizClueMapper.batchPost(bizCluePostDTO);

        //记录线索动态
        this.batchInsertClueLog(bizCluePostDTO.getClueIds(),
                ClueOperateLogEnum.RECYCLE.getOperate(),
                ClueOperateLogEnum.RECYCLE.getContent());

        return i;
    }

    /**
     * 批量转移
     * @param bizCluePostDTO 线索ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int batchTransfer(BizCluePostDTO bizCluePostDTO) {
        List<Integer> cluePoolTypes = new ArrayList<>();
        cluePoolTypes.add(CluePoolTypeEnum.FOLLOW_POOL.getValue());
        cluePoolTypes.add(CluePoolTypeEnum.COMMON_POOL.getValue());
        bizCluePostDTO.setCluePoolTypes(cluePoolTypes);
        //查询校验是否为跟进池的线索
        List<Long> passIds = this.verifyPostClueId(bizCluePostDTO, cluePoolTypes);
        if(passIds.isEmpty()){
            return 0;
        }
        //将负责人id，负责人姓名，置空。
        bizCluePostDTO.setAgentUserId("");
        bizCluePostDTO.setAgentUserName("");
        //线索池更新为共有池
        bizCluePostDTO.setCluePoolType(CluePoolTypeEnum.COMMON_POOL.getValue());
        bizCluePostDTO.setClueIds(passIds.stream().toArray(Long[]::new));
        //记录线索动态
        this.insertBatchTransferClueLog(bizCluePostDTO);

        //批量转移
        int i = bizClueMapper.batchPost(bizCluePostDTO);

        return i;
    }


    /**
     * 记录批量转移的线索动态日志
     * @param bizCluePostDTO
     */
    public void insertBatchTransferClueLog(BizCluePostDTO bizCluePostDTO){
        LoginUser loginUser = SecurityUtils.getLoginUser();

        //转移后的新区域名称组装
        Agent agent = agentService.selectAgentById(bizCluePostDTO.getAgentId());
        String agentName = agent.getName();
        ExclusiveShop exclusiveShop = shopService.selectExclusiveShopById(bizCluePostDTO.getExclusiveShopId());
        if(!ComUtil.isEmpty(exclusiveShop)){
            agentName = agentName + "（" +exclusiveShop.getName() + ")";
        }else {
            bizCluePostDTO.setExclusiveShopId(0);
        }
        String newAgentName = agentName;

        BizClueRequestVO bizClueRequestVO = new BizClueRequestVO();
        bizClueRequestVO.setClueIds(bizCluePostDTO.getClueIds());
        List<BizClueListVO> bizClueListVOS = bizClueMapper.selectBizClueList(bizClueRequestVO);

        List<BizClueLog> logList = bizClueListVOS.stream().map(item ->{
            BizClueLog bizClueLog = new BizClueLog();
            bizClueLog.setClueId(item.getClueId());
            bizClueLog.setClueLogType(ClueOperateLogEnum.TRANSFER.getOperate());
            bizClueLog.setOperaterId(loginUser.getUserId().toString());
            bizClueLog.setOperaterName(loginUser.getUsername());
            String oldAgentName = item.getAgentName();
            if(item.getExclusiveShopName() != null){
                oldAgentName = item.getAgentName() + "（" + item.getExclusiveShopName() + "）";
            }
            bizClueLog.setOperaterContent(MessageFormat.format(ClueOperateLogEnum.TRANSFER.getContent(),oldAgentName,newAgentName));
            bizClueLog.setCreateTime(new Date());
            bizClueLog.setInputTime(new Date());
            return bizClueLog;
        }).collect(Collectors.toList());

        if(!logList.isEmpty()){
            //批量插入线索动态
            logService.batchInsertBizClueLog(logList);
        }

    }

    /**
     * 通过cluePoolTypes查询校验符合条件的线索id
     * @param bizCluePostDTO
     * @param cluePoolTypes 线索池类型
     * @return
     */
    public List<Long> verifyPostClueId(BizCluePostDTO bizCluePostDTO,List<Integer> cluePoolTypes){
        List<Long> passIds = new ArrayList<>();
        //如果线索id数组为空，返回空list
        if(bizCluePostDTO.getClueIds() == null || bizCluePostDTO.getClueIds().length == 0){
            return passIds;
        }
        bizCluePostDTO.setCluePoolTypes(cluePoolTypes);
        return bizClueMapper.getVerifyPassIds(bizCluePostDTO);
    }


    /**
     * 新增线索
     * 
     * @param bizClue 线索
     * @return 结果
     */
    @Override
    public int insertBizClue(BizClue bizClue)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        bizClue.setCreateTime(DateUtils.getNowDate());
        bizClue.setCreateBy(loginUser.getUsername());
        bizClue.setCreateById(loginUser.getUserId());
        return bizClueMapper.insertBizClue(bizClue);
    }

    @Override
    public int batchInsertBizClue(List<BizClue> bizClueList) {
        return bizClueMapper.batchInsertBizClue(bizClueList);
    }

    /**
     * 修改线索
     * 
     * @param bizClue 线索
     * @return 结果
     */
    @Override
    public int updateBizClue(BizClue bizClue)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        bizClue.setUpdateBy(loginUser.getUsername());
        bizClue.setUpdateById(String.valueOf(loginUser.getUserId()));
        bizClue.setUpdateUserSystem("1");
        bizClue.setUpdateTime(DateUtils.getNowDate());
        return bizClueMapper.updateBizClue(bizClue);
    }

    /**
     * 批量删除线索
     * 
     * @param clueIds 需要删除的线索主键
     * @return 结果
     */
    @Override
    public int deleteBizClueByClueIds(Long[] clueIds)
    {
        return bizClueMapper.deleteBizClueByClueIds(clueIds);
    }

    /**
     * 删除线索信息
     * 
     * @param clueId 线索主键
     * @return 结果
     */
    @Override
    public int deleteBizClueByClueId(Long clueId)
    {
        return bizClueMapper.deleteBizClueByClueId(clueId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult importData(MultipartFile file, Long agentId, Integer exclusiveShopId) throws Exception {

        ExcelUtil<BizClueExcel> util = new ExcelUtil<BizClueExcel>(BizClueExcel.class);
        List<BizClueExcel> bizClueExcels = util.importExcel(file.getInputStream());

        if(bizClueExcels.isEmpty()){
            return AjaxResult.error("上传文件无数据！");
        }
        //错误列表
        List<Map<String,Object>> errorMsgList = bizClueExcels.stream()
                .filter(item-> item.getErrorMsg() != null)
                .map(data ->{
                    Map<String,Object> map = new HashMap<>();
                    map.put("rowNum",data.getRowNum());
                    map.put("errorMsg",data.getErrorMsg());
                    return map;
                })
                .collect(Collectors.toList());
        //基础校验-是否必填
        if(!errorMsgList.isEmpty()){
            return AjaxResult.error(HttpStatus.IMPORT_VERIFY,"导入失败！",errorMsgList);
        }
        //获取所有渠道信息，转map，通过channelName取channelId，避免在循环中去数据库查询渠道id
        List<BizChannel> bizChannels = channelService.selectBizChannelList(new BizChannel());
        //校验渠道是否维护
        if(bizChannels.isEmpty()){
            return AjaxResult.error("导入失败！无渠道数据，请先录入渠道");
        }
        Map<String, Long> channelNameMap = bizChannels.stream()
                .collect(Collectors.toMap(BizChannel::getChannelName, BizChannel::getChannelId, (k1, k2) -> k1));
        //当前用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //下标，对应excel行号
        AtomicInteger index = new AtomicInteger(1);
        //转换bizClueList
        if(agentId==null){
            ExclusiveShop exclusiveShop = exclusiveShopMapper.selectExclusiveShopById(exclusiveShopId);
            if(exclusiveShop==null){
                return AjaxResult.error("未查询到该专卖店信息");
            }
            agentId=exclusiveShop.getAgentId();
        }
        Long newAgentId=agentId;
        List<BizClue> bizClueList = bizClueExcels.stream().map(item -> {
            BizClue bizClue = new BizClue();
            bizClue.setAgentId(newAgentId);
            bizClue.setExclusiveShopId(exclusiveShopId);
            bizClue.setCustomerName(item.getCustomerName());
            bizClue.setPhone(item.getPhone());
            bizClue.setChannelId(channelNameMap.get(item.getChannelName()));
            bizClue.setChannelName(item.getChannelName());
            bizClue.setCustomerSource(item.getCustomerSource());
            bizClue.setStatus(ClueStatusEnum.NOT_FOLLOW.getValue());
            bizClue.setCluePoolType(CluePoolTypeEnum.COMMON_POOL.getValue());
            bizClue.setCreateById(loginUser.getUserId());
            bizClue.setCreateTime(new Date());
            bizClue.setCreateBy(loginUser.getUsername());
            bizClue.setFollowNumber(0);
            //递增1
            index.getAndIncrement();
            //校验excel中的渠道在数据库是否存在
            if(channelNameMap.get(item.getChannelName()) == null){
                Map<String,Object> map = new HashMap<>();
                map.put("rowNum",index.get());
                map.put("errorMsg","此渠道["+item.getChannelName()+"]在数据库不存在！");
                errorMsgList.add(map);
            }
            return bizClue;
        }).collect(Collectors.toList());
        //业务校验-渠道是否存在
        if(!errorMsgList.isEmpty()){
            return AjaxResult.error(HttpStatus.IMPORT_VERIFY,"导入失败！",errorMsgList);
        }
        //数据不为空，批量插入
        if(bizClueList.size() > 0){
            Long oldClueId = bizClueMapper.selectMaxId();
            if(oldClueId==null){
                oldClueId=0L;
            }
            //批量插入
            bizClueMapper.batchInsertBizClue(bizClueList);
            Long newClueId = bizClueMapper.selectMaxId();

            List<Long> clueIds = new ArrayList<>();
            for(int i = oldClueId.intValue()+1 ; i <= newClueId ; i++){
                clueIds.add(Long.valueOf(i));
            }

            //记录线索动态
            this.batchInsertClueLog(clueIds.toArray(new Long[clueIds.size()]),
                    ClueOperateLogEnum.IMPORT.getOperate(),
                    ClueOperateLogEnum.IMPORT.getContent());

        }
        return AjaxResult.success("导入成功！共导入"+bizClueList.size()+"条数据！");
    }



}
