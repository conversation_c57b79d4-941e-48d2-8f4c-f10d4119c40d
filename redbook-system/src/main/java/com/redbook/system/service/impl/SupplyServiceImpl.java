package com.redbook.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.Supply;
import com.redbook.system.mapper.SupplyMapper;
import com.redbook.system.service.ISupplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-11-14
 */
@Service
public class SupplyServiceImpl extends ServiceImpl<SupplyMapper, Supply> implements ISupplyService
{
    @Autowired
    private SupplyMapper supplyMapper;

    /**
     * 查询供应商
     * 
     * @param id 供应商主键
     * @return 供应商
     */
    @Override
    public Supply selectSupplyById(Long id)
    {
        return supplyMapper.selectSupplyById(id);
    }

    /**
     * 查询供应商列表
     * 
     * @param supply 供应商
     * @return 供应商
     */
    @Override
    public List<Supply> selectSupplyList(Supply supply)
    {
        return supplyMapper.selectSupplyList(supply);
    }

    @Override
    public List<Supply> selectList() {
        return supplyMapper.selectList();
    }

    /**
     * 新增供应商
     * 
     * @param supply 供应商
     * @return 结果
     */
    @Override
    public int insertSupply(Supply supply)
    {
        supply.setCreateTime(DateUtils.getNowDate());
        return supplyMapper.insertSupply(supply);
    }

    /**
     * 修改供应商
     * 
     * @param supply 供应商
     * @return 结果
     */
    @Override
    public int updateSupply(Supply supply)
    {
        supply.setUpdateTime(DateUtils.getNowDate());
        return supplyMapper.updateSupply(supply);
    }

    /**
     * 批量删除供应商
     * 
     * @param ids 需要删除的供应商主键
     * @param username
     * @return 结果
     */
    @Override
    public int deleteSupplyByIds(Long[] ids, String username)
    {
        if (ids == null || ids.length == 0) {
            return 0;
        }
        List<Long> canNotDeleteIds = supplyMapper.selectCanNotDeleteIds();
        List<Long> idsList = Arrays.asList(ids);
        List<Long> collect = idsList.stream().filter(s -> !canNotDeleteIds.contains(s)).collect(Collectors.toList());
        Long[] arr = {};
        if (CollectionUtil.isEmpty(collect)) {
            throw ServiceException.fail("所选供应商不可删除");
        }
        Long[] newIds = collect.toArray(arr);
        return supplyMapper.deleteSupplyByIds(newIds,username);
    }

    /**
     * 删除供应商信息
     * 
     * @param id 供应商主键
     * @return 结果
     */
    @Override
    public int deleteSupplyById(Long id)
    {
        return supplyMapper.deleteSupplyById(id);
    }
}
