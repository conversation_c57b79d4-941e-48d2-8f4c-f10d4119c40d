package com.redbook.system.service.postSale;

import com.redbook.system.domain.PostSaleConfig;

import java.util.List;

/**
 * 售后配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface IPostSaleConfigService 
{
    /**
     * 查询售后配置
     * 
     * @param id 售后配置主键
     * @return 售后配置
     */
     PostSaleConfig selectPostSaleConfigById(Long id);

    /**
     * 查询售后配置列表
     * 
     * @param postSaleConfig 售后配置
     * @return 售后配置集合
     */
     List<PostSaleConfig> selectPostSaleConfigList(PostSaleConfig postSaleConfig);

    /**
     * 新增售后配置
     * 
     * @param postSaleConfig 售后配置
     * @return 结果
     */
     int insertPostSaleConfig(PostSaleConfig postSaleConfig);

    /**
     * 修改售后配置
     * 
     * @param postSaleConfig 售后配置
     * @return 结果
     */
     int updatePostSaleConfig(PostSaleConfig postSaleConfig);

    /**
     * 批量删除售后配置
     * 
     * @param ids 需要删除的售后配置主键集合
     * @return 结果
     */
     int deletePostSaleConfigByIds(Long[] ids);

    /**
     * 删除售后配置信息
     * 
     * @param id 售后配置主键
     * @return 结果
     */
     int deletePostSaleConfigById(Long id);
}
