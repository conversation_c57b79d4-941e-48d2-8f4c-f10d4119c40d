package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.annotation.DataScope;
import com.redbook.common.constant.UserConstants;
import com.redbook.common.core.domain.entity.AgentInfo;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.domain.entity.SysRole;
import com.redbook.common.core.domain.entity.SysUser;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.IdentityGenerator;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.StringUtils;
import com.redbook.common.utils.bean.BeanValidators;
import com.redbook.common.utils.spring.SpringUtils;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.SysPost;
import com.redbook.system.domain.SysUserPost;
import com.redbook.system.domain.SysUserRole;
import com.redbook.system.domain.vo.AgentParamVo;
import com.redbook.system.domain.vo.ExclusiveShopVo;
import com.redbook.system.domain.vo.MangerVo;
import com.redbook.system.mapper.*;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.IExclusiveShopService;
import com.redbook.system.service.ISysConfigService;
import com.redbook.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    protected Validator validator;

    @Autowired @Lazy
    private IAgentService agentService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    AgentMappingMapper agentMappingMapper;
    @Autowired
    private IExclusiveShopService exclusiveShopService;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d" , userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        List<SysUser> sysUsers = userMapper.selectUserList(user);
        sysUsers.forEach(sysUser -> {
            if (sysUser.getLimitAgent()!=null&&sysUser.getLimitAgent()) {
                if(sysUser.getRoleId()!=null && sysUser.getRoleId()==106){//店长，查询关联的专卖店列表
                    sysUser.setExclusiveShopList(exclusiveShopService.selectExclusiveShopList(ExclusiveShop.builder().managerId(sysUser.getUserId()).build()));
                }else{//其他，查询关联的代理商信息
                    sysUser.setAgentList(agentService.getByUserId(sysUser.getUserId()).stream().map(agent -> AgentInfo.builder().id(agent.getId()).aid(agent.getAid()).name(agent.getName()).build()).collect(Collectors.toList()));
                }
            }
        });
        return sysUsers;
    }

    @Override
    public List<SysUser> selectExclusiveShopManagerList(SysUser user) {
        user.setRoleId(106L);
        user.setUserType("01");
        if(user.getAgentId()!=null){
            user.setDeptId(user.getAgentId());
        }
        List<SysUser> sysUsers = userMapper.selectUserList(user);
        sysUsers.forEach(sysUser -> {
            if (sysUser.getLimitAgent()!=null&&sysUser.getLimitAgent()) {
                sysUser.setExclusiveShopList(exclusiveShopService.selectExclusiveShopList(ExclusiveShop.builder().managerId(sysUser.getUserId()).build()));
            }
        });
        return sysUsers;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d" , userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d" , userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public String getUniqueUserName() {
        while (true) {
            LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
            String userName = UserConstants.USER_PRE + IdentityGenerator.randomString(6);
            sysUserLambdaQueryWrapper.eq(SysUser::getUserName, userName);
            if (count(sysUserLambdaQueryWrapper) < 1) {
                return userName;
            }
        }
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {

        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {
        agentService.deleteAgentCache();

        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), new Long[]{user.getRoleId()});
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        agentService.deleteAgentCache();
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        //删除用户与代理商关联
        agentMappingMapper.deleteAgentMappingByUserIds(userIds);
        agentService.deleteAgentCache();
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    this.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(user);
                    checkUserDataScope(user.getUserId());
                    user.setUpdateBy(operName);
                    this.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public AgentParamVo getParams(Long userId) {
        Long currentUserId = SecurityUtils.getUserId();
        Object object = redisCache.getCacheObject("user_params:" + currentUserId + ":paramId:" + userId);
        if (object!=null)return (AgentParamVo)object;
        SysUser currentUser = selectUserById(currentUserId);
        AgentParamVo paramVo = AgentParamVo.builder().hasManagerMenu(!currentUser.getLimitAgent()).build();
        Agent param = new Agent();
        //店长，隐藏选择代理商下拉列表
        if(currentUser.getRole().getRoleId().intValue()==106){
            paramVo.setHasAgentMenu(false);
            //店长根据专卖店获取代理商列表。
            List<AgentInfo> agentInfoList = new ArrayList<>();
            Set<Long> agentIdSet = exclusiveShopService.selectExclusiveShopList(ExclusiveShop.builder().managerId(currentUser.getUserId()).build()).stream().map(exclusiveShop -> exclusiveShop.getAgentId()).collect(Collectors.toSet());
            agentIdSet.forEach(agentId -> {
                Agent agent = agentService.selectAgentById(agentId);
                agentInfoList.add(AgentInfo.builder().id(agent.getId()).aid(agent.getAid()).parentId(agent.getParentId()).agreementLevel(agent.getAgreementLevel().intValue()).name(agent.getName()).agreementEnd(agent.getAgreementEnd()).accountEnd(agent.getAccountEnd()).build());
            });
            paramVo.setAgentInfoList(agentInfoList);
        }else{
            paramVo.setHasAgentMenu(true);
            if (!currentUser.getLimitAgent()) {
                SysUser sysUser = new SysUser();
                SysRole sysRole = new SysRole();
                sysRole.setLimitAgent(true);
                sysUser.setUserType("00");
                sysUser.setRole(sysRole);
                //查询当前可以选择的经理
                List<SysUser> sysUsers = SpringUtils.getAopProxy(this).selectUserList(sysUser);
                param.setmId(userId);
                paramVo.setMangerList(sysUsers.stream().map(user -> MangerVo.builder().userId(user.getUserId()).username(user.getUserName()).nickname(user.getNickName()).build()).collect(Collectors.toList()));
                build(paramVo, param);
            }else {
                param.setmId(currentUserId);
                build(paramVo, param);
            }
        }
        //缓存参数数据
        redisCache.setCacheObject("user_params:" + currentUserId + ":paramId:" + userId,paramVo,60, TimeUnit.MINUTES);
        return paramVo;
    }

    private void build(AgentParamVo paramVo, Agent param) {
        paramVo.setAgentInfoList(agentService.selectAgentList(param).stream().filter(agent -> agent.getStatus()==0).map(agent -> AgentInfo.builder().id(agent.getId()).aid(agent.getAid()).parentId(agent.getParentId()).agreementLevel(agent.getAgreementLevel().intValue()).name(agent.getName()).agreementEnd(agent.getAgreementEnd()).accountEnd(agent.getAccountEnd()).build()).collect(Collectors.toList()));
    }

    @Override
    public SysUser selectUserByPhonenumber(String phone) {
        return userMapper.checkPhoneUnique(phone);
    }

    @Override
    public List<ExclusiveShopVo> getExclusiveShopList(Long agentId) {
        Long userId = SecurityUtils.getUserId();
        String key = "user_exclusive_shops0326:" + userId + ":agentId:" + agentId;
        Object object = redisCache.getCacheObject(key);
        if (object!=null){
            return (List<ExclusiveShopVo>)object;
        }
        SysUser currentUser = selectUserById(userId);
        ExclusiveShop exclusiveShop = new ExclusiveShop();
        //店长，只允许看到自己的店
        if(currentUser.getRole().getRoleId().intValue()==106){
            exclusiveShop.setManagerId(userId);
        }else if (currentUser.getLimitAgent()) {
            exclusiveShop.setmId(userId);
        }
        if (agentId != null) {
            exclusiveShop.setAgentId(agentId);
        }
        //只看未停止运营专卖店。
        exclusiveShop.setStatus(0);
        List<ExclusiveShopVo> list = exclusiveShopService.selectExclusiveShopList(exclusiveShop).stream().map(eS ->
                ExclusiveShopVo.builder()
                        .agentId(eS.getAgentId())
                        .exclusiveShopId(eS.getId())
                        .exclusiveShopName(eS.getName())
                        .exclusiveShopManagerId(eS.getManagerId())
                        .exclusiveShopManagerName(eS.getManagerName())
                        .exclusiveShopLevel(eS.getLevel())
                        .build()).collect(Collectors.toList());
        redisCache.setCacheObject(key,list,60, TimeUnit.MINUTES);
        return list;
    }
}
