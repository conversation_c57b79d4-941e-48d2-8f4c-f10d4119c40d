package com.redbook.system.service;

import com.redbook.system.domain.RedbookTabletOperateLog;

import java.util.List;

/**
 * 小红本操作日志Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface IRedbookTabletOperateLogService 
{
    /**
     * 查询小红本操作日志
     * 
     * @param id 小红本操作日志主键
     * @return 小红本操作日志
     */
     RedbookTabletOperateLog selectRedbookTabletOperateLogById(Long id);

    /**
     * 查询小红本操作日志列表
     * 
     * @param redbookTabletOperateLog 小红本操作日志
     * @return 小红本操作日志集合
     */
     List<RedbookTabletOperateLog> selectRedbookTabletOperateLogList(RedbookTabletOperateLog redbookTabletOperateLog);

    /**
     * 新增小红本操作日志
     * 
     * @param redbookTabletOperateLog 小红本操作日志
     * @return 结果
     */
     int insertRedbookTabletOperateLog(RedbookTabletOperateLog redbookTabletOperateLog);

    /**
     * 修改小红本操作日志
     * 
     * @param redbookTabletOperateLog 小红本操作日志
     * @return 结果
     */
     int updateRedbookTabletOperateLog(RedbookTabletOperateLog redbookTabletOperateLog);

    /**
     * 批量删除小红本操作日志
     * 
     * @param ids 需要删除的小红本操作日志主键集合
     * @return 结果
     */
     int deleteRedbookTabletOperateLogByIds(Long[] ids);

    /**
     * 删除小红本操作日志信息
     * 
     * @param id 小红本操作日志主键
     * @return 结果
     */
     int deleteRedbookTabletOperateLogById(Long id);
}
