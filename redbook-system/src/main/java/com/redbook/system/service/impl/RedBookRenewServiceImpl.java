package com.redbook.system.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.redbook.common.constant.HttpStatus;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.enums.TransactionFundType;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.StringUtils;
import com.redbook.common.utils.sign.Md5Utils;
import com.redbook.system.domain.*;
import com.redbook.system.domain.dto.MemberRenewChargingDto;
import com.redbook.system.domain.kids.KidUserInfo;
import com.redbook.system.domain.model.UserBean;
import com.redbook.system.mapper.IMemberRenewDao;
import com.redbook.system.mapper.ITydRenewCardDao;
import com.redbook.system.mq.QueueConstants;
import com.redbook.system.service.*;
import com.redbook.system.service.kids.IKidUserInfoService;
import com.redbook.system.util.DateUtil;
import com.redbook.system.util.RemoteUtil;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;


@Service
public class RedBookRenewServiceImpl implements RedBookRenewService {
    @Autowired
    ITydRenewCardDao renewCardDao;
    @Autowired
    IMemberRenewDao memberRenewDao;
    @Autowired
    IAgentService agentService;
    @Autowired
    RemoteUtil remoteUtil;
    @Autowired
    IUserInfoService userInfoService;
    @Autowired
    private IAgentSalesDayService salesDayService;

    @Value("${redbookHost.reloadSession}")
    private String reloadSessionUrl;
    @Value("${kidHost.reloadSession}")
    private String kidReloadSessionUrl;
    @Autowired
    private IAgentTransactionInfoService agentTransactionInfoService;

    @Autowired
    IAgentAccountService agentAccountService;
    @Autowired
    IElectronicRenewCardService electronicRenewCardService;
    @Autowired
    RocketMQTemplate rocketMQTemplate;
    @Autowired
    RedisCache redisCache;
    @Autowired
    IExclusiveShopService exclusiveShopService;
    @Autowired
    IExclusiveShopMemberPriceService shopMemberPriceService;
    @Autowired
    IExclusiveShopTransactionInfoService shopTransactionInfoService;
    @Autowired
    IRedbookAgentRebateService redbookAgentRebateService;
    @Autowired
    IKidUserInfoService kidUserInfoService;

    /**
     * 0元转：
     *
     * @param contractorId
     * @param payAgentId
     * @param userId
     * @param moreTime
     * @return
     */
    @Transactional
    @Override
    public boolean zeroRenew(Long contractorId, long payAgentId, String userId, String moreTime) {
        Integer memberType = 1;
        UserInfo redBookUser = userInfoService.selectUserInfoByUserId(userId);
        Agent agent = agentService.getByAid(redBookUser.getAid());
        UserBean pcUserBean = remoteUtil.getUserByUserId(userId);
        if (pcUserBean == null || redBookUser == null) {
            return false;
        }
        Map<String, List<Integer>> purchaseProgramList = remoteUtil.getPurchaseProgramList(pcUserBean.getPurchase());
        if (purchaseProgramList == null || purchaseProgramList.get("parentIdList") == null) {
            return false;
        }
        List<Integer> parentIdList = purchaseProgramList.get("parentIdList");

        if (redBookUser.getMemberType() >= 1) {//0元转之前需要是体验会员
            return false;
        }
        //续费之前的日期
        Calendar renewBeforeDate = Calendar.getInstance();
        if (pcUserBean.getLastAccessDate().getTime() >= DateUtil.getToday0HoursTime()) {//如果没有过期  就按过期的日期上增加时长
            renewBeforeDate.setTime(pcUserBean.getLastAccessDate());
        }

        Date renewAfterDate = calculateDates(renewBeforeDate.getTime(), moreTime, pcUserBean.getLastAccessDate().getTime() < DateUtil.getToday0HoursTime());
        Date stage1ExpirationDate = null;
        Date stage2ExpirationDate = null;
        Date stage3ExpirationDate = null;

        if (pcUserBean.getPurchase().equals("1,6,") || pcUserBean.getPurchase().equals("2,6,") || pcUserBean.getPurchase().equals("30,6,")) {//小学
            stage1ExpirationDate = renewAfterDate;
        } else if (pcUserBean.getPurchase().equals("2,3,6,")) {//小升初
            stage1ExpirationDate = renewAfterDate;
            stage2ExpirationDate = renewAfterDate;
        } else if (pcUserBean.getPurchase().equals("3,6,") || pcUserBean.getPurchase().equals("4,6,") || pcUserBean.getPurchase().equals("40,6,")) {//初中
            stage2ExpirationDate = renewAfterDate;
        } else if (pcUserBean.getPurchase().equals("50,6,")) {//高中
            stage3ExpirationDate = renewAfterDate;
        } else {
            if (parentIdList.contains(30)) {
                stage1ExpirationDate = renewAfterDate;
            }
            if (parentIdList.contains(40)) {
                stage2ExpirationDate = renewAfterDate;
            }
            if (parentIdList.contains(50)) {
                stage3ExpirationDate = renewAfterDate;
            }
        }

        //生成续费记录
        Map<String, Object> renewRecordMap = new HashMap<>();
        renewRecordMap.put("contractorId", contractorId);
        renewRecordMap.put("payAreaId", payAgentId);
        renewRecordMap.put("agentId", agent.getId());
        renewRecordMap.put("exclusiveShopId", redBookUser.getExclusiveShopId());
        renewRecordMap.put("memberUserId", userId);
        renewRecordMap.put("firstRenew", -1);
        renewRecordMap.put("renewStage", 0);
        renewRecordMap.put("renewTimeLen", 0);
        renewRecordMap.put("buyTimeLen", "");
        renewRecordMap.put("presentTimeLen", moreTime);
        renewRecordMap.put("userName", redBookUser.getUsername());
        renewRecordMap.put("mobilePhone", redBookUser.getMobilePhone());
        renewRecordMap.put("renewBeforeDate", DateUtil.dateToString(renewBeforeDate.getTime()));
        renewRecordMap.put("renewAfterDate", DateUtil.dateToString(renewAfterDate));
        renewRecordMap.put("payMoney", 0.00);
        renewRecordMap.put("indentNumber", "");
        //专卖店支付，插入专卖店的金额。
        renewRecordMap.put("payExclusiveShopId", null);
        renewRecordMap.put("payExclusiveShopMoney", BigDecimal.ZERO);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        renewRecordMap.put("payTime", sdf.format(new Date()));
        this.memberRenewDao.addMemberRenewRecord(renewRecordMap);

        //更新到期时间
        String curDate = DateUtil.getCurrDate("yyyy-MM-dd");
        renewCardDao.renewRedBookUser(userId, memberType,
                DateUtil.dateToString(renewAfterDate),
                stage1ExpirationDate == null ? null : DateUtil.dateToString(stage1ExpirationDate),
                stage2ExpirationDate == null ? null : DateUtil.dateToString(stage2ExpirationDate),
                stage3ExpirationDate == null ? null : DateUtil.dateToString(stage3ExpirationDate), null, null, null, null,
                curDate, curDate);
//        renewCardDao.resetRedBookUserMainCourseInfo(userId, -1);
        remoteUtil.updateUserAgentId(userId, agent.getAid());
        remoteUtil.updateOldPCUserRedBookMemberType(userId, memberType);
//        renewCardDao.updateRedBookUserAgentId(userId, agent.getAid());
        reloadUserSession(userId);
        //2023-07-27 00:00:00之后的续费记录，销量统计到账号所在区域上。
        salesDayService.updateAgentSalesDay(agent.getId(), redBookUser.getExclusiveShopId(), DateUtil.dateToString(new Date()), true, false, 0, 0, 1);
        return true;
    }


    /**
     * 0元转pcvip会员
     *
     * @param userId
     * @return
     */
    @Transactional
    @Override
    public boolean zeroRenewPCVIP(String userId) {
        UserInfo redBookUser = userInfoService.selectUserInfoByUserId(userId);
        Agent agent = agentService.getByAid(redBookUser.getAid());
        UserBean pcUserBean = remoteUtil.getUserByUserId(userId);
        if (pcUserBean == null || redBookUser == null) {
            return false;
        }
        if (redBookUser.getMemberType() >= 1) {//0元转之前需要是体验会员
            return false;
        }
        if (!pcUserBean.getPurchase().equals("vip,") || !pcUserBean.getCommedityCode().equals("901")) {
            return false;
        }
        Date expirationDate = pcUserBean.getLastAccessDate();

        //更新到期时间
        String curDate = DateUtil.getCurrDate("yyyy-MM-dd");
        renewCardDao.renewRedBookUser(userId, 2,
                DateUtil.dateToString(expirationDate),
                DateUtil.dateToString(expirationDate),
                DateUtil.dateToString(expirationDate),
                DateUtil.dateToString(expirationDate),
                DateUtil.dateToString(expirationDate),
                DateUtil.dateToString(expirationDate),
                null, null,
                curDate, curDate);
        reloadUserSession(userId);
        salesDayService.updateAgentSalesDay(agent.getId(), redBookUser.getExclusiveShopId(), DateUtil.dateToString(new Date()), true, false, 0, 0, 1);
        return true;
    }

    /**
     * 扣费
     *
     * @param agentId
     * @param payPasswd
     * @param renewTimeLen
     * @return
     */
    @Transactional
    @Override
    public MemberRenewChargingDto charging(Long agentId, Integer exclusiveShopId, String userName, String payPasswd, int renewStage, int renewTimeLen) {
        if (agentId == null && exclusiveShopId == null) {
            return MemberRenewChargingDto.builder().errorCode(HttpStatus.MEMBER_RENEW_ERROR).errorMsg("无法续费，代理商不存在！").build();
        }
        ExclusiveShop exclusiveShop = null;
        //专卖店续费价格
        BigDecimal exclusiveShopPrice = new BigDecimal(10000);
        //如果是专卖店续费
        if (agentId == null) {
            exclusiveShop = exclusiveShopService.selectExclusiveShopById(exclusiveShopId);
            if (exclusiveShop == null) {
                return MemberRenewChargingDto.builder().errorCode(HttpStatus.MEMBER_RENEW_ERROR).errorMsg("无法续费，专卖店不存在！").build();
            }
            if (!exclusiveShopService.checkPayPassword(exclusiveShopId, payPasswd)) {
                return MemberRenewChargingDto.builder().errorCode(HttpStatus.MEMBER_RENEW_ERROR).errorMsg("支付密码错误！").build();
            }
            ExclusiveShopMemberPrice exclusiveShopMemberPrice = shopMemberPriceService.selectExclusiveShopMemberPrice(exclusiveShopId, renewStage, renewTimeLen);
            if (exclusiveShopMemberPrice == null) {
                return MemberRenewChargingDto.builder().errorCode(HttpStatus.MEMBER_RENEW_ERROR).errorMsg("无法续费，未设置会员价格，请联系代理商！").build();
            }
            exclusiveShopPrice = new BigDecimal(exclusiveShopMemberPrice.getPrice());
            if (exclusiveShop.getAccountBalance().compareTo(exclusiveShopPrice) < 0) {
                return MemberRenewChargingDto.builder().errorCode(HttpStatus.BALANCE_NOT_ENOUGH).errorMsg("专卖店余额不足，请充值！").build();
            }
            agentId = exclusiveShop.getAgentId();
        }
        Agent agent = agentService.selectAgentById(agentId);
        if (agent == null) {
            return MemberRenewChargingDto.builder().errorCode(HttpStatus.MEMBER_RENEW_ERROR).errorMsg("无法续费，代理商不存在！").build();
        }
        //如果是专卖店支付，不验证代理商密码。
        if (exclusiveShopId == null) {
            AgentAccount agentAccount = agentAccountService.selectAgentAccountByAgentId(agentId);
            if (agentAccount.getPayPassword() == null) {
                return MemberRenewChargingDto.builder().errorCode(HttpStatus.MEMBER_RENEW_ERROR).errorMsg("请先设置支付密码！").build();
            }
            if (payPasswd == null || !agentAccount.getPayPassword().equals(Md5Utils.hash(payPasswd))) {
                return MemberRenewChargingDto.builder().errorCode(HttpStatus.MEMBER_RENEW_ERROR).errorMsg("支付密码错误！").build();
            }
        }
        String indentNumber = String.valueOf(System.currentTimeMillis());
        //首先判断代理商是否已经开启新的价格体系
        boolean rebateStatus = agentAccountService.getAgentRebateStatus(agentId);
        BigDecimal agentPrice = new BigDecimal(0);
        if (rebateStatus) {
            agentPrice = redbookAgentRebateService.getAgentRebatePrice(agentId, renewTimeLen);
            BigDecimal memberBalance = agentAccountService.getBalance(agentId, TransactionFundType.NEW_MEMBER);
            BigDecimal redBookVoucherBalance = agentAccountService.getBalance(agentId, TransactionFundType.NEW_COUPON);
            BigDecimal totalBalance = memberBalance.add(redBookVoucherBalance);//可用于购买余额
            if (totalBalance.compareTo(agentPrice) < 0) {
                return MemberRenewChargingDto.builder().errorCode(HttpStatus.BALANCE_NOT_ENOUGH).errorMsg("代理商余额不足，请充值！").build();
            }
            {
                BigDecimal memberPay;
                BigDecimal redBookVoucherPay;
                BigDecimal surplusPay = agentPrice;
                //扣款顺序：会员款、代金券
                //扣会员款
                if (surplusPay.compareTo(BigDecimal.ZERO) > 0 && memberBalance.compareTo(BigDecimal.ZERO) == 1) {
                    if (memberBalance.compareTo(surplusPay) > 0) {
                        memberBalance = memberBalance.subtract(surplusPay);
                        memberPay = surplusPay;
                        surplusPay = BigDecimal.ZERO;
                    } else {
                        memberPay = memberBalance;
                        memberBalance = BigDecimal.ZERO;
                        surplusPay = surplusPay.subtract(memberPay);
                    }
                    agentAccountService.updateAccount(TransactionFundType.NEW_MEMBER, agentId, memberBalance);
                    agentTransactionInfoService.addTransactionInfo(agentId, "会员续费支出", TransactionFundType.NEW_MEMBER, indentNumber, memberPay, memberBalance, 1, 0, 0, userName + " 续费");
                }
                //扣代金券
                if (surplusPay.compareTo(BigDecimal.ZERO) > 0 && redBookVoucherBalance.compareTo(BigDecimal.ZERO) > 0) { //剩余应付大于0，续约款余额大于0
                    if (redBookVoucherBalance.compareTo(surplusPay) == 1) {
                        redBookVoucherPay = surplusPay;
                        redBookVoucherBalance = redBookVoucherBalance.subtract(surplusPay);
                    } else {
                        redBookVoucherPay = redBookVoucherBalance;
                        redBookVoucherBalance = BigDecimal.ZERO;
                    }
                    agentAccountService.updateAccount(TransactionFundType.NEW_COUPON, agentId, redBookVoucherBalance);
                    agentTransactionInfoService.addTransactionInfo(agentId, "会员续费支出", TransactionFundType.NEW_COUPON, indentNumber, redBookVoucherPay, redBookVoucherBalance, 1, 0, 0, userName + " 续费");
                }
            }
            //针对续费代理商，计算代理商动态价格优惠级别
            redbookAgentRebateService.addSaleAgentRebate(agentId, renewTimeLen);
        } else {
            //代理商续费价格
            if (renewTimeLen == 1) {
                agentPrice = new BigDecimal("86.00");
            } else if (renewTimeLen == 3) {
                agentPrice = new BigDecimal("196.00");
            } else if (renewTimeLen == 6) {
                agentPrice = new BigDecimal("296.00");
            } else if (renewTimeLen == 12) {
                agentPrice = new BigDecimal("456.00");
                //战略合作伙伴有年课程续费优惠
                Integer pricingScheme = agent.getPriceScheme();
                if (pricingScheme != null && pricingScheme == 1) {
                    agentPrice = new BigDecimal("396.00");
                }
            }

            BigDecimal memberBalance = agentAccountService.getBalance(agentId, TransactionFundType.MEMBER);
            BigDecimal redBookVoucherBalance = agentAccountService.getBalance(agentId, TransactionFundType.COUPON);
            BigDecimal totalBalance = memberBalance.add(redBookVoucherBalance);//可用于购买余额
            if (totalBalance.compareTo(agentPrice) < 0) {
                return MemberRenewChargingDto.builder().errorCode(HttpStatus.BALANCE_NOT_ENOUGH_NEW_PRICE).errorMsg("区域余额不足，请在账户管理转移到期初货款").build();
            }
            //扣代理商费用
            {
                BigDecimal memberPay;
                BigDecimal redBookVoucherPay;
                BigDecimal surplusPay = agentPrice;
                //扣款顺序：会员款、代金券
                //扣会员款
                if (surplusPay.compareTo(BigDecimal.ZERO) > 0 && memberBalance.compareTo(BigDecimal.ZERO) == 1) {
                    if (memberBalance.compareTo(surplusPay) > 0) {
                        memberBalance = memberBalance.subtract(surplusPay);
                        memberPay = surplusPay;
                        surplusPay = BigDecimal.ZERO;
                    } else {
                        memberPay = memberBalance;
                        memberBalance = BigDecimal.ZERO;
                        surplusPay = surplusPay.subtract(memberPay);
                    }
                    agentAccountService.updateAccount(TransactionFundType.MEMBER, agentId, memberBalance);
                    agentTransactionInfoService.addTransactionInfo(agentId, "会员续费支出", TransactionFundType.MEMBER, indentNumber, memberPay, memberBalance, 1, 0, 0, userName + " 续费");
                }
                //扣代金券
                if (surplusPay.compareTo(BigDecimal.ZERO) > 0 && redBookVoucherBalance.compareTo(BigDecimal.ZERO) > 0) { //剩余应付大于0，续约款余额大于0
                    if (redBookVoucherBalance.compareTo(surplusPay) == 1) {
                        redBookVoucherPay = surplusPay;
                        redBookVoucherBalance = redBookVoucherBalance.subtract(surplusPay);
                    } else {
                        redBookVoucherPay = redBookVoucherBalance;
                        redBookVoucherBalance = BigDecimal.ZERO;
                    }
                    agentAccountService.updateAccount(TransactionFundType.COUPON, agentId, redBookVoucherBalance);
                    agentTransactionInfoService.addTransactionInfo(agentId, "会员续费支出", TransactionFundType.COUPON, indentNumber, redBookVoucherPay, redBookVoucherBalance, 1, 0, 0, userName + " 续费");
                }
            }

        }
        //扣专卖店费用
        if (exclusiveShop != null) {
            BigDecimal accountBalance = exclusiveShop.getAccountBalance();
            BigDecimal surplusBalance = accountBalance.subtract(exclusiveShopPrice);
            //更新账户余额
            exclusiveShopService.updateExclusiveShop(ExclusiveShop.builder().id(exclusiveShopId).accountBalance(surplusBalance).build());
            //插入交易记录
            shopTransactionInfoService.insertExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo.builder().exclusiveShopId(exclusiveShopId)
                    .indentNumber(indentNumber).transactionType("会员续费支出").money(exclusiveShopPrice).paymentType(1).balance(surplusBalance).remark(userName + " 续费").build());
        }
        return MemberRenewChargingDto.builder().payAgentId(agentId).payExclusiveShopId(exclusiveShopId).indentNumber(indentNumber).exclusiveShopPay(exclusiveShopPrice).agentPay(agentPrice).build();

    }

    @Override
    public AjaxResult renewKids(long payAgentId, Integer payExclusiveShopId, String indentNumber, String userId, int renewStage, int renewTimeLen, BigDecimal agentPay, BigDecimal exclusiveShopPay) {
        KidUserInfo redBookUser = kidUserInfoService.selectUserInfoByUserId(userId);
        Agent agentBean = agentService.getByAid(redBookUser.getAid());
        int firstRenew = 0;
        boolean isFirstRenew = memberRenewDao.getMemberPayRenewNum(userId) == 0;
        if (isFirstRenew) {
            firstRenew = 1;
        }
        String buyTimeLen = renewTimeLen + " MONTH";
        String presentTimeLen = "";
        //续费之前的日期
        Calendar renewBeforeDate = Calendar.getInstance();
        Date renewAfterDate = null;
        String firstPurchaseDate = null;
        String lastPurchaseDate = DateUtil.getCurrDate("yyyy-MM-dd");
        if (redBookUser.getFirstPurchaseDate() != null) {
            firstPurchaseDate = DateUtil.getCurrDate("yyyy-MM-dd");
        }
        renewBeforeDate = Calendar.getInstance();
        if (redBookUser.getExpirationDate().getTime() >= DateUtil.getToday0HoursTime()) {//如果没有过期  就按过期的日期上增加时长
            renewBeforeDate.setTime(redBookUser.getExpirationDate());
        }
        Date expirationDate = redBookUser.getExpirationDate();
        boolean stageIsExpire = false;
        if (expirationDate.getTime() <= DateUtil.getToday0HoursTime()) {
            stageIsExpire = true;
            expirationDate = new Date();
        }
        renewBeforeDate.setTime(expirationDate);
        expirationDate = calculateDates(expirationDate, buyTimeLen, stageIsExpire);
        renewAfterDate = expirationDate;
        //会员类型
        if (redBookUser.getMemberType() < 2 || redBookUser.getExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {//如果不是是vip、超级会员 或者已过期，需要改会员类型
            redBookUser.setMemberType(1);
            if (buyTimeLen.equals("6 MONTH") || buyTimeLen.equals("12 MONTH")) {
                redBookUser.setMemberType(2);
            }
        }

        //生成续费记录
        Map<String, Object> renewRecordMap = new HashMap<>();
        renewRecordMap.put("contractorId", SecurityUtils.getLoginUser().getUserId());//操作人
        renewRecordMap.put("payAreaId", payAgentId);
        renewRecordMap.put("agentId", agentBean.getId());
        renewRecordMap.put("exclusiveShopId", redBookUser.getExclusiveShopId());
        renewRecordMap.put("memberUserId", userId);
        renewRecordMap.put("firstRenew", firstRenew);
        renewRecordMap.put("renewStage", renewStage);
        renewRecordMap.put("renewTimeLen", renewTimeLen);
        renewRecordMap.put("buyTimeLen", buyTimeLen);
        renewRecordMap.put("userName", redBookUser.getUserName());
        renewRecordMap.put("mobilePhone", redBookUser.getMobilePhone());
        renewRecordMap.put("presentTimeLen", presentTimeLen);
        renewRecordMap.put("indentNumber", indentNumber);
        renewRecordMap.put("renewBeforeDate", DateUtil.dateToString(renewBeforeDate.getTime()));
        renewRecordMap.put("renewAfterDate", DateUtil.dateToString(renewAfterDate));
        renewRecordMap.put("payMoney", agentPay);
        Date payTime = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        renewRecordMap.put("payTime", sdf.format(payTime));
        //专卖店支付，插入专卖店的金额。
        if (payExclusiveShopId != null) {
            renewRecordMap.put("payExclusiveShopId", payExclusiveShopId);
        }
        if (exclusiveShopPay == null) {
            exclusiveShopPay = BigDecimal.ZERO;
        }
        renewRecordMap.put("payExclusiveShopMoney", exclusiveShopPay);


        this.memberRenewDao.addMemberRenewRecord(renewRecordMap);

        //更新到期时间
        kidUserInfoService.renewKidUser(redBookUser.getUserId(), redBookUser.getMemberType(),
                DateUtil.dateToString(renewAfterDate),
                firstPurchaseDate, lastPurchaseDate);
        //2023-07-27 00:00:00之后的续费记录，销量统计到账号所在区域上。
        salesDayService.updateAgentSalesDay(agentBean.getId(), redBookUser.getExclusiveShopId(), DateUtil.dateToString(new Date()), false, isFirstRenew, renewTimeLen, renewStage, 1);
        return AjaxResult.success("续费成功");
    }


    @Transactional
    @Override
    public AjaxResult renewRedBook(long payAgentId, Integer payExclusiveShopId, String indentNumber, String userId, int renewStage, int renewTimeLen,
                                   int moreTimeLen, BigDecimal agentPay,
                                   BigDecimal exclusiveShopPay, String electronicRenewCardNumber, Integer activityContentId, Boolean joinActivity, Boolean sendCoin) {
        UserInfo redBookUser = userInfoService.selectUserInfoByUserId(userId);
        Agent agentBean = agentService.getByAid(redBookUser.getAid());
        int firstRenew = 0;
        boolean isFirstRenew = memberRenewDao.getMemberPayRenewNum(userId) == 0;
        if (isFirstRenew) {
            firstRenew = 1;
        }
        String buyTimeLen = renewTimeLen + " MONTH";
        String presentTimeLen = "";
        if (renewTimeLen == 1) {
            if (firstRenew == 1 && moreTimeLen == 1) {
                presentTimeLen = "7 DAY";
            }
        } else if (renewTimeLen == 3) {
            if (firstRenew == 1 && moreTimeLen == 1) {
                presentTimeLen = "15 DAY";
            }
        } else if (renewTimeLen == 6) {
            if (firstRenew == 1 && moreTimeLen == 1) {
                presentTimeLen = "1 MONTH";
            }
        } else if (renewTimeLen == 12) {
            if (firstRenew == 1) {
                if (moreTimeLen == 1) {
                    presentTimeLen = "1 MONTH";
                } else if (moreTimeLen == 2) {
                    presentTimeLen = "2 MONTH";
                } else if (moreTimeLen == 3) {
                    presentTimeLen = "3 MONTH";
                }
            }
        }
        //续费之前的日期
        Calendar renewBeforeDate = Calendar.getInstance();
        Date renewAfterDate = null;
        Date stage1ExpirationDate = null;
        Date stage2ExpirationDate = null;
        Date stage3ExpirationDate = null;
        Date stage4ExpirationDate = null;
        Date stage5ExpirationDate = null;
        Date stage11ExpirationDate = null;
        Date stage21ExpirationDate = null;
        List<Integer> parentIdList = new ArrayList<>();
        String firstPurchaseDate = null;
        String lastPurchaseDate = DateUtil.getCurrDate("yyyy-MM-dd");
        boolean experienceUser = false;//是否是体验用户
   /*     //记录续费之前的用户信息
        RenewPreUserInfo renewPreUserInfo = RenewPreUserInfo.builder().userId(redBookUser.getUserId()).memberType(redBookUser.getMemberType()).expirationDate(redBookUser.getExpirationDate()).stage1ExpirationDate(redBookUser.getStage1ExpirationDate())
                .stage2ExpirationDate(redBookUser.getStage2ExpirationDate()).stage3ExpirationDate(redBookUser.getStage3ExpirationDate())
                .stage4ExpirationDate(redBookUser.getStage4ExpirationDate()).stage5ExpirationDate(redBookUser.getStage5ExpirationDate())
                .stage11ExpirationDate(redBookUser.getStage11ExpirationDate()).stage21ExpirationDate(redBookUser.getStage21ExpirationDate())
                .stage(redBookUser.getStage()).firstPurchaseDate(redBookUser.getFirstPurchaseDate()).lastPurchaseDate(redBookUser.getLastPurchaseDate()).build();*/
        if (redBookUser.getMemberType() == 0 || redBookUser.getMemberType() == -1) {//体验会员需要修改购买信息
            experienceUser = true;
            renewBeforeDate = Calendar.getInstance();
            firstPurchaseDate = DateUtil.getCurrDate("yyyy-MM-dd");
            if (redBookUser.getMemberType() == -1) { //PC端转的账号
                UserBean pcUserBean = remoteUtil.getUserByUserId(userId);
                if (!pcUserBean.getCommedityCode().equals("202")) {//PC端转的账号（202是最开始用的账号，这类账号属于小红本注册的）
                    if (pcUserBean.getLastAccessDate().getTime() >= DateUtil.getToday0HoursTime()) {//如果PC端没有过期  就按过期的日期上增加时长
                        renewBeforeDate.setTime(pcUserBean.getLastAccessDate());
                    }
                    if (redBookUser.getExpirationDate().getTime() >= pcUserBean.getLastAccessDate().getTime()) {//如果小红本的体验时间大于PC端到期时间，就按照小红本体验到期日期增加时长
                        if (redBookUser.getExpirationDate().getTime() >= DateUtil.getToday0HoursTime()) {//如果没有过期  就按过期的日期上增加时长
                            renewBeforeDate.setTime(redBookUser.getExpirationDate());
                        } else {
                            renewBeforeDate.setTime(new Date()); //如果过期  就按当前续费的日期上增加时长
                        }
                    }
                    //判断在原先pc端购买的课程
                    Map<String, List<Integer>> purchaseProgramList = remoteUtil.getPurchaseProgramList(pcUserBean.getPurchase());
                    try {
                        if (null != purchaseProgramList && purchaseProgramList.get("parentIdList") != null) {
                            parentIdList = purchaseProgramList.get("parentIdList");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //如果在pc购买的课程不包含续费的学段，只保留续费后的学段。
                    if (renewStage == 1 && !parentIdList.contains(30) ||
                            renewStage == 2 && !parentIdList.contains(40) ||
                            renewStage == 3 && !parentIdList.contains(50)) {
                        parentIdList = new ArrayList<>();
                    }
                } else {//是小红本注册的code是202直接续费的情况
                    if (redBookUser.getExpirationDate().getTime() >= DateUtil.getToday0HoursTime()) {//如果没有过期  就按过期的日期上增加时长
                        renewBeforeDate.setTime(redBookUser.getExpirationDate());
                    } else {
                        renewBeforeDate.setTime(new Date()); //如果过期  就按当前续费的日期上增加时长
                    }
                }
            } else {//直接注册的小红本账号
                if (redBookUser.getExpirationDate().getTime() >= DateUtil.getToday0HoursTime()) {//如果没有过期  就按过期的日期上增加时长
                    renewBeforeDate.setTime(redBookUser.getExpirationDate());
                } else {
                    renewBeforeDate.setTime(new Date()); //如果过期  就按当前续费的日期上增加时长
                }
            }
            renewAfterDate = calculateDates(renewBeforeDate.getTime(), buyTimeLen, redBookUser.getExpirationDate().getTime() < DateUtil.getToday0HoursTime());
            renewAfterDate = calculateDates(renewAfterDate, presentTimeLen, false);
            switch (renewStage) {
                case 1:
                    parentIdList.add(30);
                    break;
                case 2:
                    parentIdList.add(40);
                    break;
                case 3:
                    parentIdList.add(50);
                    break;
                case 4:
                    parentIdList.add(60);
                    break;
                case 5:
                    parentIdList.add(70);
                    break;
                case 11:
                    parentIdList.add(110);
                    break;
                case 21:
                    parentIdList.add(210);
                    break;
            }
            if (!parentIdList.isEmpty()) {
                if (parentIdList.contains(30)) {
                    stage1ExpirationDate = renewAfterDate;
                }
                if (parentIdList.contains(40)) {
                    stage2ExpirationDate = renewAfterDate;
                }
                if (parentIdList.contains(50)) {
                    stage3ExpirationDate = renewAfterDate;
                }
                if (parentIdList.contains(60)) {
                    stage4ExpirationDate = renewAfterDate;
                }
                if (parentIdList.contains(70)) {
                    stage5ExpirationDate = renewAfterDate;
                }
                if (parentIdList.contains(110)) {
                    stage11ExpirationDate = renewAfterDate;
                }
                if (parentIdList.contains(210)) {
                    stage21ExpirationDate = renewAfterDate;
                }
            }
            if (agentBean != null) {
                remoteUtil.updateUserAgentId(userId, agentBean.getAid());
//                renewCardDao.updateRedBookUserAid(userId, agentBean.getAid());
            }
        } else {//老用户 //续费之前的日期
            renewBeforeDate = Calendar.getInstance();
            if (redBookUser.getExpirationDate().getTime() >= DateUtil.getToday0HoursTime()) {//如果没有过期  就按过期的日期上增加时长
                renewBeforeDate.setTime(redBookUser.getExpirationDate());
            }
            stage1ExpirationDate = redBookUser.getStage1ExpirationDate();
            stage2ExpirationDate = redBookUser.getStage2ExpirationDate();
            stage3ExpirationDate = redBookUser.getStage3ExpirationDate();
            stage4ExpirationDate = redBookUser.getStage4ExpirationDate();
            stage5ExpirationDate = redBookUser.getStage5ExpirationDate();
            stage11ExpirationDate = redBookUser.getStage11ExpirationDate();
            stage21ExpirationDate = redBookUser.getStage21ExpirationDate();
            boolean stageIsExpire = false;
            switch (renewStage) {
                case 1:
                    if (redBookUser.getStage1ExpirationDate() == null || redBookUser.getStage1ExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {
                        stageIsExpire = true;
                        stage1ExpirationDate = new Date();
                    }
                    renewBeforeDate.setTime(stage1ExpirationDate);
                    stage1ExpirationDate = calculateDates(stage1ExpirationDate, buyTimeLen, stageIsExpire);
                    stage1ExpirationDate = calculateDates(stage1ExpirationDate, presentTimeLen, false);
                    renewAfterDate = stage1ExpirationDate;
                    break;
                case 2:
                    if (redBookUser.getStage2ExpirationDate() == null || redBookUser.getStage2ExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {
                        stageIsExpire = true;
                        stage2ExpirationDate = new Date();
                    }
                    renewBeforeDate.setTime(stage2ExpirationDate);
                    stage2ExpirationDate = calculateDates(stage2ExpirationDate, buyTimeLen, stageIsExpire);
                    stage2ExpirationDate = calculateDates(stage2ExpirationDate, presentTimeLen, false);
                    renewAfterDate = stage2ExpirationDate;
                    break;
                case 3:
                    if (redBookUser.getStage3ExpirationDate() == null || redBookUser.getStage3ExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {
                        stageIsExpire = true;
                        stage3ExpirationDate = new Date();
                    }
                    renewBeforeDate.setTime(stage3ExpirationDate);
                    stage3ExpirationDate = calculateDates(stage3ExpirationDate, buyTimeLen, stageIsExpire);
                    stage3ExpirationDate = calculateDates(stage3ExpirationDate, presentTimeLen, false);
                    renewAfterDate = stage3ExpirationDate;
                    break;
                case 4:
                    if (redBookUser.getStage4ExpirationDate() == null || redBookUser.getStage4ExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {
                        stageIsExpire = true;
                        stage4ExpirationDate = new Date();
                    }
                    renewBeforeDate.setTime(stage4ExpirationDate);
                    stage4ExpirationDate = calculateDates(stage4ExpirationDate, buyTimeLen, stageIsExpire);
                    stage4ExpirationDate = calculateDates(stage4ExpirationDate, presentTimeLen, false);
                    renewAfterDate = stage4ExpirationDate;
                    break;
                case 5:
                    if (redBookUser.getStage5ExpirationDate() == null || redBookUser.getStage5ExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {
                        stageIsExpire = true;
                        stage5ExpirationDate = new Date();
                    }
                    renewBeforeDate.setTime(stage5ExpirationDate);
                    stage5ExpirationDate = calculateDates(stage5ExpirationDate, buyTimeLen, stageIsExpire);
                    stage5ExpirationDate = calculateDates(stage5ExpirationDate, presentTimeLen, false);
                    renewAfterDate = stage5ExpirationDate;
                    break;
                case 11:
                    if (redBookUser.getStage11ExpirationDate() == null || redBookUser.getStage11ExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {
                        stageIsExpire = true;
                        stage11ExpirationDate = new Date();
                    }
                    renewBeforeDate.setTime(stage11ExpirationDate);
                    stage11ExpirationDate = calculateDates(stage11ExpirationDate, buyTimeLen, stageIsExpire);
                    stage11ExpirationDate = calculateDates(stage11ExpirationDate, presentTimeLen, false);
                    renewAfterDate = stage11ExpirationDate;
                    break;
                case 21:
                    if (redBookUser.getStage21ExpirationDate() == null || redBookUser.getStage21ExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {
                        stageIsExpire = true;
                        stage21ExpirationDate = new Date();
                    }
                    renewBeforeDate.setTime(stage21ExpirationDate);
                    stage21ExpirationDate = calculateDates(stage21ExpirationDate, buyTimeLen, stageIsExpire);
                    stage21ExpirationDate = calculateDates(stage21ExpirationDate, presentTimeLen, false);
                    renewAfterDate = stage21ExpirationDate;
                    break;
            }
        }
        //会员类型
        if (redBookUser.getMemberType() < 2 || redBookUser.getExpirationDate().getTime() < DateUtil.getToday0HoursTime()) {//如果不是是vip、超级会员 或者已过期，需要改会员类型
            redBookUser.setMemberType(1);
            if (buyTimeLen.equals("6 MONTH") || buyTimeLen.equals("12 MONTH")) {
                redBookUser.setMemberType(2);
            }
        }
        redBookUser.setStage1ExpirationDate(stage1ExpirationDate);
        redBookUser.setStage2ExpirationDate(stage2ExpirationDate);
        redBookUser.setStage3ExpirationDate(stage3ExpirationDate);
        redBookUser.setStage4ExpirationDate(stage4ExpirationDate);
        redBookUser.setStage5ExpirationDate(stage5ExpirationDate);
        redBookUser.setStage11ExpirationDate(stage11ExpirationDate);
        redBookUser.setStage21ExpirationDate(stage21ExpirationDate);
        this.computeExpirationDate(redBookUser);
        //使用电子续费卡
        if (StringUtils.isNotBlank(electronicRenewCardNumber)) {
            if (!electronicRenewCardService.useCard(electronicRenewCardNumber, userId, redBookUser.getUsername(), renewTimeLen, moreTimeLen, renewStage, renewBeforeDate.getTime(), renewAfterDate)) {
                return AjaxResult.error("电子续费卡使用失败！");
            }
        }
        //生成续费记录
        Map<String, Object> renewRecordMap = new HashMap<>();
        renewRecordMap.put("contractorId", SecurityUtils.getLoginUser().getUserId());//操作人
        renewRecordMap.put("payAreaId", payAgentId);
        renewRecordMap.put("agentId", agentBean.getId());
        renewRecordMap.put("exclusiveShopId", redBookUser.getExclusiveShopId());
        renewRecordMap.put("memberUserId", userId);
        renewRecordMap.put("firstRenew", firstRenew);
        renewRecordMap.put("renewStage", renewStage);
        renewRecordMap.put("renewTimeLen", renewTimeLen);
        renewRecordMap.put("buyTimeLen", buyTimeLen);
        renewRecordMap.put("userName", redBookUser.getUsername());
        renewRecordMap.put("mobilePhone", redBookUser.getMobilePhone());
        renewRecordMap.put("presentTimeLen", presentTimeLen);
        renewRecordMap.put("indentNumber", indentNumber);
        renewRecordMap.put("renewBeforeDate", DateUtil.dateToString(renewBeforeDate.getTime()));
        renewRecordMap.put("renewAfterDate", DateUtil.dateToString(renewAfterDate));
        renewRecordMap.put("payMoney", agentPay);
        Date payTime = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        renewRecordMap.put("payTime", sdf.format(payTime));
        renewRecordMap.put("electronicRenewCardNumber", electronicRenewCardNumber);
        //专卖店支付，插入专卖店的金额。
        if (payExclusiveShopId != null) {
            renewRecordMap.put("payExclusiveShopId", payExclusiveShopId);
        }
        if (exclusiveShopPay == null) {
            exclusiveShopPay = BigDecimal.ZERO;
        }
        renewRecordMap.put("payExclusiveShopMoney", exclusiveShopPay);
        //活动
        renewRecordMap.put("activityContentId", activityContentId);
       /* //续费之前的用户信息
        renewRecordMap.put("renewPreUserInfo", JSONObject.toJSONString(renewPreUserInfo));*/
        if (sendCoin != null && sendCoin) {
            try {
                if (activityContentId != null && activityContentId != -1) {
                    Map<String, String> map = new HashMap<>();
                    map.put("activityContentId", String.valueOf(activityContentId));
                    map.put("memberUserId", String.valueOf(userId));
                    map.put("joinActivity", String.valueOf(joinActivity));
                    map.put("sendCoin", String.valueOf(sendCoin));
                    map.put("renewTimeLen", String.valueOf(renewTimeLen));
                    rocketMQTemplate.convertAndSend(QueueConstants.ACTVITY_RENEW_MESSAGE, JSONObject.toJSONString(map));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            renewRecordMap.put("activityContentId", null);
        }

        this.memberRenewDao.addMemberRenewRecord(renewRecordMap);

        //更新到期时间
        renewCardDao.renewRedBookUser(redBookUser.getUserId(), redBookUser.getMemberType(),
                DateUtil.dateToString(redBookUser.getExpirationDate()),
                redBookUser.getStage1ExpirationDate() == null ? null : DateUtil.dateToString(redBookUser.getStage1ExpirationDate()),
                redBookUser.getStage2ExpirationDate() == null ? null : DateUtil.dateToString(redBookUser.getStage2ExpirationDate()),
                redBookUser.getStage3ExpirationDate() == null ? null : DateUtil.dateToString(redBookUser.getStage3ExpirationDate()),
                redBookUser.getStage4ExpirationDate() == null ? null : DateUtil.dateToString(redBookUser.getStage4ExpirationDate()),
                redBookUser.getStage5ExpirationDate() == null ? null : DateUtil.dateToString(redBookUser.getStage5ExpirationDate()),
                redBookUser.getStage11ExpirationDate() == null ? null : DateUtil.dateToString(redBookUser.getStage11ExpirationDate()),
                redBookUser.getStage21ExpirationDate() == null ? null : DateUtil.dateToString(redBookUser.getStage21ExpirationDate()),
                firstPurchaseDate, lastPurchaseDate);
        if (renewStage != redBookUser.getStage()) {//如果用户新买的学段不是之前的学段，需要重新选择课程。
            renewCardDao.resetRedBookUserMainCourseInfo(userId, renewStage);
        }
        remoteUtil.updateOldPCUserRedBookMemberType(userId, redBookUser.getMemberType());
        reloadUserSession(userId);
        //2023-07-27 00:00:00之后的续费记录，销量统计到账号所在区域上。
        salesDayService.updateAgentSalesDay(agentBean.getId(), redBookUser.getExclusiveShopId(), DateUtil.dateToString(new Date()), false, isFirstRenew, renewTimeLen, renewStage, 1);
        return AjaxResult.success("续费成功");
    }

    /**
     * 计算到期日期。
     *
     * @param userInfo
     */
    private void computeExpirationDate(UserInfo userInfo) {
        Long expirationDateTime = 0L;
        long currentTime = System.currentTimeMillis();
        List<String> stages = Arrays.asList("Stage1", "Stage2", "Stage3", "Stage4", "Stage5", "Stage11", "Stage21");
        for (String stage : stages) {
            try {
                Date expirationDate = getExpirationDate(userInfo, stage);
                if (expirationDate != null) {
                    if (expirationDate.getTime() < currentTime) {
                        setExpirationDate(userInfo, stage, null);
                    }
                    expirationDateTime = Math.max(expirationDateTime, expirationDate.getTime());
                }
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                // 处理异常
                e.printStackTrace();
            }
        }
        userInfo.setExpirationDate(new Date(expirationDateTime));
    }

    private Date getExpirationDate(UserInfo userInfo, String stage) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        return (Date) userInfo.getClass().getMethod("get" + stage + "ExpirationDate").invoke(userInfo);
    }

    private void setExpirationDate(UserInfo userInfo, String stage, Date date) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        userInfo.getClass().getMethod("set" + stage + "ExpirationDate", Date.class).invoke(userInfo, date);
    }


    @Override
    /**
     * 退费处理 - 使用新事务避免数据源切换冲突
     * @param id 续费记录ID
     * @return 退费是否成功
     */
//    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Boolean refundRedBook(Integer id) {
        Map<String, Object> memberRenewRecordById = memberRenewDao.getMemberRenewRecordById(id);
        if (memberRenewRecordById == null) {
            return false;
        }
        Long agentId = Long.parseLong(memberRenewRecordById.get("agent_id").toString());
        Integer exclusiveShopId = (memberRenewRecordById.get("exclusive_shop_id") == null ? null : Integer.parseInt(memberRenewRecordById.get("exclusive_shop_id").toString()));
        Integer payExclusiveShopId = (memberRenewRecordById.get("pay_exclusive_shop_id") == null ? null : Integer.parseInt(memberRenewRecordById.get("pay_exclusive_shop_id").toString()));
        String renewTimeLen = memberRenewRecordById.get("buy_time_len").toString();
        String presentTimeLen = memberRenewRecordById.get("present_time_len").toString();
        int renewStage = NumberUtil.parseInt(memberRenewRecordById.get("renew_stage").toString());
        String memberUserId = memberRenewRecordById.get("member_user_id").toString();
        String firstRenew = memberRenewRecordById.get("first_renew").toString();
        String isRefund = memberRenewRecordById.get("is_refund").toString();
        String ecardNumber = memberRenewRecordById.containsKey("ecard_number") ? memberRenewRecordById.get("ecard_number") + "" : "";
        if ("Y".equals(isRefund)) {
            return false;
        }

        Date payTime = DateUtil.stringtoDate(memberRenewRecordById.get("pay_time").toString(), DateUtil.FORMAT_ONE);
        //超过30天的禁止退费。
        if ((System.currentTimeMillis() - payTime.getTime()) / 1000 > 2592000) {
            return false;
        }
        if (renewStage==99){
            KidUserInfo kidUserInfo = kidUserInfoService.selectUserInfoByUserId(memberUserId);
            if (kidUserInfo == null) {
                return false;
            }
            if (renewTimeLen.equals("0 DAY") || renewTimeLen.isEmpty()) {
                return false;
            }
            kidUserInfo.setExpirationDate(minusDates(minusDates(kidUserInfo.getExpirationDate(), presentTimeLen), renewTimeLen));
            kidUserInfoService.renewKidUser(memberUserId, kidUserInfo.getMemberType(), DateUtil.dateToString(kidUserInfo.getExpirationDate()),null,null);
            reloadKidSession(memberUserId);
        }else {
            UserInfo userInfo = userInfoService.selectUserInfoByUserId(memberUserId);
            if (renewTimeLen.equals("0 DAY") || renewTimeLen.isEmpty()) {
                return false;
            }
            if (userInfo == null) {
                return false;
            }
            if (renewStage <= 0) {
                return false;
            }
            switch (renewStage) {
                case 1:
                    userInfo.setStage1ExpirationDate(minusDates(minusDates(userInfo.getStage1ExpirationDate(), presentTimeLen), renewTimeLen));
                    break;
                case 2:
                    userInfo.setStage2ExpirationDate(minusDates(minusDates(userInfo.getStage2ExpirationDate(), presentTimeLen), renewTimeLen));
                    break;
                case 3:
                    userInfo.setStage3ExpirationDate(minusDates(minusDates(userInfo.getStage3ExpirationDate(), presentTimeLen), renewTimeLen));
                    break;
                case 4:
                    userInfo.setStage4ExpirationDate(minusDates(minusDates(userInfo.getStage4ExpirationDate(), presentTimeLen), renewTimeLen));
                    break;
                case 5:
                    userInfo.setStage5ExpirationDate(minusDates(minusDates(userInfo.getStage5ExpirationDate(), presentTimeLen), renewTimeLen));
                    break;
                case 11:
                    userInfo.setStage11ExpirationDate(minusDates(minusDates(userInfo.getStage11ExpirationDate(), presentTimeLen), renewTimeLen));
                    break;
                case 21:
                    userInfo.setStage21ExpirationDate(minusDates(minusDates(userInfo.getStage21ExpirationDate(), presentTimeLen), renewTimeLen));
                    break;
                default:
                    return false;
            }
            this.computeExpirationDate(userInfo);
            //更新到期时间
            renewCardDao.renewRedBookUser(userInfo.getUserId(), userInfo.getMemberType(),
                    DateUtil.dateToString(userInfo.getExpirationDate()),
                    userInfo.getStage1ExpirationDate() == null ? null : DateUtil.dateToString(userInfo.getStage1ExpirationDate()),
                    userInfo.getStage2ExpirationDate() == null ? null : DateUtil.dateToString(userInfo.getStage2ExpirationDate()),
                    userInfo.getStage3ExpirationDate() == null ? null : DateUtil.dateToString(userInfo.getStage3ExpirationDate()),
                    userInfo.getStage4ExpirationDate() == null ? null : DateUtil.dateToString(userInfo.getStage4ExpirationDate()),
                    userInfo.getStage5ExpirationDate() == null ? null : DateUtil.dateToString(userInfo.getStage5ExpirationDate()),
                    userInfo.getStage11ExpirationDate() == null ? null : DateUtil.dateToString(userInfo.getStage11ExpirationDate()),
                    userInfo.getStage21ExpirationDate() == null ? null : DateUtil.dateToString(userInfo.getStage21ExpirationDate()),
                    null, null);
            reloadUserSession(memberUserId);
        }
        //退费逻辑不做调整
        if (StringUtils.isNotBlank(ecardNumber) && !"null".equalsIgnoreCase(ecardNumber)) {
            electronicRenewCardService.refundCard(ecardNumber);
        } else {
            String indentNumber = memberRenewRecordById.get("indent_number").toString();
            String transactionType = "会员续费退款";
            //代理商退款
            if (!agentAccountService.refund(indentNumber, transactionType, memberUserId)) {
                throw ServiceException.fail("会员续费退款失败！");
            }
            //首先判断代理商是否已经开启新的价格体系
            boolean rebateStatus = agentAccountService.getAgentRebateStatus(agentId);
            if (rebateStatus) {
                redbookAgentRebateService.subtractSaleAgentRebate(agentId, NumberUtil.parseInt(renewTimeLen));
            }
            //专卖店退款
            if (payExclusiveShopId != null) {
                ExclusiveShop exclusiveShop = exclusiveShopService.selectExclusiveShopById(payExclusiveShopId);
                BigDecimal payExclusiveShopMoney = new BigDecimal(memberRenewRecordById.get("pay_exclusive_shop_money").toString());
                BigDecimal accountBalance = exclusiveShop.getAccountBalance().add(payExclusiveShopMoney);
                //更新账户余额
                exclusiveShopService.updateExclusiveShop(ExclusiveShop.builder().id(payExclusiveShopId).accountBalance(accountBalance).build());
                //插入交易记录
                shopTransactionInfoService.insertExclusiveShopTransactionInfo(ExclusiveShopTransactionInfo.builder().exclusiveShopId(payExclusiveShopId)
                        .indentNumber(indentNumber).transactionType(transactionType).money(payExclusiveShopMoney).paymentType(0).balance(accountBalance).build());
            }
        }
        //每日销量删除
        salesDayService.updateAgentSalesDay(agentId, exclusiveShopId, DateUtil.dateToString(payTime), false, firstRenew.equals("1"), NumberUtil.parseInt(renewTimeLen), renewStage, -1);
        //续费记录修改
        return memberRenewDao.refundMemberRenewRecord(id) > 0;
    }


    /**
     * 重新加载用户信息
     *
     * @param userId
     */
    private void reloadUserSession(String userId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId", userId);
        HttpUtil.get(reloadSessionUrl, hashMap);
    }

    private void reloadKidSession(String userId){
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId", userId);
        HttpUtil.get(kidReloadSessionUrl, hashMap);
    }


    /**
     * 计算续费后日期
     *
     * @param beforeDate
     * @param timeLen
     * @param isExpire
     * @return
     */
    private Date calculateDates(Date beforeDate, String timeLen, boolean isExpire) {
        if (timeLen.equals("0 DAY") || timeLen.equals("")) {
            return beforeDate;
        }
        //计算续费到了那天
        Calendar renewAfterDate = Calendar.getInstance();
        renewAfterDate.setTime(beforeDate);
        String[] timeLenArr = timeLen.split(" ");
        if (timeLenArr[1].equals("DAY")) {
            renewAfterDate.add(Calendar.DAY_OF_MONTH, Integer.parseInt(timeLenArr[0]));
        } else if (timeLenArr[1].equals("MONTH")) {
            renewAfterDate.add(Calendar.MONTH, Integer.parseInt(timeLenArr[0]));
        } else if (timeLenArr[1].equals("YEAR")) {
            renewAfterDate.add(Calendar.YEAR, Integer.parseInt(timeLenArr[0]));
        }
        //if(isExpire) {//如果过期了，续费后的日期需要减少一天
        //   renewAfterDate.add(Calendar.DAY_OF_MONTH, -1);
        //}
        return renewAfterDate.getTime();
    }

    /**
     * 计算续费后日期
     *
     * @param beforeDate
     * @param timeLen
     * @return
     */
    private Date minusDates(Date beforeDate, String timeLen) {
        if (timeLen.equals("0 DAY") || timeLen.equals("")) {
            return beforeDate;
        }
        Calendar renewAfterDate = Calendar.getInstance();
        renewAfterDate.setTime(beforeDate);
        String[] timeLenArr = timeLen.split(" ");
        if (timeLenArr[1].equals("DAY")) {
            renewAfterDate.add(Calendar.DAY_OF_MONTH, -Integer.parseInt(timeLenArr[0]));
        } else if (timeLenArr[1].equals("MONTH")) {
            renewAfterDate.add(Calendar.MONTH, -Integer.parseInt(timeLenArr[0]));
        } else if (timeLenArr[1].equals("YEAR")) {
            renewAfterDate.add(Calendar.YEAR, -Integer.parseInt(timeLenArr[0]));
        }
        return renewAfterDate.getTime();
    }
}
