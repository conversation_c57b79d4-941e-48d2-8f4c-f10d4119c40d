package com.redbook.system.service;

import com.redbook.system.domain.*;
import com.redbook.system.domain.dto.ActivityBaseUserDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 小小单词王Service接口
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
public interface IActivityUserService {

    /**
     * 查询用户火星币/纪念币记录列表
     *
     * @param activityUserCoinRecord 用户火星币/纪念币记录
     * @return 用户火星币/纪念币记录集合
     */
    List<ActivityUserCoinRecord> selectActivityUserCoinRecordList(ActivityUserCoinRecord activityUserCoinRecord);


    /**
     * 修改用户火星币/纪念币记录
     *
     * @param activityUserCoinRecord 用户火星币/纪念币记录
     * @return 结果
     */
    int updateActivityUserCoinRecord(ActivityUserCoinRecord activityUserCoinRecord);


    Integer getActivityId();

    /**
     * 第三届小小单词王
     *
     * @param activityUser
     * @return
     */
    List<ActivityUser> listV3(ActivityUser activityUser);


    List<ActivityDrawRecord> myWinningRecodList(ActivityBaseUserDTO activityBaseUserDTO);

    List<ActivityUserWordDayRecord> studyDetail(ActivityBaseUserDTO activityBaseUserDTO);


    //地球保护
    List<ActivityUser> selectActivityUserList(ActivityUser activityUser);

    String getActivityBaseName(Integer activityBaseId);

    void export(List<ActivityUser> list, HttpServletResponse response, ActivityUser activityUser);


    List<FiveStarUserRecord> fiveStarUserRecordList(FiveStarUserRecord fiveStarUserRecord);

    Boolean exchangeFiveStar(String userId, Integer stage);

    List<ActivityDcwUserAnswerRecord> scoreRecordList(ActivityBaseUserDTO activityBaseUserDTO);
}
