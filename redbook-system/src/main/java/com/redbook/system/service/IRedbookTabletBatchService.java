package com.redbook.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.redbook.system.domain.RedbookTabletBatch;

import java.util.List;

/**
 * 批次Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-15
 */
public interface IRedbookTabletBatchService extends IService<RedbookTabletBatch>
{
    /**
     * 查询批次
     * 
     * @param id 批次主键
     * @return 批次
     */
     RedbookTabletBatch selectRedbookTabletBatchById(Long id);

    /**
     * 查询批次列表
     * 
     * @param redbookTabletBatch 批次
     * @return 批次集合
     */
     List<RedbookTabletBatch> selectRedbookTabletBatchList(RedbookTabletBatch redbookTabletBatch);

    /**
     * 新增批次
     * 
     * @param redbookTabletBatch 批次
     * @return 结果
     */
     int insertRedbookTabletBatch(RedbookTabletBatch redbookTabletBatch);

    /**
     * 修改批次
     * 
     * @param redbookTabletBatch 批次
     * @return 结果
     */
     int updateRedbookTabletBatch(RedbookTabletBatch redbookTabletBatch);

    /**
     * 批量删除批次
     * 
     * @param ids 需要删除的批次主键集合
     * @param username
     * @return 结果
     */
     int deleteRedbookTabletBatchByIds(Long[] ids, String username);

    /**
     * 删除批次信息
     * 
     * @param id 批次主键
     * @return 结果
     */
     int deleteRedbookTabletBatchById(Long id);
}
