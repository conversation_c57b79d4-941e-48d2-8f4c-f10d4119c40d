package com.redbook.system.service;

import com.redbook.system.domain.TeacherFeedbackRecord;

import java.util.List;

/**
 * 教师反馈信息记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface ITeacherFeedbackRecordService 
{
    /**
     * 查询教师反馈信息记录
     * 
     * @param id 教师反馈信息记录主键
     * @return 教师反馈信息记录
     */
     TeacherFeedbackRecord selectTeacherFeedbackRecordById(Long id);

    /**
     * 查询教师反馈信息记录列表
     * 
     * @param teacherFeedbackRecord 教师反馈信息记录
     * @return 教师反馈信息记录集合
     */
     List<TeacherFeedbackRecord> selectTeacherFeedbackRecordList(TeacherFeedbackRecord teacherFeedbackRecord);

    /**
     * 新增教师反馈信息记录
     * 
     * @param teacherFeedbackRecord 教师反馈信息记录
     * @return 结果
     */
     int insertTeacherFeedbackRecord(TeacherFeedbackRecord teacherFeedbackRecord);

    /**
     * 修改教师反馈信息记录
     *
     * @param loginUser
     * @param teacherFeedbackRecord 教师反馈信息记录
     * @return 结果
     */
     int updateTeacherFeedbackRecord(TeacherFeedbackRecord teacherFeedbackRecord);

    /**
     * 批量删除教师反馈信息记录
     * 
     * @param ids 需要删除的教师反馈信息记录主键集合
     * @return 结果
     */
     int deleteTeacherFeedbackRecordByIds(Long[] ids);

    /**
     * 删除教师反馈信息记录信息
     * 
     * @param id 教师反馈信息记录主键
     * @return 结果
     */
     int deleteTeacherFeedbackRecordById(Long id);
}
