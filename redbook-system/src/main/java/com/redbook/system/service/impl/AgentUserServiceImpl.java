package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.IdentityGenerator;
import com.redbook.common.utils.sign.Md5Utils;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.AgentUser;
import com.redbook.system.domain.dto.TeachChangeAgentDto;
import com.redbook.system.mapper.AgentMapper;
import com.redbook.system.mapper.AgentUserMapper;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.IAgentUserService;
import com.redbook.system.service.IExclusiveShopService;
import com.redbook.system.service.IUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 老师管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
@Service
public class AgentUserServiceImpl extends ServiceImpl<AgentUserMapper, AgentUser> implements IAgentUserService {
    @Autowired
    private AgentUserMapper agentUserMapper;
    @Autowired
    private IAgentService agentService;
    @Autowired
    private AgentMapper agentMapper;
    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    IExclusiveShopService exclusiveShopService;
    /**
     * 查询老师管理
     *
     * @param userId
     * @return 老师管理
     */
    @Override
    public AgentUser selectAgentUserByUserId(String userId) {
        return agentUserMapper.selectAgentUserByUserId(userId);
    }

    /**
     * 查询老师管理列表
     *
     * @param agentUser 老师管理
     * @return 老师管理
     */
    @Override
    public List<AgentUser> selectAgentUserList(AgentUser agentUser) {
        return agentUserMapper.selectAgentUserList(agentUser);
    }

    /**
     * 新增老师管理
     *
     * @param agentUser 老师管理
     * @return 结果
     */
    @Override
    public int insertAgentUser(AgentUser agentUser) {
        Agent agent = agentService.getByAid(agentUser.getAid());
        if (agentUser.getLastAccessDate() == null) {
            agentUser.setLastAccessDate(agent.getAccountEnd());
        }
//        agentUser.setUserType(32);
        agentUser.setAccessDate(new Date());
        agentUser.setRegisterDate(new Date());
        agentUser.setPassword(Md5Utils.hash("123456"));
        agentUser.setPasswordText("123456");
        String userId = getUserId();
        agentUser.setUserId(userId);
        agentUser.setTimeLen("0");
        if (agentUser.getUserName()==null) agentUser.setUserName(userId);
        return agentUserMapper.insertAgentUser(agentUser);
    }


    private String getUserId() {
        while (true) {
            String teacherId = "thb" + IdentityGenerator.randomString(5);
            if (agentMapper.checkHasTeacherUser(teacherId) < 1) {
                return teacherId;
            }
        }
    }

    /**
     * 修改老师管理
     *
     * @param agentUser 老师管理
     * @return 结果
     */
    @Override
    public int updateAgentUser(AgentUser agentUser) {
        return agentUserMapper.updateAgentUser(agentUser);
    }

    /**
     * 批量删除老师管理
     *
     * @param ids 需要删除的老师管理主键
     * @return 结果
     */
    @Override
    public int deleteAgentUserByIds(Long[] ids) {
        return agentUserMapper.deleteAgentUserByIds(ids);
    }

    /**
     * 删除老师管理信息
     *
     * @param id 老师管理主键
     * @return 结果
     */
    @Override
    public int deleteAgentUserById(Long id) {
        return agentUserMapper.deleteAgentUserById(id);
    }


    @Override
    public String resetPassword(String userId) {
        LambdaQueryWrapper<AgentUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentUser::getUserId, userId);
        if (count(queryWrapper) > 0) {
            AgentUser userInfo = new AgentUser();
            userInfo.setUserId(userId);
            String password = IdentityGenerator.randomString(6);
            userInfo.setPassword(Md5Utils.hash(password));
            updateAgentUser(userInfo);
            return password;
        }
        return null;
    }

    @Override
    @Transactional
    public boolean changeAgent(TeachChangeAgentDto teachChangeAgentDto) {
        if(teachChangeAgentDto.getExclusiveShopId()!=null){
            teachChangeAgentDto.setAid(agentService.selectAgentById(exclusiveShopService.selectExclusiveShopById(teachChangeAgentDto.getExclusiveShopId()).getAgentId()).getAid());
        }
        teachChangeAgentDto.getUserIdList().forEach(userId -> {
            agentUserMapper.updateTeacherAgent(userId,teachChangeAgentDto.getAid(),teachChangeAgentDto.getExclusiveShopId());
            agentUserMapper.updateTeacherClassAgent(userId,teachChangeAgentDto.getAid());
            //学生一起转移
            if(teachChangeAgentDto.getMoveStudents()!=null && teachChangeAgentDto.getMoveStudents()){
                userInfoService.updateStudentAgentByTeacher(userId,teachChangeAgentDto.getAid(),teachChangeAgentDto.getExclusiveShopId());
            }else{
                agentUserMapper.clearTeacherAndClassByUserId(userId);
                agentUserMapper.clearGroupByUserId(userId);
            }
        });
        return true;
    }

    @Override
    @Cacheable(value = "teacherName",key = "#teachId", unless = "#result == null")
    public String getTeacherName(String teachId) {
       return agentUserMapper.getTeacherName(teachId);
    }

    @Override
    @Cacheable(value = "className",key = "#classId", unless = "#result == null")
    public String getClassName(Long classId) {
        return agentUserMapper.getTeacherClassName(classId);
    }
}
