package com.redbook.system.service;

import com.redbook.system.domain.TabletMoveRecord;

import java.util.List;

/**
 * 硬件转移记录Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-17
 */
public interface ITabletMoveRecordService 
{
    /**
     * 查询硬件转移记录
     * 
     * @param id 硬件转移记录主键
     * @return 硬件转移记录
     */
     TabletMoveRecord selectTabletMoveRecordById(Long id);

    /**
     * 查询硬件转移记录列表
     * 
     * @param tabletMoveRecord 硬件转移记录
     * @return 硬件转移记录集合
     */
     List<TabletMoveRecord> selectTabletMoveRecordList(TabletMoveRecord tabletMoveRecord);

    /**
     * 新增硬件转移记录
     * 
     * @param tabletMoveRecord 硬件转移记录
     * @return 结果
     */
     int insertTabletMoveRecord(TabletMoveRecord tabletMoveRecord);

    /**
     * 修改硬件转移记录
     * 
     * @param tabletMoveRecord 硬件转移记录
     * @return 结果
     */
     int updateTabletMoveRecord(TabletMoveRecord tabletMoveRecord);

    /**
     * 批量删除硬件转移记录
     * 
     * @param ids 需要删除的硬件转移记录主键集合
     * @return 结果
     */
     int deleteTabletMoveRecordByIds(Long[] ids);

    /**
     * 删除硬件转移记录信息
     * 
     * @param id 硬件转移记录主键
     * @return 结果
     */
     int deleteTabletMoveRecordById(Long id);
}
