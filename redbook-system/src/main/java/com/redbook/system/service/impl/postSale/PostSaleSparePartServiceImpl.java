package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.PostSaleSparePart;
import com.redbook.system.mapper.PostSaleSparePartMapper;
import com.redbook.system.service.postSale.IPostSaleSparePartService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 备件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class PostSaleSparePartServiceImpl extends ServiceImpl<PostSaleSparePartMapper, PostSaleSparePart> implements IPostSaleSparePartService
{
    @Autowired
    private PostSaleSparePartMapper postSaleSparePartMapper;

    /**
     * 查询备件
     * 
     * @param id 备件主键
     * @return 备件
     */
    @Override
    public PostSaleSparePart selectPostSaleSparePartById(Integer id)
    {
        return postSaleSparePartMapper.selectPostSaleSparePartById(id);
    }

    /**
     * 查询备件列表
     * 
     * @param postSaleSparePart 备件
     * @return 备件
     */
    @Override
    public List<PostSaleSparePart> selectPostSaleSparePartList(PostSaleSparePart postSaleSparePart)
    {
        return postSaleSparePartMapper.selectPostSaleSparePartList(postSaleSparePart);
    }

    /**
     * 新增备件
     * 
     * @param postSaleSparePart 备件
     * @return 结果
     */
    @Override
    public int insertPostSaleSparePart(PostSaleSparePart postSaleSparePart)
    {
        postSaleSparePart.setCreateTime(DateUtils.getNowDate());
        postSaleSparePart.setCreateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleSparePartMapper.insertPostSaleSparePart(postSaleSparePart);
    }

    /**
     * 修改备件
     * 
     * @param postSaleSparePart 备件
     * @return 结果
     */
    @Override
    public int updatePostSaleSparePart(PostSaleSparePart postSaleSparePart)
    {
        postSaleSparePart.setUpdateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        postSaleSparePart.setUpdateTime(DateUtils.getNowDate());
        return postSaleSparePartMapper.updatePostSaleSparePart(postSaleSparePart);
    }

    /**
     * 批量删除备件
     * 
     * @param ids 需要删除的备件主键
     * @return 结果
     */
    @Override
    public int deletePostSaleSparePartByIds(Long[] ids)
    {
        return postSaleSparePartMapper.deletePostSaleSparePartByIds(ids);
    }

    /**
     * 删除备件信息
     * 
     * @param id 备件主键
     * @return 结果
     */
    @Override
    public int deletePostSaleSparePartById(Long id)
    {
        return postSaleSparePartMapper.deletePostSaleSparePartById(id);
    }
}
