package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.PostSaleGoods;
import com.redbook.system.mapper.PostSaleGoodsMapper;
import com.redbook.system.service.postSale.IPostSaleGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 商品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class PostSaleGoodsServiceImpl extends ServiceImpl<PostSaleGoodsMapper, PostSaleGoods> implements IPostSaleGoodsService
{
    @Autowired
    private PostSaleGoodsMapper postSaleGoodsMapper;

    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
    @Override
    public PostSaleGoods selectPostSaleGoodsById(Long id)
    {
        return postSaleGoodsMapper.selectPostSaleGoodsById(id);
    }

    /**
     * 查询商品列表
     * 
     * @param postSaleGoods 商品
     * @return 商品
     */
    @Override
    public List<PostSaleGoods> selectPostSaleGoodsList(PostSaleGoods postSaleGoods)
    {
        return postSaleGoodsMapper.selectPostSaleGoodsList(postSaleGoods);
    }

    /**
     * 新增商品
     * 
     * @param postSaleGoods 商品
     * @return 结果
     */
    @Override
    public int insertPostSaleGoods(PostSaleGoods postSaleGoods)
    {
        postSaleGoods.setCreateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        postSaleGoods.setCreateTime(DateUtils.getNowDate());
        return postSaleGoodsMapper.insertPostSaleGoods(postSaleGoods);
    }

    /**
     * 修改商品
     * 
     * @param postSaleGoods 商品
     * @return 结果
     */
    @Override
    public int updatePostSaleGoods(PostSaleGoods postSaleGoods)
    {
        postSaleGoods.setUpdateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        postSaleGoods.setUpdateTime(DateUtils.getNowDate());
        return postSaleGoodsMapper.updatePostSaleGoods(postSaleGoods);
    }

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的商品主键
     * @return 结果
     */
    @Override
    public int deletePostSaleGoodsByIds(Long[] ids)
    {
        return postSaleGoodsMapper.deletePostSaleGoodsByIds(ids);
    }

    /**
     * 删除商品信息
     * 
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public int deletePostSaleGoodsById(Long id)
    {
        return postSaleGoodsMapper.deletePostSaleGoodsById(id);
    }
}
