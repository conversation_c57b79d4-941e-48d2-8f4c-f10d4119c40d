package com.redbook.system.service.kids;

import java.util.List;

import com.redbook.common.core.page.TableDataInfo;
import com.redbook.system.domain.kids.KidUserInfo;
import com.redbook.system.domain.dto.UserClassChangeDto;
import com.redbook.system.domain.vo.ClassInfo;
import com.redbook.system.domain.vo.TableDataSummaryInfoVo;

/**
 * 棒棒糖用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IKidUserInfoService 
{
    /**
     * 查询棒棒糖用户
     * 
     * @param id 棒棒糖用户主键
     * @return 棒棒糖用户
     */
     KidUserInfo selectKidUserInfoById(Long id);

    /**
     * 查询棒棒糖用户列表
     * 
     * @param kidUserInfo 棒棒糖用户
     * @return 棒棒糖用户集合
     */
     List<KidUserInfo> selectKidUserInfoList(KidUserInfo kidUserInfo);

    /**
     * 新增棒棒糖用户
     * 
     * @param kidUserInfo 棒棒糖用户
     * @return 结果
     */
     int insertKidUserInfo(KidUserInfo kidUserInfo);

    /**
     * 修改棒棒糖用户
     * 
     * @param kidUserInfo 棒棒糖用户
     * @return 结果
     */
     int updateKidUserInfo(KidUserInfo kidUserInfo);

    /**
     * 批量删除棒棒糖用户
     * 
     * @param ids 需要删除的棒棒糖用户主键集合
     * @return 结果
     */
     int deleteKidUserInfoByIds(Long[] ids);

    /**
     * 删除棒棒糖用户信息
     * 
     * @param id 棒棒糖用户主键
     * @return 结果
     */
     int deleteKidUserInfoById(Long id);
    /**
     * 棒棒糖用户分页查询
     * @param userInfo
     * @return
     */
    TableDataInfo<KidUserInfo> selectUserInfoList(KidUserInfo userInfo);

    /**
     * 统计棒棒糖用户列表
     * @param userInfo
     * @return
     */
    TableDataSummaryInfoVo countUserInfoList(KidUserInfo userInfo);

    /**
     * 修改用户信息
     * @param user
     * @return
     */
    Boolean updateStudentInfo(KidUserInfo user);

    /**
     * 重置密码
     * @param userId
     * @return
     */
    String resetPassword(String userId);

    /**
     * 获取班级列表
     * @param teacherId
     * @return
     */
    List<ClassInfo> getClassList(String  teacherId);
    /**
     * 修改用户班级
     * @param dto
     * @return
     */
    Boolean updateStudentClass(UserClassChangeDto dto);

    KidUserInfo selectUserInfoByUserId(String userId);

    boolean renewKidUser(String userId, Integer memberType, String expirationDate, String firstPurchaseDate, String lastPurchaseDate);


    String getClassName(Long classId);
}
