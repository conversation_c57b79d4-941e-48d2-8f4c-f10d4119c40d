package com.redbook.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.redbook.system.domain.AgentMapping;

import java.util.List;

/**
 * 代理商管理映射Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface IAgentMappingService extends IService<AgentMapping>
{
    /**
     * 查询代理商管理映射
     * 
     * @param id 代理商管理映射主键
     * @return 代理商管理映射
     */
    AgentMapping selectAgentMappingById(Long id);

    /**
     * 查询代理商管理映射列表
     * 
     * @param agentMapping 代理商管理映射
     * @return 代理商管理映射集合
     */
    List<AgentMapping> selectAgentMappingList(AgentMapping agentMapping);

    /**
     * 新增代理商管理映射
     * 
     * @param agentMapping 代理商管理映射
     * @return 结果
     */
    int insertAgentMapping(AgentMapping agentMapping);

    /**
     * 修改代理商管理映射
     * 
     * @param agentMapping 代理商管理映射
     * @return 结果
     */
    int updateAgentMapping(AgentMapping agentMapping);

    /**
     * 批量删除代理商管理映射
     * 
     * @param ids 需要删除的代理商管理映射主键集合
     * @return 结果
     */
    int deleteAgentMappingByIds(Long[] ids);

    /**
     * 删除代理商管理映射信息
     * 
     * @param id 代理商管理映射主键
     * @return 结果
     */
    int deleteAgentMappingById(Long id);
}
