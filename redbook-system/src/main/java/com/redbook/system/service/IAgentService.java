package com.redbook.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.dto.*;
import com.redbook.system.domain.vo.AgentAreaVo;
import com.redbook.system.domain.vo.AgentDetailVo;
import com.redbook.system.domain.vo.AgentMappingListVo;

import java.util.List;
import java.util.Map;

/**
 * 代理商Service接口
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface IAgentService extends IService<Agent> {
    /**
     * 查询代理商
     *
     * @param id 代理商主键
     * @return 代理商
     */
    Agent selectAgentById(Long id);

    /**
     *
     * @param name
     * @return
     */
    Agent selectByName(String name);

    AgentDetailVo getAgentDetail(Long id);

    /**
     * 查询代理商ID列表
     * @param mId
     * @param searchValue
     * @return
     */
    List<Integer> getAgentIdList(Long mId,String searchValue,Long agentId);
    List<String> getAgentAidList(Long mId,String searchValue,Long agentId);
    /**
     * 查询代理商列表
     *
     * @param agent 代理商
     * @return 代理商集合
     */
    List<Agent> selectAgentList(Agent agent);

    List<Agent> getByUserId(Long userId);

    /**
     * 获取代理商名（缓存redis）
     * @param agentId
     * @return
     */
    String getAgentName(Long agentId);
    String getAgentName(String aid);
    String getAgentLevel(Long agentId);

    /**
     * 获取经理姓名列表 用“，”分割（缓存3天）
     * @param agentId
     * @return
     */
    String getManagerNamesByAgentId(Long agentId);


    String getKeFuAccountByAgentId(Long agentId);
    /**
     * 新增代理商
     *
     * @param agent 代理商
     * @return 结果
     */
    int insertAgent(Agent agent);

    /**
     * 修改代理商
     *
     * @param agent 代理商
     * @return 结果
     */
    int updateAgent(Agent agent);

    /**
     * 批量删除代理商
     *
     * @param ids 需要删除的代理商主键集合
     * @return 结果
     */
    int deleteAgentByIds(Long[] ids);

    /**
     * 删除代理商信息
     *
     * @param id 代理商主键
     * @return 结果
     */
    int deleteAgentById(Long id);

    /**
     * 根据用户id和角色获取代理商关系
     *
     * @param userId
     * @return
     */
    AgentMappingListVo agentMappingListVo(Long userId, Long roleId);

    int saveAgentMapping(AgentMappingDto dto);

    /**
     * 删除代理商相关缓存数据
     */
    void deleteAgentCache();

    Boolean addAgent(AddAgentDto dto);

    int updateAgent(UpdateAgentDto agent);

    int exit(AgentExitDto dto);

    int renew(AgentRenewDto dto);

    /**
     * 根据代理商aid获取代理商信息
     * @param aid
     * @return
     */
    Agent getByAid(String aid);

    /**
     * 获取续费记录
     * @return
     */
    List<Map<String, Object>> MemberPaymentInfo(MemberPaymentInfoDto memberPaymentInfoDto);

    AgentAreaVo getAgentArea(Long id);

    void genAgentTeacherAndUser(Agent agent,ExclusiveShop exclusiveShop);

    /**
     * 更新超级会员和学管师到期时间
     * @param updateScope 影响范围  all:代理商下所有超级会员 onlyAgent:只有代理商下的，排除专卖店   onlyExclusiveShop：专卖店的超级会员
     * @param aid
     * @param exclusiveShopId
     * @param date
     */
    void updateAccountEndDate(String updateScope,String aid,Integer exclusiveShopId, String date);

    int setAgentGiftCount(SetAgentGiftCountDto agent);

    int setGiftAccount(SetAgentGiftAccountDto agent);
}
