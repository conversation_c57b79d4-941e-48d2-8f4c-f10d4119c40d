package com.redbook.system.service;

import com.redbook.system.domain.RechargeDiscount;

import java.util.List;

/**
 * 充值优惠Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-12
 */
public interface IRechargeDiscountService 
{
    /**
     * 查询充值优惠
     * 
     * @param fundtypeid 充值优惠主键
     * @return 充值优惠
     */
     RechargeDiscount selectRechargeDiscountByFundtypeid(Integer fundtypeid);

    /**
     * 查询充值优惠列表
     * 
     * @param rechargeDiscount 充值优惠
     * @return 充值优惠集合
     */
     List<RechargeDiscount> selectRechargeDiscountList(RechargeDiscount rechargeDiscount);

    /**
     * 新增充值优惠
     * 
     * @param rechargeDiscount 充值优惠
     * @return 结果
     */
     int insertRechargeDiscount(RechargeDiscount rechargeDiscount);

    /**
     * 修改充值优惠
     * 
     * @param rechargeDiscount 充值优惠
     * @return 结果
     */
     int updateRechargeDiscount(RechargeDiscount rechargeDiscount);

    /**
     * 批量删除充值优惠
     * 
     * @param fundtypeids 需要删除的充值优惠主键集合
     * @return 结果
     */
     int deleteRechargeDiscountByFundtypeids(Long[] fundtypeids);

    /**
     * 删除充值优惠信息
     * 
     * @param fundtypeid 充值优惠主键
     * @return 结果
     */
     int deleteRechargeDiscountByFundtypeid(Long fundtypeid);
}
