package com.redbook.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.ActivityExcleUtil;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.StringUtils;
import com.redbook.system.domain.ActivityDcwThirdUser;
import com.redbook.system.domain.dto.ActivityDcwThirdIndexListDto;
import com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank;
import com.redbook.system.mapper.ActivityDcwThirdMapper;
import com.redbook.system.service.IActivityDcwThirdService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ActivityDcwThirdServiceImpl extends ServiceImpl<ActivityDcwThirdMapper, ActivityDcwThirdUser> implements IActivityDcwThirdService {
    @Autowired
    ActivityDcwThirdMapper activityDcwThirdMapper;

    @Override
    public List<ActivityDcwThirdUser> selectRecordList(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto) {
        Integer activityBaseId = activityDcwThirdIndexListDto.getActivityBaseId();
        String searchKey = activityDcwThirdIndexListDto.getSearchKey();
        Integer gameStage = activityDcwThirdIndexListDto.getGameStage();
        //赛段四的模式：100：100强  32：32强  16：16强  8：8强
        Integer gameStage4Mode = activityDcwThirdIndexListDto.getJinJiSaiMode();
        Integer stage = activityDcwThirdIndexListDto.getStage();
        List<ActivityDcwThirdUser> activityDcwThirdUsers = new ArrayList<>();
        List<ActivityDcwThirdPersonalRank> topUserList = new ArrayList<>();
        Map<String, ActivityDcwThirdPersonalRank> rankMap = new HashMap<>();
        switch (gameStage) {
            case 1:
                activityDcwThirdUsers = activityDcwThirdMapper.selectGameStage1List(activityDcwThirdIndexListDto);
                if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                    topUserList = activityDcwThirdMapper.selectGameStage1TopNumList(activityBaseId, 50);
                    rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                    for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                        activityDcwThirdUser.setRank(rankMap.containsKey(activityDcwThirdUser.getUserId()) ? rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString() : "未上榜");
                        buildAddress(activityDcwThirdUser);
                    }
                }

                break;
            case 2:
                activityDcwThirdUsers = activityDcwThirdMapper.selectGameStage2List(activityDcwThirdIndexListDto);
                if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                    topUserList = activityDcwThirdMapper.selectGameStage2TopNumList(activityBaseId, 50);
                    rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                    for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                        activityDcwThirdUser.setRank(rankMap.containsKey(activityDcwThirdUser.getUserId()) ? rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString() : "未上榜");
                        buildAddress(activityDcwThirdUser);
                    }
                }

                break;
            case 3:
                activityDcwThirdUsers = activityDcwThirdMapper.selectGameStage3List(activityDcwThirdIndexListDto);
                if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                    topUserList = activityDcwThirdMapper.selectGameStage3TopNumList(activityBaseId, activityDcwThirdUsers.get(0).getCityId(), 50);
                    rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                    for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                        activityDcwThirdUser.setRank(rankMap.containsKey(activityDcwThirdUser.getUserId()) ? rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString() : "未上榜");
                        buildAddress(activityDcwThirdUser);
                    }
                }
                break;
            case 4:
                if (gameStage4Mode == 100) {
                    activityDcwThirdUsers = activityDcwThirdMapper.selectGameStage4Mode100List(activityDcwThirdIndexListDto);
                    if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                        topUserList = activityDcwThirdMapper.selectGameStage4Mode100TopList(activityBaseId, stage, 100);
                        rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                        for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                            activityDcwThirdUser.setRank("未上榜");
                            if (activityDcwThirdUser.getScore() != null && rankMap.containsKey(activityDcwThirdUser.getUserId())) {
                                activityDcwThirdUser.setRank(rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString());
                            }
                            buildAddress(activityDcwThirdUser);
                        }
                    }
                } else if (gameStage4Mode == 32) {
                    activityDcwThirdUsers = activityDcwThirdMapper.selectGameStage4Mode32List(activityDcwThirdIndexListDto);
                    if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                        topUserList = activityDcwThirdMapper.selectGameStage4Mode32TopList(activityBaseId, stage, 32);
                        rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                        for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                            activityDcwThirdUser.setRank("未上榜");
                            if (activityDcwThirdUser.getScore() != null && rankMap.containsKey(activityDcwThirdUser.getUserId())) {
                                activityDcwThirdUser.setRank(rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString());
                            }
                            buildAddress(activityDcwThirdUser);
                        }
                    }

                } else if (gameStage4Mode == 16) {
                    activityDcwThirdUsers = activityDcwThirdMapper.selectGameStage4Mode16List(activityDcwThirdIndexListDto);
                    if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                        topUserList = activityDcwThirdMapper.selectGameStage4Mode16TopList(activityBaseId, stage, 16);
                        rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                        for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                            activityDcwThirdUser.setRank("未上榜");
                            if (activityDcwThirdUser.getScore() != null && rankMap.containsKey(activityDcwThirdUser.getUserId())) {
                                activityDcwThirdUser.setRank(rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString());
                            }
                            buildAddress(activityDcwThirdUser);
                        }
                    }

                } else if (gameStage4Mode == 8) {
                    activityDcwThirdUsers = activityDcwThirdMapper.selectGameStage4Mode8List(activityDcwThirdIndexListDto);
                    if (CollectionUtils.isNotEmpty(activityDcwThirdUsers)) {
                        topUserList = activityDcwThirdMapper.selectGameStage4Mode8TopList(activityBaseId, stage, 8);
                        rankMap = topUserList.stream().collect(Collectors.toMap(ActivityDcwThirdPersonalRank::getUserId, Function.identity()));
                        for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                            activityDcwThirdUser.setRank("未上榜");
                            if (activityDcwThirdUser.getScore() != null && rankMap.containsKey(activityDcwThirdUser.getUserId())) {
                                activityDcwThirdUser.setRank(rankMap.get(activityDcwThirdUser.getUserId()).getRankNum().toString());
                            }
                            buildAddress(activityDcwThirdUser);
                        }
                    }
                }

                break;
            case 5:
                activityDcwThirdUsers = activityDcwThirdMapper.selectGameStage5List(activityDcwThirdIndexListDto);
                if(CollectionUtils.isNotEmpty(activityDcwThirdUsers)){
                    for (ActivityDcwThirdUser activityDcwThirdUser : activityDcwThirdUsers) {
                        buildAddress(activityDcwThirdUser);
                    }
                }
                break;
        }
        return activityDcwThirdUsers;
    }

    private void buildAddress(ActivityDcwThirdUser activityDcwThirdUser) {
        String userAddr = activityDcwThirdUser.getUserAddr();
        if(StringUtils.isNotEmpty(userAddr)){
            String[] split = userAddr.split("-");
            activityDcwThirdUser.setProvince(split[0]);
            if(split.length==3){
                activityDcwThirdUser.setCity(split[1]);
                activityDcwThirdUser.setCounty(split[2]);
            }else if(split.length==2){
                activityDcwThirdUser.setCity(split[1]);
            }
        }

    }

    @Override
    public void export(List<ActivityDcwThirdUser> list, HttpServletResponse response, ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto) {
        try {
            Integer gameStage = activityDcwThirdIndexListDto.getGameStage();
            Integer gameStage4Mode = activityDcwThirdIndexListDto.getJinJiSaiMode();
            //excel工作空间名
            String sheetName = "信息表";
            List<String[]> rows = new ArrayList<>();
            String fileName = "";
            String fileNamePre = "2024第三届单词王争霸赛-";
            String fileNameMiddle = "";
            String fileNameEnd = ".xlsx";
            String userName = SecurityUtils.getLoginUser().getUser().getUserName();
            boolean specialUser= StringUtils.isNotEmpty(userName) && "hb271736".equals(userName);
            if (CollectionUtil.isNotEmpty(list)) {
                switch (gameStage) {
                    case 1:
                        fileNameMiddle = "城市热度争夺赛";
                        if(specialUser){
                            String[] headers1 = {"姓名", "省", "市", "区县","账号", "学段", "最高热度", "个人总热度", "当前排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[9];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getProvince();
                                row[2] = activityDcwThirdUser.getCity();
                                row[3] = activityDcwThirdUser.getCounty();
                                row[4] = activityDcwThirdUser.getUserId();
                                row[5] = activityDcwThirdUser.getStageDesc();
                                row[6] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getMaxScore() + "";
                                row[7] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getScore() + "";
                                row[8] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers1, fileName, sheetName, response);
                            break;
                        }else {
                            String[] headers1 = {"姓名", "账号", "学段", "最高热度", "个人总热度", "当前排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[6];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getUserId();
                                row[2] = activityDcwThirdUser.getStageDesc();
                                row[3] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getMaxScore() + "";
                                row[4] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getScore() + "";
                                row[5] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers1, fileName, sheetName, response);
                            break;
                        }

                    case 2:
                        fileNameMiddle = "城市举办权争夺赛";
                        if(specialUser){
                            String[] headers2 = {"姓名", "省", "市", "区县","账号", "学段", "最高分值", "个人总分值", "当前排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[9];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getProvince();
                                row[2] = activityDcwThirdUser.getCity();
                                row[3] = activityDcwThirdUser.getCounty();
                                row[4] = activityDcwThirdUser.getUserId();
                                row[5] = activityDcwThirdUser.getStageDesc();
                                row[6] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getMaxScore() + "";
                                row[7] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getScore() + "";
                                row[8] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers2, fileName, sheetName, response);
                            break;
                        }else {
                            String[] headers2 = {"姓名", "账号", "学段", "最高分值", "个人总分值", "当前排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[6];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getUserId();
                                row[2] = activityDcwThirdUser.getStageDesc();
                                row[3] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getMaxScore() + "";
                                row[4] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getScore() + "";
                                row[5] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers2, fileName, sheetName, response);
                            break;
                        }

                    case 3:
                        fileNameMiddle = "城市英雄选拔赛";
                        if(specialUser){
                            String[] headers3 = {"姓名","省", "市", "区县", "账号", "学段", "赛段最高分", "当前排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[8];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getProvince();
                                row[2] = activityDcwThirdUser.getCity();
                                row[3] = activityDcwThirdUser.getCounty();
                                row[4] = activityDcwThirdUser.getUserId();
                                row[5] = activityDcwThirdUser.getStageDesc();
                                row[6] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getMaxScore() + "";
                                row[7] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers3, fileName, sheetName, response);
                            break;
                        }else {
                            String[] headers3 = {"姓名", "账号", "学段", "赛段最高分", "当前排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[5];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getUserId();
                                row[2] = activityDcwThirdUser.getStageDesc();
                                row[3] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getMaxScore() + "";
                                row[4] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers3, fileName, sheetName, response);
                            break;
                        }

                    case 4:
                        if (gameStage4Mode == 100) {
                            fileNameMiddle = "全国100强";
                        } else if (gameStage4Mode == 32) {
                            fileNameMiddle = "全国32强";
                        } else if (gameStage4Mode == 16) {
                            fileNameMiddle = "全国16强";
                        } else if (gameStage4Mode == 8) {
                            fileNameMiddle = "全国8强";
                        }
                        if(specialUser){
                            String[] headers4 = {"姓名","省", "市", "区县", "账号", "组别", "当前赛程", "赛段成绩", "当前排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[9];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getProvince();
                                row[2] = activityDcwThirdUser.getCity();
                                row[3] = activityDcwThirdUser.getCounty();
                                row[4] = activityDcwThirdUser.getUserId();
                                row[5] = activityDcwThirdUser.getStageDesc() + "组";
                                row[6] = fileNameMiddle;
                                row[7] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getScore() + "";
                                row[8] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers4, fileName, sheetName, response);
                            break;
                        }else {
                            String[] headers4 = {"姓名", "账号", "组别", "当前赛程", "赛段成绩", "当前排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[6];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getUserId();
                                row[2] = activityDcwThirdUser.getStageDesc() + "组";
                                row[3] = fileNameMiddle;
                                row[4] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getScore() + "";
                                row[5] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers4, fileName, sheetName, response);
                            break;
                        }

                    case 5:
                        fileNameMiddle = "总决赛";
                        if(specialUser){
                            String[] headers5 = {"姓名", "省", "市", "区县", "账号", "组别", "成绩", "效率", "排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[9];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getProvince();
                                row[2] = activityDcwThirdUser.getCity();
                                row[3] = activityDcwThirdUser.getCounty();
                                row[4] = activityDcwThirdUser.getUserId();
                                row[5] = activityDcwThirdUser.getStageDesc() + "组";
                                row[6] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getScore() + "";
                                row[7] = activityDcwThirdUser.getEfficiency() + "";
                                row[8] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers5, fileName, sheetName, response);
                            break;
                        }else {
                            String[] headers5 = {"姓名", "账号", "组别", "成绩", "效率", "排名"};
                            for (ActivityDcwThirdUser activityDcwThirdUser : list) {
                                String[] row = new String[6];
                                row[0] = activityDcwThirdUser.getUserName();
                                row[1] = activityDcwThirdUser.getUserId();
                                row[2] = activityDcwThirdUser.getStageDesc() + "组";
                                row[3] = activityDcwThirdUser.getScoreTime()==null?"-":activityDcwThirdUser.getScore() + "";
                                row[4] = activityDcwThirdUser.getEfficiency() + "";
                                row[5] = activityDcwThirdUser.getRank();
                                rows.add(row);
                            }
                            fileName = fileNamePre + fileNameMiddle + fileNameEnd;
                            ActivityExcleUtil.exportExcel(rows, headers5, fileName, sheetName, response);
                            break;
                        }

                }

            }
        } catch (Exception e) {

        }
    }
}
