package com.redbook.system.service.kids;

import java.util.List;
import com.redbook.system.domain.kids.PraiseStar;

/**
 * 夸夸星语Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IPraiseStarService 
{
    /**
     * 查询夸夸星语
     * 
     * @param id 夸夸星语主键
     * @return 夸夸星语
     */
     PraiseStar selectPraiseStarById(String id);

    /**
     * 查询夸夸星语列表
     * 
     * @param praiseStar 夸夸星语
     * @return 夸夸星语集合
     */
     List<PraiseStar> selectPraiseStarList(PraiseStar praiseStar);

    /**
     * 新增夸夸星语
     * 
     * @param praiseStar 夸夸星语
     * @return 结果
     */
     int insertPraiseStar(PraiseStar praiseStar);

    /**
     * 修改夸夸星语
     * 
     * @param praiseStar 夸夸星语
     * @return 结果
     */
     int updatePraiseStar(PraiseStar praiseStar);

    /**
     * 批量删除夸夸星语
     * 
     * @param ids 需要删除的夸夸星语主键集合
     * @return 结果
     */
     int deletePraiseStarByIds(String[] ids);

    /**
     * 删除夸夸星语信息
     * 
     * @param id 夸夸星语主键
     * @return 结果
     */
     int deletePraiseStarById(String id);
}
