package com.redbook.system.service.kids;

import com.redbook.system.domain.kids.AppVersion;

import java.util.List;

/**
 * 应用版本，存储所有应用版本信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface IAppVersionService 
{
    /**
     * 查询应用版本，存储所有应用版本信息
     * 
     * @param id 应用版本，存储所有应用版本信息主键
     * @return 应用版本，存储所有应用版本信息
     */
     AppVersion selectAppVersionById(Long id);

    /**
     * 查询应用版本，存储所有应用版本信息列表
     * 
     * @param appVersion 应用版本，存储所有应用版本信息
     * @return 应用版本，存储所有应用版本信息集合
     */
     List<AppVersion> selectAppVersionList(AppVersion appVersion);

    /**
     * 新增应用版本，存储所有应用版本信息
     * 
     * @param appVersion 应用版本，存储所有应用版本信息
     * @return 结果
     */
     int insertAppVersion(AppVersion appVersion);

    /**
     * 修改应用版本，存储所有应用版本信息
     * 
     * @param appVersion 应用版本，存储所有应用版本信息
     * @return 结果
     */
     int updateAppVersion(AppVersion appVersion);

    /**
     * 批量删除应用版本，存储所有应用版本信息
     * 
     * @param ids 需要删除的应用版本，存储所有应用版本信息主键集合
     * @return 结果
     */
     int deleteAppVersionByIds(Long[] ids);

    /**
     * 删除应用版本，存储所有应用版本信息信息
     * 
     * @param id 应用版本，存储所有应用版本信息主键
     * @return 结果
     */
     int deleteAppVersionById(Long id);
}
