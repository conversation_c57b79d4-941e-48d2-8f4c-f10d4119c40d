package com.redbook.system.service.postSale;

import com.redbook.system.domain.PostSaleQuestionArticle;

import java.util.List;

/**
 * 售后文章Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IPostSaleQuestionArticleService 
{
    /**
     * 查询售后文章
     * 
     * @param id 售后文章主键
     * @return 售后文章
     */
     PostSaleQuestionArticle selectPostSaleQuestionArticleById(Long id);

    /**
     * 查询售后文章列表
     * 
     * @param postSaleQuestionArticle 售后文章
     * @return 售后文章集合
     */
     List<PostSaleQuestionArticle> selectPostSaleQuestionArticleList(PostSaleQuestionArticle postSaleQuestionArticle);

    /**
     * 新增售后文章
     * 
     * @param postSaleQuestionArticle 售后文章
     * @return 结果
     */
     int insertPostSaleQuestionArticle(PostSaleQuestionArticle postSaleQuestionArticle);

    /**
     * 修改售后文章
     * 
     * @param postSaleQuestionArticle 售后文章
     * @return 结果
     */
     int updatePostSaleQuestionArticle(PostSaleQuestionArticle postSaleQuestionArticle);

    /**
     * 批量删除售后文章
     * 
     * @param ids 需要删除的售后文章主键集合
     * @return 结果
     */
     int deletePostSaleQuestionArticleByIds(Long[] ids);

    /**
     * 删除售后文章信息
     * 
     * @param id 售后文章主键
     * @return 结果
     */
     int deletePostSaleQuestionArticleById(Long id);
}
