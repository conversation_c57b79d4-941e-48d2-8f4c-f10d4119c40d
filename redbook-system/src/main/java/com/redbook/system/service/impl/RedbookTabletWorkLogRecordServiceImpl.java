package com.redbook.system.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.RedbookTabletWorkLogRecord;
import com.redbook.system.mapper.RedbookTabletWorkLogRecordMapper;
import com.redbook.system.service.IRedbookTabletWorkLogRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.redbook.system.mapper.RedbookTabletWorkLogRecordMapper;
import com.redbook.system.domain.RedbookTabletWorkLogRecord;
import com.redbook.system.service.IRedbookTabletWorkLogRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
/**
 * 小红本设备工作日志记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
public class RedbookTabletWorkLogRecordServiceImpl extends ServiceImpl<RedbookTabletWorkLogRecordMapper, RedbookTabletWorkLogRecord> implements IRedbookTabletWorkLogRecordService
{
    @Autowired
    private RedbookTabletWorkLogRecordMapper redbookTabletWorkLogRecordMapper;

    /**
     * 查询小红本设备工作日志记录
     *
     * @param id 小红本设备工作日志记录主键
     * @return 小红本设备工作日志记录
     */
    @Override
    public RedbookTabletWorkLogRecord selectRedbookTabletWorkLogRecordById(Long id)
    {
        return redbookTabletWorkLogRecordMapper.selectRedbookTabletWorkLogRecordById(id);
    }

    @Override
    public RedbookTabletWorkLogRecord selectBySn(String sn) {
        return redbookTabletWorkLogRecordMapper.selectBySn(sn);
    }

    /**
     * 查询小红本设备工作日志记录列表
     *
     * @param redbookTabletWorkLogRecord 小红本设备工作日志记录
     * @return 小红本设备工作日志记录
     */
    @Override
    public List<RedbookTabletWorkLogRecord> selectRedbookTabletWorkLogRecordList(RedbookTabletWorkLogRecord redbookTabletWorkLogRecord)
    {
        return redbookTabletWorkLogRecordMapper.selectRedbookTabletWorkLogRecordList(redbookTabletWorkLogRecord);
    }

    /**
     * 新增小红本设备工作日志记录
     *
     * @param redbookTabletWorkLogRecord 小红本设备工作日志记录
     * @return 结果
     */
    @Override
    public int insertRedbookTabletWorkLogRecord(RedbookTabletWorkLogRecord redbookTabletWorkLogRecord)
    {
        redbookTabletWorkLogRecord.setCreateTime(DateUtils.getNowDate());
        return redbookTabletWorkLogRecordMapper.insertRedbookTabletWorkLogRecord(redbookTabletWorkLogRecord);
    }

    /**
     * 修改小红本设备工作日志记录
     *
     * @param redbookTabletWorkLogRecord 小红本设备工作日志记录
     * @return 结果
     */
    @Override
    public int updateRedbookTabletWorkLogRecord(RedbookTabletWorkLogRecord redbookTabletWorkLogRecord)
    {
        return redbookTabletWorkLogRecordMapper.updateRedbookTabletWorkLogRecord(redbookTabletWorkLogRecord);
    }

    @Override
    public int update(RedbookTabletWorkLogRecord redbookTabletWorkLogRecord) {
        return redbookTabletWorkLogRecordMapper.update(redbookTabletWorkLogRecord);
    }

    /**
     * 批量删除小红本设备工作日志记录
     *
     * @param ids 需要删除的小红本设备工作日志记录主键
     * @return 结果
     */
    @Override
    public int deleteRedbookTabletWorkLogRecordByIds(Long[] ids)
    {
        return redbookTabletWorkLogRecordMapper.deleteRedbookTabletWorkLogRecordByIds(ids);
    }

    /**
     * 删除小红本设备工作日志记录信息
     *
     * @param id 小红本设备工作日志记录主键
     * @return 结果
     */
    @Override
    public int deleteRedbookTabletWorkLogRecordById(Long id)
    {
        return redbookTabletWorkLogRecordMapper.deleteRedbookTabletWorkLogRecordById(id);
    }
}
