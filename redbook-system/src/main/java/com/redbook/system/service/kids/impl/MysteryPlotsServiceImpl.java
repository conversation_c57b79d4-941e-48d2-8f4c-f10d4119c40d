package com.redbook.system.service.kids.impl;

import java.util.List;

import com.redbook.common.annotation.DataSource;
import com.redbook.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.redbook.system.mapper.MysteryPlotsMapper;
import com.redbook.system.domain.kids.MysteryPlots;
import com.redbook.system.service.kids.IMysteryPlotsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
/**
 * 神秘剧情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
@DataSource(value = DataSourceType.KIDS)
public class MysteryPlotsServiceImpl extends ServiceImpl<MysteryPlotsMapper, MysteryPlots> implements IMysteryPlotsService
{
    @Autowired
    private MysteryPlotsMapper mysteryPlotsMapper;

    /**
     * 查询神秘剧情
     * 
     * @param id 神秘剧情主键
     * @return 神秘剧情
     */
    @Override
    public MysteryPlots selectMysteryPlotsById(String id)
    {
        return mysteryPlotsMapper.selectMysteryPlotsById(id);
    }

    /**
     * 查询神秘剧情列表
     * 
     * @param mysteryPlots 神秘剧情
     * @return 神秘剧情
     */
    @Override
    public List<MysteryPlots> selectMysteryPlotsList(MysteryPlots mysteryPlots)
    {
        return mysteryPlotsMapper.selectMysteryPlotsList(mysteryPlots);
    }

    /**
     * 新增神秘剧情
     * 
     * @param mysteryPlots 神秘剧情
     * @return 结果
     */
    @Override
    public int insertMysteryPlots(MysteryPlots mysteryPlots)
    {
        return mysteryPlotsMapper.insertMysteryPlots(mysteryPlots);
    }

    /**
     * 修改神秘剧情
     * 
     * @param mysteryPlots 神秘剧情
     * @return 结果
     */
    @Override
    public int updateMysteryPlots(MysteryPlots mysteryPlots)
    {
        return mysteryPlotsMapper.updateMysteryPlots(mysteryPlots);
    }

    /**
     * 批量删除神秘剧情
     * 
     * @param ids 需要删除的神秘剧情主键
     * @return 结果
     */
    @Override
    public int deleteMysteryPlotsByIds(String[] ids)
    {
        return mysteryPlotsMapper.deleteMysteryPlotsByIds(ids);
    }

    /**
     * 删除神秘剧情信息
     * 
     * @param id 神秘剧情主键
     * @return 结果
     */
    @Override
    public int deleteMysteryPlotsById(String id)
    {
        return mysteryPlotsMapper.deleteMysteryPlotsById(id);
    }
}
