package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.TimeUtils;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.RedbookAgentRebate;
import com.redbook.system.domain.RedbookMemberPrices;
import com.redbook.system.domain.RedbookMemberRebates;
import com.redbook.system.mapper.RedbookAgentRebateMapper;
import com.redbook.system.mapper.RedbookMemberPricesMapper;
import com.redbook.system.mapper.RedbookMemberRebatesMapper;
import com.redbook.system.service.IAgentAccountService;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.IRedbookAgentRebateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 代理商动态价格优惠关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Service
public class RedbookAgentRebateServiceImpl extends ServiceImpl<RedbookAgentRebateMapper, RedbookAgentRebate> implements IRedbookAgentRebateService {

    @Autowired
    private RedbookAgentRebateMapper redbookAgentRebateMapper;
    @Autowired
    private RedbookMemberRebatesMapper redbookMemberRebatesMapper;
    @Autowired
    private RedbookMemberPricesMapper redbookMemberPricesMapper;
    @Autowired
    private IAgentAccountService agentAccountService;
    @Autowired
    private IAgentService agentService;
    private final ConcurrentHashMap<Long, RedbookMemberRebates> memberRebatesCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, BigDecimal> priceCache = new ConcurrentHashMap<>();

    @Override
    public RedbookAgentRebate selectRedbookAgentRebateById(Long id) {
        return redbookAgentRebateMapper.selectRedbookAgentRebateById(id);
    }

    @Override
    public List<RedbookAgentRebate> selectRedbookAgentRebateList(RedbookAgentRebate redbookAgentRebate) {
        return redbookAgentRebateMapper.selectRedbookAgentRebateList(redbookAgentRebate);
    }

    @Override
    public int insertRedbookAgentRebate(RedbookAgentRebate redbookAgentRebate) {
        return redbookAgentRebateMapper.insertRedbookAgentRebate(redbookAgentRebate);
    }

    @Override
    public int updateRedbookAgentRebate(RedbookAgentRebate redbookAgentRebate) {
        return redbookAgentRebateMapper.updateRedbookAgentRebate(redbookAgentRebate);
    }

    @Override
    public int deleteRedbookAgentRebateByIds(Long[] ids) {
        return redbookAgentRebateMapper.deleteRedbookAgentRebateByIds(ids);
    }

    @Override
    public int deleteRedbookAgentRebateById(Long id) {
        return redbookAgentRebateMapper.deleteRedbookAgentRebateById(id);
    }

    @Override
    public boolean addSaleAgentRebate(Long agentId, int month) {
        RedbookAgentRebate agentRebate = getAgentRebateByAgentId(agentId);
        if (agentRebate == null) {
            return false;
        }

        BigDecimal saleNum = calculateSaleNum(month);
        agentRebate.setSaleNum(agentRebate.getSaleNum().add(saleNum));
        if (agentRebate.getSaleNum().compareTo(agentRebate.getNextSaleNum()) >= 0) {
            RedbookMemberRebates nextRebateLevel = getRedbookMemberRebates(agentRebate.getRebates()+1);
            agentRebate.setNextSaleNum(BigDecimal.valueOf(nextRebateLevel.getSalesRangeMax()));
            agentRebate.setRebates(agentRebate.getRebates()+1);
        }

        try {
            updateRedbookAgentRebate(agentRebate);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean subtractSaleAgentRebate(Long agentId, int month) {
        RedbookAgentRebate agentRebate = getAgentRebateByAgentId(agentId);
        if (agentRebate == null) {
            return false;
        }

        BigDecimal saleNum = calculateSaleNum(month);
        agentRebate.setSaleNum(agentRebate.getSaleNum().subtract(saleNum));


        try {
            updateRedbookAgentRebate(agentRebate);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public BigDecimal getAgentRebatePrice(Long agentId, int month) {
        if (!agentAccountService.getAgentRebateStatus(agentId)) {
            Agent agent = agentService.selectAgentById(agentId);
            return getOldPrice(month, agent);
        }

        RedbookAgentRebate agentRebate = getAgentRebateByAgentId(agentId);
        if (agentRebate == null) {
            throw new IllegalArgumentException("Agent not found for ID: " + agentId);
        }

        BigDecimal basePrice = getRedbookMemberPrices(month);
        BigDecimal rebatePercentage = agentRebate.getRebate().getRebatePercentage();

        return calculateDiscountPrice(basePrice, rebatePercentage);
    }

    private static BigDecimal getOldPrice(int month, Agent agent) {
        BigDecimal agentPrice = new BigDecimal("0.00");
        //代理商续费价格
        if (month == 1) {
            agentPrice = new BigDecimal("86.00");
        } else if (month == 3) {
            agentPrice = new BigDecimal("196.00");
        } else if (month == 6) {
            agentPrice = new BigDecimal("296.00");
        } else if (month == 12) {
            agentPrice = new BigDecimal("456.00");
            //战略合作伙伴有年课程续费优惠
            Integer pricingScheme = agent.getPriceScheme();
            if (pricingScheme != null && pricingScheme == 1) {
                agentPrice = new BigDecimal("396.00");
            }
        }
        return agentPrice;
    }

    @Override
    public RedbookAgentRebate getAgentRebateByAgentId(Long agentId) {
        LambdaQueryWrapper<RedbookAgentRebate> queryWrapper = new LambdaQueryWrapper<RedbookAgentRebate>()
                .eq(RedbookAgentRebate::getAgentId, agentId)
                .eq(RedbookAgentRebate::getYear, TimeUtils.getYear(new Date()));

        RedbookAgentRebate agentRebate = getOne(queryWrapper);

        if (agentRebate == null) {
            agentRebate = createDefaultAgentRebate(agentId);
        }

        agentRebate.setRebate(getRedbookMemberRebates(agentRebate.getRebates()));
        return agentRebate;
    }

    private BigDecimal calculateSaleNum(int month) {
        switch (month) {
            case 12:
                return BigDecimal.ONE;
            case 6:
                return BigDecimal.valueOf(0.5);
            case 3:
                return BigDecimal.valueOf(0.25).setScale(2, RoundingMode.HALF_UP);
            case 1:
                return BigDecimal.valueOf(0.08).setScale(2, RoundingMode.HALF_UP);
            default:
                throw new IllegalArgumentException("Invalid month value: " + month);
        }
    }

    public static BigDecimal calculateDiscountPrice(BigDecimal basePrice, BigDecimal rebatePercentage) {
        return basePrice.subtract(basePrice.multiply(rebatePercentage)
                .divide(BigDecimal.valueOf(100), 1, BigDecimal.ROUND_HALF_UP));
    }


    private RedbookAgentRebate createDefaultAgentRebate(Long agentId) {
        try {
            RedbookAgentRebate defaultRebate = RedbookAgentRebate.builder()
                    .agentId(agentId)
                    .year(String.valueOf(TimeUtils.getYear(new Date())))
                    .rebates(1L)
                    .saleNum(BigDecimal.ZERO)
                    .nextSaleNum(BigDecimal.valueOf(getRedbookMemberRebates(1L).getSalesRangeMax()))
                    .build();

            insertRedbookAgentRebate(defaultRebate);
            return getAgentRebateByAgentId(agentId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private RedbookMemberRebates getRedbookMemberRebates(Long level) {
        return memberRebatesCache.computeIfAbsent(level, redbookMemberRebatesMapper::selectRedbookMemberRebatesById);
    }

    private BigDecimal getRedbookMemberPrices(Integer month) {
        return priceCache.computeIfAbsent(month, key -> {
            RedbookMemberPrices memberPrice = redbookMemberPricesMapper.selectRedbookMemberPricesByMonth(key);
            return memberPrice != null ? memberPrice.getBasePrice() : BigDecimal.ZERO;
        });
    }

}
