package com.redbook.system.service.postSale;

import com.redbook.system.domain.PostSaleSparePartInOut;

import java.util.List;

/**
 * 备件出入库Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IPostSaleSparePartInOutService 
{
    /**
     * 查询备件出入库
     * 
     * @param id 备件出入库主键
     * @return 备件出入库
     */
     PostSaleSparePartInOut selectPostSaleSparePartInOutById(Long id);

    /**
     * 查询备件出入库列表
     * 
     * @param postSaleSparePartInOut 备件出入库
     * @return 备件出入库集合
     */
     List<PostSaleSparePartInOut> selectPostSaleSparePartInOutList(PostSaleSparePartInOut postSaleSparePartInOut);
    String summaryCount();

    /**
     * 新增备件出入库
     * 
     * @param postSaleSparePartInOut 备件出入库
     * @return 结果
     */
     int insertPostSaleSparePartInOut(PostSaleSparePartInOut postSaleSparePartInOut);

    /**
     * 修改备件出入库
     * 
     * @param postSaleSparePartInOut 备件出入库
     * @return 结果
     */
     int updatePostSaleSparePartInOut(PostSaleSparePartInOut postSaleSparePartInOut);

    /**
     * 批量删除备件出入库
     * 
     * @param ids 需要删除的备件出入库主键集合
     * @return 结果
     */
     int deletePostSaleSparePartInOutByIds(Long[] ids);

    /**
     * 删除备件出入库信息
     * 
     * @param id 备件出入库主键
     * @return 结果
     */
     int deletePostSaleSparePartInOutById(Long id);
}
