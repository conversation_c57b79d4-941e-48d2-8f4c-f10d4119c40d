package com.redbook.system.service.impl.postSale;

import java.util.List;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.StringUtils;
import com.redbook.system.service.postSale.IPostSaleQuickReplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.redbook.system.mapper.PostSaleQuickReplyMapper;
import com.redbook.system.domain.PostSaleQuickReply;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
/**
 * 售后快速回复Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class PostSaleQuickReplyServiceImpl extends ServiceImpl<PostSaleQuickReplyMapper, PostSaleQuickReply> implements IPostSaleQuickReplyService {
    @Autowired
    private PostSaleQuickReplyMapper postSaleQuickReplyMapper;


    /**
     * 查询售后快速回复列表
     * 
     * @param postSaleQuickReply 售后快速回复
     * @return 售后快速回复
     */
    @Override
    public List<PostSaleQuickReply> selectPostSaleQuickReplyList(PostSaleQuickReply postSaleQuickReply)
    {
        postSaleQuickReply.setCreateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        postSaleQuickReply.setIsDelete(0);
        return postSaleQuickReplyMapper.selectPostSaleQuickReplyList(postSaleQuickReply);
    }

    /**
     * 新增售后快速回复
     * 
     * @param postSaleQuickReply 售后快速回复
     * @return 结果
     */
    @Override
    public int insertPostSaleQuickReply(PostSaleQuickReply postSaleQuickReply)
    {
        if(postSaleQuickReply==null|| StringUtils.isEmpty(postSaleQuickReply.getContent())){
            throw new RuntimeException("快速回复内容不能为空");
        }
        postSaleQuickReply.setCreateTime(DateUtils.getNowDate());
        postSaleQuickReply.setCreateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleQuickReplyMapper.insertPostSaleQuickReply(postSaleQuickReply);
    }

    /**
     * 修改售后快速回复
     * 
     * @param postSaleQuickReply 售后快速回复
     * @return 结果
     */
    @Override
    public int updatePostSaleQuickReply(PostSaleQuickReply postSaleQuickReply)
    {
        postSaleQuickReply.setUpdateTime(DateUtils.getNowDate());
        postSaleQuickReply.setUpdateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleQuickReplyMapper.updatePostSaleQuickReply(postSaleQuickReply);
    }

    /**
     * 批量删除售后快速回复
     * 
     * @param ids 需要删除的售后快速回复主键
     * @return 结果
     */
    @Override
    public int deletePostSaleQuickReplyByIds(Long[] ids)
    {
        return postSaleQuickReplyMapper.deletePostSaleQuickReplyByIds(ids);
    }
}
