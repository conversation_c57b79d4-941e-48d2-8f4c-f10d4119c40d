package com.redbook.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.redbook.system.domain.AgentRechargeOrder;

import java.util.List;

/**
 * 代理商充值Service接口
 * 
 * <AUTHOR>
 * @date 2022-11-23
 */
public interface IAgentRechargeOrderService extends IService<AgentRechargeOrder>
{
    /**
     * 查询代理商充值
     * 
     * @param id 代理商充值主键
     * @return 代理商充值
     */
     AgentRechargeOrder selectAgentRechargeOrderById(Long id);


    /**
     * 查询代理商充值列表
     * 
     * @param agentRechargeOrder 代理商充值
     * @return 代理商充值集合
     */
     List<AgentRechargeOrder> selectAgentRechargeOrderList(AgentRechargeOrder agentRechargeOrder);

    /**
     * 新增代理商充值
     * 
     * @param agentRechargeOrder 代理商充值
     * @return 结果
     */
     int insertAgentRechargeOrder(AgentRechargeOrder agentRechargeOrder);

    /**
     * 修改代理商充值
     * 
     * @param agentRechargeOrder 代理商充值
     * @return 结果
     */
     int updateAgentRechargeOrder(AgentRechargeOrder agentRechargeOrder);

    /**
     * 批量删除代理商充值
     * 
     * @param ids 需要删除的代理商充值主键集合
     * @return 结果
     */
     int deleteAgentRechargeOrderByIds(Long[] ids);

    /**
     * 删除代理商充值信息
     * 
     * @param id 代理商充值主键
     * @return 结果
     */
     int deleteAgentRechargeOrderById(Long id);

    /**
     * 根据单号查询充值记录
     * @param orderId
     * @return
     */
    AgentRechargeOrder selectAgentRechargeOrderByOrderId(String orderId);
}
