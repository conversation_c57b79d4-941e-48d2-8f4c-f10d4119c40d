package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.system.domain.AgentMapping;
import com.redbook.system.mapper.AgentMappingMapper;
import com.redbook.system.service.IAgentMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 代理商管理映射Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-11-09
 */
@Service
public class AgentMappingServiceImpl extends ServiceImpl<AgentMappingMapper, AgentMapping> implements IAgentMappingService
{
    @Autowired
    private AgentMappingMapper agentMappingMapper;

    /**
     * 查询代理商管理映射
     * 
     * @param id 代理商管理映射主键
     * @return 代理商管理映射
     */
    @Override
    public AgentMapping selectAgentMappingById(Long id)
    {
        return agentMappingMapper.selectAgentMappingById(id);
    }

    /**
     * 查询代理商管理映射列表
     * 
     * @param agentMapping 代理商管理映射
     * @return 代理商管理映射
     */
    @Override
    public List<AgentMapping> selectAgentMappingList(AgentMapping agentMapping)
    {
        return agentMappingMapper.selectAgentMappingList(agentMapping);
    }

    /**
     * 新增代理商管理映射
     * 
     * @param agentMapping 代理商管理映射
     * @return 结果
     */
    @Override
    public int insertAgentMapping(AgentMapping agentMapping)
    {
        return agentMappingMapper.insertAgentMapping(agentMapping);
    }

    /**
     * 修改代理商管理映射
     * 
     * @param agentMapping 代理商管理映射
     * @return 结果
     */
    @Override
    public int updateAgentMapping(AgentMapping agentMapping)
    {
        return agentMappingMapper.updateAgentMapping(agentMapping);
    }

    /**
     * 批量删除代理商管理映射
     * 
     * @param ids 需要删除的代理商管理映射主键
     * @return 结果
     */
    @Override
    public int deleteAgentMappingByIds(Long[] ids)
    {
        return agentMappingMapper.deleteAgentMappingByIds(ids);
    }

    /**
     * 删除代理商管理映射信息
     * 
     * @param id 代理商管理映射主键
     * @return 结果
     */
    @Override
    public int deleteAgentMappingById(Long id)
    {
        return agentMappingMapper.deleteAgentMappingById(id);
    }
}
