package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.system.domain.ExclusiveShopMemberPrice;
import com.redbook.system.mapper.ExclusiveShopMemberPriceMapper;
import com.redbook.system.service.IExclusiveShopMemberPriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 专卖店会员价格Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-21
 */
@Service
public class ExclusiveShopMemberPriceServiceImpl extends ServiceImpl<ExclusiveShopMemberPriceMapper, ExclusiveShopMemberPrice> implements IExclusiveShopMemberPriceService
{
    @Autowired
    private ExclusiveShopMemberPriceMapper exclusiveShopMemberPriceMapper;

    /**
     * 查询专卖店会员价格
     * @return 专卖店会员价格
     */
    @Override
    public ExclusiveShopMemberPrice selectExclusiveShopMemberPrice(Integer exclusiveShopId,Integer stage,Integer timeLen)
    {
        return exclusiveShopMemberPriceMapper.selectExclusiveShopMemberPrice(exclusiveShopId,stage,timeLen);
    }

    /**
     * 查询专卖店会员价格列表
     * 
     * @param exclusiveShopMemberPrice 专卖店会员价格
     * @return 专卖店会员价格
     */
    @Override
    public List<ExclusiveShopMemberPrice> selectExclusiveShopMemberPriceList(ExclusiveShopMemberPrice exclusiveShopMemberPrice)
    {
        return exclusiveShopMemberPriceMapper.selectExclusiveShopMemberPriceList(exclusiveShopMemberPrice);
    }

    /**
     * 保存专卖店会员价格
     * @return 结果
     */
    @Override
    public boolean saveExclusiveShopMemberPrice(List<ExclusiveShopMemberPrice> priceList)
    {
        for (ExclusiveShopMemberPrice newPrice : priceList) {
            ExclusiveShopMemberPrice exclusiveShopMemberPrice = selectExclusiveShopMemberPrice(newPrice.getExclusiveShopId(), newPrice.getStage(), newPrice.getTimeLen());
            if(exclusiveShopMemberPrice==null){
                exclusiveShopMemberPriceMapper.insertExclusiveShopMemberPrice(newPrice);
            }else{
                exclusiveShopMemberPrice.setPrice(newPrice.getPrice());
                exclusiveShopMemberPriceMapper.updateExclusiveShopMemberPrice(exclusiveShopMemberPrice);
            }
        }
        return true;
    }


    /**
     * 批量删除专卖店会员价格
     * 
     * @param ids 需要删除的专卖店会员价格主键
     * @return 结果
     */
    @Override
    public int deleteExclusiveShopMemberPriceByIds(Long[] ids)
    {
        return exclusiveShopMemberPriceMapper.deleteExclusiveShopMemberPriceByIds(ids);
    }

    /**
     * 删除专卖店会员价格信息
     * 
     * @param id 专卖店会员价格主键
     * @return 结果
     */
    @Override
    public int deleteExclusiveShopMemberPriceById(Long id)
    {
        return exclusiveShopMemberPriceMapper.deleteExclusiveShopMemberPriceById(id);
    }
}
