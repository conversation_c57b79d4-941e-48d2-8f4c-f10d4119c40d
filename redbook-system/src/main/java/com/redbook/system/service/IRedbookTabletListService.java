package com.redbook.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.system.domain.RedbookTabletList;
import com.redbook.system.domain.dto.*;
import com.redbook.system.domain.vo.AgentTabletListVo;
import com.redbook.system.domain.vo.RedbookTabletDetailVo;
import com.redbook.system.domain.vo.StockTabletListVo;

import java.util.List;
import java.util.Map;

/**
 * 硬件Service接口
 *
 * <AUTHOR>
 * @date 2022-11-14
 */
public interface IRedbookTabletListService extends IService<RedbookTabletList> {
    /**
     * 查询硬件
     *
     * @param id 硬件主键
     * @return 硬件
     */
    RedbookTabletList selectRedbookTabletListById(Long id);
    RedbookTabletList selectRedbookTabletListBySN(String sn);

    /**
     * 查询库存硬件列表
     *
     * @param stockTabletListDto 硬件
     * @return 硬件集合
     */
    List<StockTabletListVo> selectStockTabletList(StockTabletListDto stockTabletListDto);

    /**
     * 查询代理商硬件列表
     *
     * @param agentTabletListDto
     * @return
     */
    List<AgentTabletListVo> selectAgentTabletList(AgentTabletListDto agentTabletListDto);

    /**
     * 新增硬件
     *
     * @param redbookTabletList 硬件
     * @return 结果
     */
    int insertRedbookTabletList(RedbookTabletList redbookTabletList);

    /**
     * 修改硬件
     *
     * @param redbookTabletList 硬件
     * @return 结果
     */
    int updateRedbookTabletList(RedbookTabletList redbookTabletList);

    /**
     * 修改硬件状态
     * @param sn
     * @param status
     * @return
     */
    int modifyStatus(String sn,Integer status);

    /**
     * 批量删除硬件
     *
     * @param ids 需要删除的硬件主键集合
     * @return 结果
     */
    int deleteRedbookTabletListByIds(Long[] ids);

    /**
     * 删除硬件信息
     *
     * @param id 硬件主键
     * @return 结果
     */
    int deleteRedbookTabletListById(Long id);

    /**
     * 生产入库
     *
     * @param redbookTabletInputDto
     * @param username
     * @return
     */
    int productInput(RedbookTabletInputDto redbookTabletInputDto, String username) throws Exception;

    /**
     * 库存设备转移
     *
     * @param stockTabletMoveDto
     * @param username
     * @return
     */
    int stockTabletMove(StockTabletMoveDto stockTabletMoveDto, String username);

    /**
     * 设备代理商设备转移
     *
     * @param agentTabletMoveDto
     * @param username
     * @return
     */
    AjaxResult agentTabletMove(AgentTabletMoveDto agentTabletMoveDto, String username);
    AjaxResult cleanUsageTraces(String sn);

    RedbookTabletDetailVo selectRedbookTabletDetailInfoById(Long id);


    int saleTablet(TabletSaleDto tabletSaleDto);

    /**
     * 查询硬件登录记录
     * @param sn
     * @param limitStartTime
     * @param limitEndTime
     * @param pageIndex
     * @param pageSize
     * @return
     */
    Map<String, Object> getTabletLogonRecord(String sn, String limitStartTime, String limitEndTime, Integer pageIndex, Integer pageSize);


    /**
     * 日志管理操作：启动、关闭、重新上传、刷新
     * @param workLogRecordDTO
     * @return
     */
    int workLogPost(RedbookTabletWorkLogRecordDTO workLogRecordDTO);



}
