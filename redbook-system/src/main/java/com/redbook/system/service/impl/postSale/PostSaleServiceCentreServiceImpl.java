package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.PostSaleServiceCentre;
import com.redbook.system.mapper.PostSaleServiceCentreMapper;
import com.redbook.system.service.postSale.IPostSaleServiceCentreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 售后服务中心Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@Service
public class PostSaleServiceCentreServiceImpl extends ServiceImpl<PostSaleServiceCentreMapper, PostSaleServiceCentre> implements IPostSaleServiceCentreService
{
    @Autowired
    private PostSaleServiceCentreMapper postSaleServiceCentreMapper;

    /**
     * 查询售后服务中心
     * 
     * @param id 售后服务中心主键
     * @return 售后服务中心
     */
    @Override
    public PostSaleServiceCentre selectPostSaleServiceCentreById(Long id)
    {
        return postSaleServiceCentreMapper.selectPostSaleServiceCentreById(id);
    }

    /**
     * 查询售后服务中心列表
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 售后服务中心
     */
    @Override
    public List<PostSaleServiceCentre> selectPostSaleServiceCentreList(PostSaleServiceCentre postSaleServiceCentre)
    {
        return postSaleServiceCentreMapper.selectPostSaleServiceCentreList(postSaleServiceCentre);
    }

    /**
     * 新增售后服务中心
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 结果
     */
    @Override
    public int insertPostSaleServiceCentre(PostSaleServiceCentre postSaleServiceCentre)
    {
        postSaleServiceCentre.setCreateTime(DateUtils.getNowDate());
        return postSaleServiceCentreMapper.insertPostSaleServiceCentre(postSaleServiceCentre);
    }

    /**
     * 修改售后服务中心
     * 
     * @param postSaleServiceCentre 售后服务中心
     * @return 结果
     */
    @Override
    public int updatePostSaleServiceCentre(PostSaleServiceCentre postSaleServiceCentre)
    {
        postSaleServiceCentre.setUpdateTime(DateUtils.getNowDate());
        return postSaleServiceCentreMapper.updatePostSaleServiceCentre(postSaleServiceCentre);
    }

    /**
     * 批量删除售后服务中心
     * 
     * @param ids 需要删除的售后服务中心主键
     * @return 结果
     */
    @Override
    public int deletePostSaleServiceCentreByIds(Long[] ids)
    {
        return postSaleServiceCentreMapper.deletePostSaleServiceCentreByIds(ids);
    }

    /**
     * 删除售后服务中心信息
     * 
     * @param id 售后服务中心主键
     * @return 结果
     */
    @Override
    public int deletePostSaleServiceCentreById(Long id)
    {
        return postSaleServiceCentreMapper.deletePostSaleServiceCentreById(id);
    }
}
