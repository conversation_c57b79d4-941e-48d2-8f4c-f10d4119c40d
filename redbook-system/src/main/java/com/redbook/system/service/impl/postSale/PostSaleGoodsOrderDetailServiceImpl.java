package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.system.domain.PostSaleGoodsOrderDetail;
import com.redbook.system.mapper.PostSaleGoodsOrderDetailMapper;
import com.redbook.system.service.postSale.IPostSaleGoodsOrderDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 商品订单详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class PostSaleGoodsOrderDetailServiceImpl extends ServiceImpl<PostSaleGoodsOrderDetailMapper, PostSaleGoodsOrderDetail> implements IPostSaleGoodsOrderDetailService
{
    @Autowired
    private PostSaleGoodsOrderDetailMapper postSaleGoodsOrderDetailMapper;

    /**
     * 查询商品订单详情
     * 
     * @param id 商品订单详情主键
     * @return 商品订单详情
     */
    @Override
    public PostSaleGoodsOrderDetail selectPostSaleGoodsOrderDetailById(Long id)
    {
        return postSaleGoodsOrderDetailMapper.selectPostSaleGoodsOrderDetailById(id);
    }

    /**
     * 查询商品订单详情列表
     * 
     * @param postSaleGoodsOrderDetail 商品订单详情
     * @return 商品订单详情
     */
    @Override
    public List<PostSaleGoodsOrderDetail> selectPostSaleGoodsOrderDetailList(PostSaleGoodsOrderDetail postSaleGoodsOrderDetail)
    {
        return postSaleGoodsOrderDetailMapper.selectPostSaleGoodsOrderDetailList(postSaleGoodsOrderDetail);
    }

    /**
     * 新增商品订单详情
     * 
     * @param postSaleGoodsOrderDetail 商品订单详情
     * @return 结果
     */
    @Override
    public int insertPostSaleGoodsOrderDetail(PostSaleGoodsOrderDetail postSaleGoodsOrderDetail)
    {
        return postSaleGoodsOrderDetailMapper.insertPostSaleGoodsOrderDetail(postSaleGoodsOrderDetail);
    }

    @Override
    public int batchInsertGoodsOrderDetail(List<PostSaleGoodsOrderDetail> goodsOrderDetailList) {
        return postSaleGoodsOrderDetailMapper.batchInsertGoodsOrderDetail(goodsOrderDetailList);
    }

    /**
     * 修改商品订单详情
     * 
     * @param postSaleGoodsOrderDetail 商品订单详情
     * @return 结果
     */
    @Override
    public int updatePostSaleGoodsOrderDetail(PostSaleGoodsOrderDetail postSaleGoodsOrderDetail)
    {
        return postSaleGoodsOrderDetailMapper.updatePostSaleGoodsOrderDetail(postSaleGoodsOrderDetail);
    }

    /**
     * 批量删除商品订单详情
     * 
     * @param ids 需要删除的商品订单详情主键
     * @return 结果
     */
    @Override
    public int deletePostSaleGoodsOrderDetailByIds(Long[] ids)
    {
        return postSaleGoodsOrderDetailMapper.deletePostSaleGoodsOrderDetailByIds(ids);
    }

    /**
     * 删除商品订单详情信息
     * 
     * @param id 商品订单详情主键
     * @return 结果
     */
    @Override
    public int deletePostSaleGoodsOrderDetailById(Long id)
    {
        return postSaleGoodsOrderDetailMapper.deletePostSaleGoodsOrderDetailById(id);
    }
}
