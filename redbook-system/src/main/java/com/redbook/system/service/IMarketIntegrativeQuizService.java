package com.redbook.system.service;

import com.redbook.system.domain.MarketIntegrativeQuiz;
import com.redbook.system.domain.market.MarketStageData;

import java.util.List;

/**
 * 代理商网络营销-综合测评Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-19
 */
public interface IMarketIntegrativeQuizService 
{
    /**
     * 查询代理商网络营销-综合测评
     * 
     * @param id 代理商网络营销-综合测评主键
     * @return 代理商网络营销-综合测评
     */
     MarketIntegrativeQuiz selectMarketIntegrativeQuizById(Long id);
     List<MarketStageData> versionList();

    /**
     * 查询代理商网络营销-综合测评列表
     * 
     * @param marketIntegrativeQuiz 代理商网络营销-综合测评
     * @return 代理商网络营销-综合测评集合
     */
     List<MarketIntegrativeQuiz> selectMarketIntegrativeQuizList(MarketIntegrativeQuiz marketIntegrativeQuiz);

    /**
     * 新增代理商网络营销-综合测评
     * 
     * @param marketIntegrativeQuiz 代理商网络营销-综合测评
     * @return 结果
     */
     int insertMarketIntegrativeQuiz(MarketIntegrativeQuiz marketIntegrativeQuiz);

    /**
     * 修改代理商网络营销-综合测评
     * 
     * @param marketIntegrativeQuiz 代理商网络营销-综合测评
     * @return 结果
     */
     int updateMarketIntegrativeQuiz(MarketIntegrativeQuiz marketIntegrativeQuiz);

    /**
     * 批量删除代理商网络营销-综合测评
     * 
     * @param ids 需要删除的代理商网络营销-综合测评主键集合
     * @return 结果
     */
     int deleteMarketIntegrativeQuizByIds(Long[] ids);

    /**
     * 删除代理商网络营销-综合测评信息
     * 
     * @param id 代理商网络营销-综合测评主键
     * @return 结果
     */
     int deleteMarketIntegrativeQuizById(Long id);
}
