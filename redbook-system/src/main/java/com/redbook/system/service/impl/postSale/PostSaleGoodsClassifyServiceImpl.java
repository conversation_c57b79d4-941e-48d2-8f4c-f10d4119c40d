package com.redbook.system.service.impl.postSale;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.PostSaleGoodsClassify;
import com.redbook.system.mapper.PostSaleGoodsClassifyMapper;
import com.redbook.system.service.postSale.IPostSaleGoodsClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 商品类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class PostSaleGoodsClassifyServiceImpl extends ServiceImpl<PostSaleGoodsClassifyMapper, PostSaleGoodsClassify> implements IPostSaleGoodsClassifyService
{
    @Autowired
    private PostSaleGoodsClassifyMapper postSaleGoodsClassifyMapper;

    /**
     * 查询商品类型
     * 
     * @param id 商品类型主键
     * @return 商品类型
     */
    @Override
    public PostSaleGoodsClassify selectPostSaleGoodsClassifyById(Long id)
    {
        return postSaleGoodsClassifyMapper.selectPostSaleGoodsClassifyById(id);
    }

    /**
     * 查询商品类型列表
     * 
     * @param postSaleGoodsClassify 商品类型
     * @return 商品类型
     */
    @Override
    public List<PostSaleGoodsClassify> selectPostSaleGoodsClassifyList(PostSaleGoodsClassify postSaleGoodsClassify)
    {
        return postSaleGoodsClassifyMapper.selectPostSaleGoodsClassifyList(postSaleGoodsClassify);
    }

    /**
     * 新增商品类型
     * 
     * @param postSaleGoodsClassify 商品类型
     * @return 结果
     */
    @Override
    public int insertPostSaleGoodsClassify(PostSaleGoodsClassify postSaleGoodsClassify)
    {
        postSaleGoodsClassify.setCreateTime(DateUtils.getNowDate());
        postSaleGoodsClassify.setCreateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleGoodsClassifyMapper.insertPostSaleGoodsClassify(postSaleGoodsClassify);
    }

    /**
     * 修改商品类型
     * 
     * @param postSaleGoodsClassify 商品类型
     * @return 结果
     */
    @Override
    public int updatePostSaleGoodsClassify(PostSaleGoodsClassify postSaleGoodsClassify)
    {
        postSaleGoodsClassify.setUpdateTime(DateUtils.getNowDate());
        postSaleGoodsClassify.setUpdateBy(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
        return postSaleGoodsClassifyMapper.updatePostSaleGoodsClassify(postSaleGoodsClassify);
    }

    /**
     * 批量删除商品类型
     * 
     * @param ids 需要删除的商品类型主键
     * @return 结果
     */
    @Override
    public int deletePostSaleGoodsClassifyByIds(Long[] ids)
    {
        return postSaleGoodsClassifyMapper.deletePostSaleGoodsClassifyByIds(ids,String.valueOf(SecurityUtils.getLoginUser().getUserId()));
    }

    /**
     * 删除商品类型信息
     * 
     * @param id 商品类型主键
     * @return 结果
     */
    @Override
    public int deletePostSaleGoodsClassifyById(Long id)
    {
        return postSaleGoodsClassifyMapper.deletePostSaleGoodsClassifyById(id,String.valueOf(SecurityUtils.getLoginUser().getUserId()));
    }
}
