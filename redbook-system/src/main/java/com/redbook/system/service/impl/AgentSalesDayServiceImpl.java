package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.constant.HttpStatus;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.utils.StringUtils;
import com.redbook.system.domain.AgentSalesDay;
import com.redbook.system.domain.dto.AgentSalesListDto;
import com.redbook.system.mapper.AgentSalesDayMapper;
import com.redbook.system.service.IAgentSalesDayService;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.IExclusiveShopService;
import com.redbook.system.service.ISysUserService;
import com.redbook.system.util.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 代理商销售统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-25
 */
@Service
public class AgentSalesDayServiceImpl extends ServiceImpl<AgentSalesDayMapper, AgentSalesDay> implements IAgentSalesDayService {
    @Autowired
    private AgentSalesDayMapper agentSalesDayMapper;
    @Autowired
    private IExclusiveShopService exclusiveShopService;
    @Autowired
    IAgentService agentService;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询代理商销售统计
     *
     * @param id 代理商销售统计主键
     * @return 代理商销售统计
     */
    @Override
    public AgentSalesDay selectAgentSalesDayById(Long id) {
        return agentSalesDayMapper.selectAgentSalesDayById(id);
    }

    /**
     * 查询代理商销售统计列表
     *
     * @param agentSalesDay 代理商销售统计
     * @return 代理商销售统计
     */
    @Override
    public List<AgentSalesDay> selectAgentSalesDayList(AgentSalesDay agentSalesDay) {
        return agentSalesDayMapper.selectAgentSalesDayList(agentSalesDay);
    }

    @Override
    public boolean updateAgentSalesDay(long agentId, Integer exclusiveShopId, String salesDate, boolean isZeroTransfer, boolean isFirstRenew, int renewTimeLen, int renewStage, int updateNum) {
        //更新销售统计
        AgentSalesDay salesDay = agentSalesDayMapper.getByAgentIdAndDate(agentId, exclusiveShopId, salesDate);
        if (updateNum == -1 && salesDay == null) {
            return false;
        }
        if (salesDay == null) {
            salesDay = new AgentSalesDay();
            salesDay.setAgentId(agentId);
            salesDay.setExclusiveShopId(exclusiveShopId);
            salesDay.setSalesDate(salesDate);
        }
        //0元转
        if (isZeroTransfer) {
            salesDay.setZeroTransferNum((salesDay.getZeroTransferNum() == null ? 0 : salesDay.getZeroTransferNum()) + updateNum);
        } else {
            if (isFirstRenew) {
                //新购
                switch (renewTimeLen) {
                    case 1:
                        switch (renewStage) {
                            case 1:
                                salesDay.setNewFirstOne((salesDay.getNewFirstOne() == null ? 0 : salesDay.getNewFirstOne()) + updateNum);
                                break;
                            case 2:
                                salesDay.setNewSecondOne((salesDay.getNewSecondOne() == null ? 0 : salesDay.getNewSecondOne()) + updateNum);
                                break;
                            case 3:
                                salesDay.setNewThirdOne((salesDay.getNewThirdOne() == null ? 0 : salesDay.getNewThirdOne()) + updateNum);
                                break;
                            case 4:
                                salesDay.setNewFourOne((salesDay.getNewFourOne() == null ? 0 : salesDay.getNewFourOne()) + updateNum);
                                break;
                            case 5:
                                salesDay.setNewFiveOne((salesDay.getNewFiveOne() == null ? 0 : salesDay.getNewFiveOne()) + updateNum);
                                break;
                            case 11:
                                salesDay.setNewElevenOne((salesDay.getNewElevenOne() == null ? 0 : salesDay.getNewElevenOne()) + updateNum);
                                break;
                            case 21:
                                salesDay.setNewTwentyoneOne((salesDay.getNewTwentyoneOne() == null ? 0 : salesDay.getNewTwentyoneOne()) + updateNum);
                                break;
                            case 99:
                                salesDay.setNewKidOne((salesDay.getNewKidOne() == null ? 0 : salesDay.getNewKidOne()) + updateNum);
                                break;
                        }
                        break;
                    case 3:
                        switch (renewStage) {
                            case 1:
                                salesDay.setNewFirstThree((salesDay.getNewFirstThree() == null ? 0 : salesDay.getNewFirstThree()) + updateNum);
                                break;
                            case 2:
                                salesDay.setNewSecondThree((salesDay.getNewSecondThree() == null ? 0 : salesDay.getNewSecondThree()) + updateNum);
                                break;
                            case 3:
                                salesDay.setNewThirdThree((salesDay.getNewThirdThree() == null ? 0 : salesDay.getNewThirdThree()) + updateNum);
                                break;
                            case 4:
                                salesDay.setNewFourThree((salesDay.getNewFourThree() == null ? 0 : salesDay.getNewFourThree()) + updateNum);
                                break;
                            case 5:
                                salesDay.setNewFiveThree((salesDay.getNewFiveThree() == null ? 0 : salesDay.getNewFiveThree()) + updateNum);
                                break;
                            case 11:
                                salesDay.setNewElevenThree((salesDay.getNewElevenThree() == null ? 0 : salesDay.getNewElevenThree()) + updateNum);
                                break;
                            case 21:
                                salesDay.setNewTwentyoneThree((salesDay.getNewTwentyoneThree() == null ? 0 : salesDay.getNewTwentyoneThree()) + updateNum);
                                break;
                            case 99:
                                salesDay.setNewKidThree((salesDay.getNewKidThree() == null ? 0 : salesDay.getNewKidThree()) + updateNum);
                                break;
                        }
                        break;
                    case 6:
                        switch (renewStage) {
                            case 1:
                                salesDay.setNewFirstSix((salesDay.getNewFirstSix() == null ? 0 : salesDay.getNewFirstSix()) + updateNum);
                                break;
                            case 2:
                                salesDay.setNewSecondSix((salesDay.getNewSecondSix() == null ? 0 : salesDay.getNewSecondSix()) + updateNum);
                                break;
                            case 3:
                                salesDay.setNewThirdSix((salesDay.getNewThirdSix() == null ? 0 : salesDay.getNewThirdSix()) + updateNum);
                                break;
                            case 4:
                                salesDay.setNewFourSix((salesDay.getNewFourSix() == null ? 0 : salesDay.getNewFourSix()) + updateNum);
                                break;
                            case 5:
                                salesDay.setNewFiveSix((salesDay.getNewFiveSix() == null ? 0 : salesDay.getNewFiveSix()) + updateNum);
                                break;
                            case 11:
                                salesDay.setNewElevenSix((salesDay.getNewElevenSix() == null ? 0 : salesDay.getNewElevenSix()) + updateNum);
                                break;
                            case 21:
                                salesDay.setNewTwentyoneSix((salesDay.getNewTwentyoneSix() == null ? 0 : salesDay.getNewTwentyoneSix()) + updateNum);
                                break;
                            case 99:
                                salesDay.setNewKidSix((salesDay.getNewKidSix() == null ? 0 : salesDay.getNewKidSix()) + updateNum);
                                break;
                        }
                        break;
                    case 12:
                        switch (renewStage) {
                            case 1:
                                salesDay.setNewFirstTwelve((salesDay.getNewFirstTwelve() == null ? 0 : salesDay.getNewFirstTwelve()) + updateNum);
                                break;
                            case 2:
                                salesDay.setNewSecondTwelve((salesDay.getNewSecondTwelve() == null ? 0 : salesDay.getNewSecondTwelve()) + updateNum);
                                break;
                            case 3:
                                salesDay.setNewThirdTwelve((salesDay.getNewThirdTwelve() == null ? 0 : salesDay.getNewThirdTwelve()) + updateNum);
                                break;
                            case 4:
                                salesDay.setNewFourTwelve((salesDay.getNewFourTwelve() == null ? 0 : salesDay.getNewFourTwelve()) + updateNum);
                                break;
                            case 5:
                                salesDay.setNewFiveTwelve((salesDay.getNewFiveTwelve() == null ? 0 : salesDay.getNewFiveTwelve()) + updateNum);
                                break;
                            case 11:
                                salesDay.setNewElevenTwelve((salesDay.getNewElevenTwelve() == null ? 0 : salesDay.getNewElevenTwelve()) + updateNum);
                                break;
                            case 21:
                                salesDay.setNewTwentyoneTwelve((salesDay.getNewTwentyoneTwelve() == null ? 0 : salesDay.getNewTwentyoneTwelve()) + updateNum);
                                break;
                            case 99:
                                salesDay.setNewKidTwelve((salesDay.getNewKidTwelve() == null ? 0 : salesDay.getNewKidTwelve()) + updateNum);
                                break;
                        }
                        break;
                }
            } else {
                //续费
                switch (renewTimeLen) {
                    case 1:
                        switch (renewStage) {
                            case 1:
                                salesDay.setRenewFirstOne((salesDay.getRenewFirstOne() == null ? 0 : salesDay.getRenewFirstOne()) + updateNum);
                                break;
                            case 2:
                                salesDay.setRenewSecondOne((salesDay.getRenewSecondOne() == null ? 0 : salesDay.getRenewSecondOne()) + updateNum);
                                break;
                            case 3:
                                salesDay.setRenewThirdOne((salesDay.getRenewThirdOne() == null ? 0 : salesDay.getRenewThirdOne()) + updateNum);
                                break;
                            case 4:
                                salesDay.setRenewFourOne((salesDay.getRenewFourOne() == null ? 0 : salesDay.getRenewFourOne()) + updateNum);
                                break;
                            case 5:
                                salesDay.setRenewFiveOne((salesDay.getRenewFiveOne() == null ? 0 : salesDay.getRenewFiveOne()) + updateNum);
                                break;
                            case 11:
                                salesDay.setRenewElevenOne((salesDay.getRenewElevenOne() == null ? 0 : salesDay.getRenewElevenOne()) + updateNum);
                                break;
                            case 21:
                                salesDay.setRenewTwentyoneOne((salesDay.getRenewTwentyoneOne() == null ? 0 : salesDay.getRenewTwentyoneOne()) + updateNum);
                                break;
                            case 99:
                                salesDay.setRenewKidOne((salesDay.getRenewKidOne() == null ? 0 : salesDay.getRenewKidOne()) + updateNum);
                                break;
                        }
                        break;
                    case 3:
                        switch (renewStage) {
                            case 1:
                                salesDay.setRenewFirstThree((salesDay.getRenewFirstThree() == null ? 0 : salesDay.getRenewFirstThree()) + updateNum);
                                break;
                            case 2:
                                salesDay.setRenewSecondThree((salesDay.getRenewSecondThree() == null ? 0 : salesDay.getRenewSecondThree()) + updateNum);
                                break;
                            case 3:
                                salesDay.setRenewThirdThree((salesDay.getRenewThirdThree() == null ? 0 : salesDay.getRenewThirdThree()) + updateNum);
                                break;
                            case 4:
                                salesDay.setRenewFourThree((salesDay.getRenewFourThree() == null ? 0 : salesDay.getRenewFourThree()) + updateNum);
                                break;
                            case 5:
                                salesDay.setRenewFiveThree((salesDay.getRenewFiveThree() == null ? 0 : salesDay.getRenewFiveThree()) + updateNum);
                                break;
                            case 11:
                                salesDay.setRenewElevenThree((salesDay.getRenewElevenThree() == null ? 0 : salesDay.getRenewElevenThree()) + updateNum);
                                break;
                            case 21:
                                salesDay.setRenewTwentyoneThree((salesDay.getRenewTwentyoneThree() == null ? 0 : salesDay.getRenewTwentyoneThree()) + updateNum);
                                break;
                            case 99:
                                salesDay.setRenewKidThree((salesDay.getRenewKidThree() == null ? 0 : salesDay.getRenewKidThree()) + updateNum);
                                break;
                        }
                        break;
                    case 6:
                        switch (renewStage) {
                            case 1:
                                salesDay.setRenewFirstSix((salesDay.getRenewFirstSix() == null ? 0 : salesDay.getRenewFirstSix()) + updateNum);
                                break;
                            case 2:
                                salesDay.setRenewSecondSix((salesDay.getRenewSecondSix() == null ? 0 : salesDay.getRenewSecondSix()) + updateNum);
                                break;
                            case 3:
                                salesDay.setRenewThirdSix((salesDay.getRenewThirdSix() == null ? 0 : salesDay.getRenewThirdSix()) + updateNum);
                                break;
                            case 4:
                                salesDay.setRenewFourSix((salesDay.getRenewFourSix() == null ? 0 : salesDay.getRenewFourSix()) + updateNum);
                                break;
                            case 5:
                                salesDay.setRenewFiveSix((salesDay.getRenewFiveSix() == null ? 0 : salesDay.getRenewFiveSix()) + updateNum);
                                break;
                            case 11:
                                salesDay.setRenewElevenSix((salesDay.getRenewElevenSix() == null ? 0 : salesDay.getRenewElevenSix()) + updateNum);
                                break;
                            case 21:
                                salesDay.setRenewTwentyoneSix((salesDay.getRenewTwentyoneSix() == null ? 0 : salesDay.getRenewTwentyoneSix()) + updateNum);
                                break;
                            case 99:
                                salesDay.setRenewKidSix((salesDay.getRenewKidSix() == null ? 0 : salesDay.getRenewKidSix()) + updateNum);
                                break;
                        }
                        break;
                    case 12:
                        switch (renewStage) {
                            case 1:
                                salesDay.setRenewFirstTwelve((salesDay.getRenewFirstTwelve() == null ? 0 : salesDay.getRenewFirstTwelve()) + updateNum);
                                break;
                            case 2:
                                salesDay.setRenewSecondTwelve((salesDay.getRenewSecondTwelve() == null ? 0 : salesDay.getRenewSecondTwelve()) + updateNum);
                                break;
                            case 3:
                                salesDay.setRenewThirdTwelve((salesDay.getRenewThirdTwelve() == null ? 0 : salesDay.getRenewThirdTwelve()) + updateNum);
                                break;
                            case 4:
                                salesDay.setRenewFourTwelve((salesDay.getRenewFourTwelve() == null ? 0 : salesDay.getRenewFourTwelve()) + updateNum);
                                break;
                            case 5:
                                salesDay.setRenewFiveTwelve((salesDay.getRenewFiveTwelve() == null ? 0 : salesDay.getRenewFiveTwelve()) + updateNum);
                                break;
                            case 11:
                                salesDay.setRenewElevenTwelve((salesDay.getRenewElevenTwelve() == null ? 0 : salesDay.getRenewElevenTwelve()) + updateNum);
                                break;
                            case 21:
                                salesDay.setRenewTwentyoneTwelve((salesDay.getRenewTwentyoneTwelve() == null ? 0 : salesDay.getRenewTwentyoneTwelve()) + updateNum);
                                break;
                            case 99:
                                salesDay.setRenewKidTwelve((salesDay.getRenewKidTwelve() == null ? 0 : salesDay.getRenewKidTwelve()) + updateNum);
                                break;
                        }
                        break;
                }
            }
        }
        if (salesDay.getId() == null) {
            agentSalesDayMapper.insertAgentSalesDay(salesDay);
        } else {
            agentSalesDayMapper.updateAgentSalesDay(salesDay);
        }
        return true;
    }

    @Override
    public TableDataInfo<AgentSalesDay> selectSaleList(AgentSalesListDto dto) {
        List<Integer> idList;
        //专卖店销量
        if (dto.getQueryScope() == 1) {
            ExclusiveShop exclusiveShop = ExclusiveShop.builder().build();
            BeanUtils.copyProperties(dto, exclusiveShop);
            exclusiveShop.setStatus(0);
            exclusiveShop.setId(dto.getExclusiveShopId());
            idList = exclusiveShopService.getExclusiveShopIdList(exclusiveShop);
        } else {
            idList = dto.getAgentIdList();
            if (idList.size() == 0) {
                idList = agentService.getAgentIdList(dto.getmId(), dto.getSearchValue(), dto.getAgentId());
            }
        }
        List<AgentSalesDay> list = new ArrayList<>();
        idList.forEach(id -> {
            AgentSalesDay sales = getSales(id, dto.getQueryScope(), dto.getStartDate(), dto.getEndDate());
            if (StringUtils.isNotEmpty(sales.getSystem())) {
                sales.setSystem(dto.getSystem());
            }
            // 新增：根据 system 字段处理数据
            resetFieldsBySystem(sales, dto.getSystem());
            list.add(sales);
        });
        TableDataInfo<AgentSalesDay> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.SUCCESS);
        String sb = "汇总（换算年费）：共 " + String.format("%.2f", list.stream().mapToDouble(AgentSalesDay::getYearRate).sum()) + " 个【" +
                "新生：" + String.format("%.2f", list.stream().mapToDouble(AgentSalesDay::getNewTotalYearRate).sum()) + "个（" +
                list.stream().mapToInt(day -> day.getNewFirstOne() + day.getNewSecondOne() + day.getNewThirdOne() + day.getNewFourOne() + day.getNewFiveOne() + day.getNewElevenOne() + day.getNewTwentyoneOne()).sum() + "/" +
                list.stream().mapToInt(day -> day.getNewFirstThree() + day.getNewSecondThree() + day.getNewThirdThree() + day.getNewFourThree() + day.getNewFiveThree() + day.getNewElevenThree() + day.getNewTwentyoneThree()).sum() + "/" +
                list.stream().mapToInt(day -> day.getNewFirstSix() + day.getNewSecondSix() + day.getNewThirdSix() + day.getNewFourSix() + day.getNewFiveSix() + day.getNewElevenSix() + day.getNewTwentyoneSix()).sum() + "/" +
                list.stream().mapToInt(day -> day.getNewFirstTwelve() + day.getNewSecondTwelve() + day.getNewThirdTwelve() + day.getNewFourTwelve() + day.getNewFiveTwelve() + day.getNewElevenTwelve() + day.getNewTwentyoneTwelve()).sum() + "），" +
                "续费：" + String.format("%.2f", list.stream().mapToDouble(AgentSalesDay::getRenewTotalYearRate).sum()) + "个（" +
                list.stream().mapToInt(day -> day.getRenewFirstOne() + day.getRenewSecondOne() + day.getRenewThirdOne() + day.getRenewFourOne() + day.getRenewFiveOne() + day.getRenewElevenOne() + day.getRenewTwentyoneOne()).sum() + "/" +
                list.stream().mapToInt(day -> day.getRenewFirstThree() + day.getRenewSecondThree() + day.getRenewThirdThree() + day.getRenewFourThree() + day.getRenewFiveThree() + day.getRenewElevenThree() + day.getRenewTwentyoneThree()).sum() + "/" +
                list.stream().mapToInt(day -> day.getRenewFirstSix() + day.getRenewSecondSix() + day.getRenewThirdSix() + day.getRenewFourSix() + day.getRenewFiveSix() + day.getRenewElevenSix() + day.getRenewTwentyoneSix()).sum() + "/" +
                list.stream().mapToInt(day -> day.getRenewFirstTwelve() + day.getRenewSecondTwelve() + day.getRenewThirdTwelve() + day.getRenewFourTwelve() + day.getRenewFiveTwelve() + day.getRenewElevenTwelve() + day.getRenewTwentyoneTwelve()).sum() + "），" +
                "0元转：" + list.stream().mapToInt(AgentSalesDay::getZeroTransferNum).sum() + "个】。";
        rspData.setMsg(sb);
        rspData.setRows(list);
        rspData.setTotal(list.size());
        return rspData;
    }

    /**
     * 根据 system 字段重置部分字段
     */
    private void resetFieldsBySystem(AgentSalesDay day, String system) {
        if (day == null || system == null) return;
        if ("KID".equals(system)) {
            // 除少儿相关字段外全部置0
            day.setNewFirstOne(0); day.setNewFirstThree(0); day.setNewFirstSix(0); day.setNewFirstTwelve(0);
            day.setNewSecondOne(0); day.setNewSecondThree(0); day.setNewSecondSix(0); day.setNewSecondTwelve(0);
            day.setNewThirdOne(0); day.setNewThirdThree(0); day.setNewThirdSix(0); day.setNewThirdTwelve(0);
            day.setNewFourOne(0); day.setNewFourThree(0); day.setNewFourSix(0); day.setNewFourTwelve(0);
            day.setNewFiveOne(0); day.setNewFiveThree(0); day.setNewFiveSix(0); day.setNewFiveTwelve(0);
            day.setNewElevenOne(0); day.setNewElevenThree(0); day.setNewElevenSix(0); day.setNewElevenTwelve(0);
            day.setNewTwentyoneOne(0); day.setNewTwentyoneThree(0); day.setNewTwentyoneSix(0); day.setNewTwentyoneTwelve(0);
            day.setRenewFirstOne(0); day.setRenewFirstThree(0); day.setRenewFirstSix(0); day.setRenewFirstTwelve(0);
            day.setRenewSecondOne(0); day.setRenewSecondThree(0); day.setRenewSecondSix(0); day.setRenewSecondTwelve(0);
            day.setRenewThirdOne(0); day.setRenewThirdThree(0); day.setRenewThirdSix(0); day.setRenewThirdTwelve(0);
            day.setRenewFourOne(0); day.setRenewFourThree(0); day.setRenewFourSix(0); day.setRenewFourTwelve(0);
            day.setRenewFiveOne(0); day.setRenewFiveThree(0); day.setRenewFiveSix(0); day.setRenewFiveTwelve(0);
            day.setRenewElevenOne(0); day.setRenewElevenThree(0); day.setRenewElevenSix(0); day.setRenewElevenTwelve(0);
            day.setRenewTwentyoneOne(0); day.setRenewTwentyoneThree(0); day.setRenewTwentyoneSix(0); day.setRenewTwentyoneTwelve(0);
            day.setZeroTransferNum(0);
        } else if ("SYNC".equals(system)) {
            // 少儿相关字段全部置0
            day.setNewKidOne(0); day.setNewKidThree(0); day.setNewKidSix(0); day.setNewKidTwelve(0);
            day.setRenewKidOne(0); day.setRenewKidThree(0); day.setRenewKidSix(0); day.setRenewKidTwelve(0);
        }
        // 其他情况不变
    }

    /**
     * 如果是历史销量汇总数据，则缓存1周，避免重复查询。
     *
     * @param id
     * @param queryScope
     * @param startDate
     * @param endDate
     * @return
     */
    private AgentSalesDay getSales(Integer id, Integer queryScope, String startDate, String endDate) {
        AgentSalesDay agentSalesDay = null;
        String key = "agentSalesDay:id:" + id + ":queryScope:" + queryScope + ":startDate:" + startDate + ":endDate:" + endDate;
        Object object = redisCache.getCacheObject(key);
        if (object != null) {
            agentSalesDay = (AgentSalesDay) object;
        } else {
            agentSalesDay = agentSalesDayMapper.statisticsSale(id, queryScope, startDate, endDate);
            if (agentSalesDay == null) {
                agentSalesDay = new AgentSalesDay();
                if (queryScope == 1) {
                    agentSalesDay.setExclusiveShopId(id);
                    agentSalesDay.setAgentId(exclusiveShopService.getAgentIdByExclusiveShopId(id));
//                    System.out.println(exclusiveShopService.getAgentIdByExclusiveShopId(id));
                } else {
                    agentSalesDay.setAgentId(id.longValue());
                }
            }
            //获取店名
            if (queryScope == 1) {
                agentSalesDay.setExclusiveShopName(exclusiveShopService.getExclusiveShopName(agentSalesDay.getExclusiveShopId()));
            }
            //获取代理商信息
            agentSalesDay.setAgentName(agentService.getAgentName(agentSalesDay.getAgentId()));
            //如果是历史日期，缓存数据
            if (endDate != null && DateUtil.getDateTime(endDate) < DateUtil.getToday0HoursTime()) {
                redisCache.setCacheObject(key, agentSalesDay, 7, TimeUnit.DAYS);
            }
        }
        agentSalesDay.setAgentLevel(agentService.getAgentLevel(agentSalesDay.getAgentId()));
        return agentSalesDay;
    }

}
