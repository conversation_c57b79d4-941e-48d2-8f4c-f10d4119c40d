package com.redbook.system.service;

import com.redbook.system.domain.MarketNormalQuiz;

import java.util.List;

/**
 * 代理商网络营销-基础测评Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-19
 */
public interface IMarketNormalQuizService 
{
    /**
     * 查询代理商网络营销-基础测评
     * 
     * @param id 代理商网络营销-基础测评主键
     * @return 代理商网络营销-基础测评
     */
     MarketNormalQuiz selectMarketNormalQuizById(Long id);

    /**
     * 查询代理商网络营销-基础测评列表
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 代理商网络营销-基础测评集合
     */
     List<MarketNormalQuiz> selectMarketNormalQuizList(MarketNormalQuiz marketNormalQuiz);

    /**
     * 新增代理商网络营销-基础测评
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 结果
     */
     int insertMarketNormalQuiz(MarketNormalQuiz marketNormalQuiz);

    /**
     * 修改代理商网络营销-基础测评
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 结果
     */
     int updateMarketNormalQuiz(MarketNormalQuiz marketNormalQuiz);

    /**
     * 批量删除代理商网络营销-基础测评
     * 
     * @param id 需要删除的代理商网络营销-基础测评主键集合
     * @return 结果
     */
     int deleteMarketNormalQuizByIds(Long id);

    /**
     * 删除代理商网络营销-基础测评信息
     * 
     * @param id 代理商网络营销-基础测评主键
     * @return 结果
     */
     int deleteMarketNormalQuizById(Long id);
}
