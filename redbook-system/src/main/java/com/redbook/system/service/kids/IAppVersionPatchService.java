package com.redbook.system.service.kids;

import com.redbook.system.domain.kids.AppVersionPatch;

import java.util.List;

/**
 * 应用补丁包，存储版本间的增量更新包信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface IAppVersionPatchService 
{
    /**
     * 查询应用补丁包，存储版本间的增量更新包信息
     * 
     * @param id 应用补丁包，存储版本间的增量更新包信息主键
     * @return 应用补丁包，存储版本间的增量更新包信息
     */
     AppVersionPatch selectAppVersionPatchById(Long id);

    /**
     * 查询应用补丁包，存储版本间的增量更新包信息列表
     * 
     * @param appVersionPatch 应用补丁包，存储版本间的增量更新包信息
     * @return 应用补丁包，存储版本间的增量更新包信息集合
     */
     List<AppVersionPatch> selectAppVersionPatchList(AppVersionPatch appVersionPatch);

    /**
     * 新增应用补丁包，存储版本间的增量更新包信息
     * 
     * @param appVersionPatch 应用补丁包，存储版本间的增量更新包信息
     * @return 结果
     */
     int insertAppVersionPatch(AppVersionPatch appVersionPatch);

    /**
     * 修改应用补丁包，存储版本间的增量更新包信息
     * 
     * @param appVersionPatch 应用补丁包，存储版本间的增量更新包信息
     * @return 结果
     */
     int updateAppVersionPatch(AppVersionPatch appVersionPatch);

    /**
     * 批量删除应用补丁包，存储版本间的增量更新包信息
     * 
     * @param ids 需要删除的应用补丁包，存储版本间的增量更新包信息主键集合
     * @return 结果
     */
     int deleteAppVersionPatchByIds(Long[] ids);

    /**
     * 删除应用补丁包，存储版本间的增量更新包信息信息
     * 
     * @param id 应用补丁包，存储版本间的增量更新包信息主键
     * @return 结果
     */
     int deleteAppVersionPatchById(Long id);
}
