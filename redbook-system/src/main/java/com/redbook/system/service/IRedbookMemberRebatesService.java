package com.redbook.system.service;

import com.redbook.system.domain.RedbookMemberRebates;

import java.util.List;

/**
 * 动态价格优惠档位Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface IRedbookMemberRebatesService 
{
    /**
     * 查询动态价格优惠档位
     * 
     * @param id 动态价格优惠档位主键
     * @return 动态价格优惠档位
     */
     RedbookMemberRebates selectRedbookMemberRebatesById(Long id);

    /**
     * 查询动态价格优惠档位列表
     * 
     * @param redbookMemberRebates 动态价格优惠档位
     * @return 动态价格优惠档位集合
     */
     List<RedbookMemberRebates> selectRedbookMemberRebatesList(RedbookMemberRebates redbookMemberRebates);

    /**
     * 新增动态价格优惠档位
     * 
     * @param redbookMemberRebates 动态价格优惠档位
     * @return 结果
     */
     int insertRedbookMemberRebates(RedbookMemberRebates redbookMemberRebates);

    /**
     * 修改动态价格优惠档位
     * 
     * @param redbookMemberRebates 动态价格优惠档位
     * @return 结果
     */
     int updateRedbookMemberRebates(RedbookMemberRebates redbookMemberRebates);

    /**
     * 批量删除动态价格优惠档位
     * 
     * @param ids 需要删除的动态价格优惠档位主键集合
     * @return 结果
     */
     int deleteRedbookMemberRebatesByIds(Long[] ids);

    /**
     * 删除动态价格优惠档位信息
     * 
     * @param id 动态价格优惠档位主键
     * @return 结果
     */
     int deleteRedbookMemberRebatesById(Long id);
}
