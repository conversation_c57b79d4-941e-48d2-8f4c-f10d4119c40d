package com.redbook.system.service;

import com.redbook.common.core.domain.AjaxResult;
import com.redbook.system.domain.BizClue;
import com.redbook.system.domain.dto.BizCluePostDTO;
import com.redbook.system.domain.vo.BizClueEditVO;
import com.redbook.system.domain.vo.BizClueListVO;
import com.redbook.system.domain.vo.BizClueReferralVO;
import com.redbook.system.domain.vo.BizClueRequestVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 线索Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
public interface IBizClueService 
{
    /**
     * 查询线索
     * 
     * @param clueId 线索主键
     * @return 线索
     */
    BizClueEditVO selectBizClueByClueId(Long clueId);

    /**
     * 查询线索列表
     * 
     * @param bizClueRequestVO 线索
     * @return 线索集合
     */
     List<BizClueListVO> selectBizClueList(BizClueRequestVO bizClueRequestVO);

    /**
     * 查询线索转介绍列表
     *
     * @param clueId 线索id
     * @return 线索集合
     */
    List<BizClueReferralVO> selectClueListForReferral(Long clueId);

    /**
     * 批量分配
     * 
     * @param bizCluePostDTO 线索ids+学管师id
     * @return 结果
     */
     int batchAssign(BizCluePostDTO bizCluePostDTO);
    /**
     * 回收共有池
     *
     * @param bizCluePostDTO 线索ids
     * @return 结果
     */
     int recycleCommonPool(BizCluePostDTO bizCluePostDTO);
    /**
     * 批量转移
     *
     * @param bizCluePostDTO 线索ids
     * @return 结果
     */
    int batchTransfer(BizCluePostDTO bizCluePostDTO);

    /**
     * 新增线索
     *
     * @param bizClue 线索
     * @return 结果
     */
     int insertBizClue(BizClue bizClue);
    /**
     * 批量新增线索
     *
     * @param bizClueList 线索
     * @return 结果
     */
    int batchInsertBizClue(List<BizClue> bizClueList);
    /**
     * 修改线索
     * 
     * @param bizClue 线索
     * @return 结果
     */
     int updateBizClue(BizClue bizClue);

    /**
     * 批量删除线索
     * 
     * @param clueIds 需要删除的线索主键集合
     * @return 结果
     */
     int deleteBizClueByClueIds(Long[] clueIds);

    /**
     * 删除线索信息
     * 
     * @param clueId 线索主键
     * @return 结果
     */
     int deleteBizClueByClueId(Long clueId);


    /**
     * 导入
     * @param file
     * @param agentId
     * @param exclusiveShopId
     */
    AjaxResult importData(MultipartFile file, Long agentId, Integer exclusiveShopId) throws Exception;
}
