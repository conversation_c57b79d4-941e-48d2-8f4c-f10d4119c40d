package com.redbook.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.system.domain.MarketNormalQuiz;
import com.redbook.system.enums.MarketQuizEnum;
import com.redbook.system.mapper.MarketNormalQuizMapper;
import com.redbook.system.service.IExclusiveShopService;
import com.redbook.system.service.IMarketNormalQuizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 代理商网络营销-基础测评Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-19
 */
@Service
public class MarketNormalQuizServiceImpl extends ServiceImpl<MarketNormalQuizMapper, MarketNormalQuiz> implements IMarketNormalQuizService
{
    @Autowired
    private MarketNormalQuizMapper marketNormalQuizMapper;
    @Autowired
    private IExclusiveShopService shopService;
    /**
     * 查询代理商网络营销-基础测评
     * 
     * @param id 代理商网络营销-基础测评主键
     * @return 代理商网络营销-基础测评
     */
    @Override
    public MarketNormalQuiz selectMarketNormalQuizById(Long id)
    {
        return marketNormalQuizMapper.selectMarketNormalQuizById(id);
    }

    /**
     * 查询代理商网络营销-基础测评列表
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 代理商网络营销-基础测评
     */
    @Override
    public List<MarketNormalQuiz> selectMarketNormalQuizList(MarketNormalQuiz marketNormalQuiz)
    {
        return marketNormalQuizMapper.selectMarketNormalQuizList(marketNormalQuiz);
    }

    /**
     * 新增代理商网络营销-基础测评
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 结果
     */
    @Override
    public int insertMarketNormalQuiz(MarketNormalQuiz marketNormalQuiz)
    {
        List<MarketNormalQuiz> marketNormalQuizs = marketNormalQuizMapper.selectMarketNormalQuizList(marketNormalQuiz);
        if(CollectionUtil.isNotEmpty(marketNormalQuizs) && marketNormalQuizs.size() >= 3){
            //创建之后，更新操作
            List<MarketNormalQuiz> collect = marketNormalQuizs.stream().filter(marketNormalQuiz1 -> marketNormalQuiz1.getQuizType().equals(marketNormalQuiz.getQuizType()))
                    .collect(Collectors.toList());
            MarketNormalQuiz update = new MarketNormalQuiz();
            update.setId(collect.get(0).getId());
            update.setStatus(1);
            update.setCreateTime(new Date());
            marketNormalQuizMapper.update(update);
            return 1;
        }
        marketNormalQuiz.setStatus(1);
        marketNormalQuiz.setCreateTime(new Date());
        marketNormalQuiz.setExclusiveShopId(marketNormalQuiz.getExclusiveShopId());
        marketNormalQuizMapper.insertMarketNormalQuiz(marketNormalQuiz);
        List<MarketQuizEnum.Child> childListByType = MarketQuizEnum.getChildListByType(marketNormalQuiz.getQuizType());
        for (MarketQuizEnum.Child child : childListByType) {
            MarketNormalQuiz bean = new MarketNormalQuiz();
            bean.setAgentId(marketNormalQuiz.getAgentId());
            bean.setExclusiveShopId(marketNormalQuiz.getExclusiveShopId());
            bean.setQuizName(child.getName());
            bean.setQuizType(child.getType());
            bean.setStatus(2);
            marketNormalQuizMapper.insertMarketNormalQuiz(bean);
        }
        return 1;
    }

    /**
     * 修改代理商网络营销-基础测评
     * 
     * @param marketNormalQuiz 代理商网络营销-基础测评
     * @return 结果
     */
    @Override
    public int updateMarketNormalQuiz(MarketNormalQuiz marketNormalQuiz)
    {
        return marketNormalQuizMapper.updateMarketNormalQuiz(marketNormalQuiz);
    }

    /**
     * 批量删除代理商网络营销-基础测评
     * 
     * @param id 需要删除的代理商网络营销-基础测评主键
     * @return 结果
     */
    @Override
    public int deleteMarketNormalQuizByIds(Long id)
    {
        MarketNormalQuiz marketNormalQuiz = new MarketNormalQuiz();
        marketNormalQuiz.setId(id);
        marketNormalQuiz.setStatus(2);
        return marketNormalQuizMapper.update(marketNormalQuiz);
//        return marketNormalQuizMapper.deleteMarketNormalQuizByIds(ids);
    }

    /**
     * 删除代理商网络营销-基础测评信息
     * 
     * @param id 代理商网络营销-基础测评主键
     * @return 结果
     */
    @Override
    public int deleteMarketNormalQuizById(Long id)
    {
        return marketNormalQuizMapper.deleteMarketNormalQuizById(id);
    }
}
