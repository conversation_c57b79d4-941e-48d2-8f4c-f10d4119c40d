package com.redbook.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.market.ConsultPictureFolder;
import com.redbook.system.domain.market.ConsultPictureFolderDTO;
import com.redbook.system.mapper.AgentMapper;
import com.redbook.system.mapper.ConsultPictureFolderMapper;
import com.redbook.system.service.IConsultPictureFolderService;
import com.redbook.system.service.IExclusiveShopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
/**
 * 咨询夹图片Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-22
 */
@Service
public class ConsultPictureFolderServiceImpl extends ServiceImpl<ConsultPictureFolderMapper, ConsultPictureFolder> implements IConsultPictureFolderService
{
    @Autowired
    private ConsultPictureFolderMapper consultPictureFolderMapper;
    @Autowired
    private AgentMapper agentMapper;
    @Autowired
    private IExclusiveShopService shopService;

    /**
     * 查询咨询夹图片
     * 
     * @param id 咨询夹图片主键
     * @return 咨询夹图片
     */
    @Override
    public ConsultPictureFolder selectConsultPictureFolderById(Integer id)
    {
        return consultPictureFolderMapper.selectConsultPictureFolderById(id);
    }

    /**
     * 查询咨询夹图片列表
     * 
     * @param consultPictureFolder 咨询夹图片
     * @return 咨询夹图片
     */
    @Override
    public List<ConsultPictureFolder> selectConsultPictureFolderList(ConsultPictureFolder consultPictureFolder)
    {
        List<ConsultPictureFolder> consultPictureFolders = this.systemPictureList();
        Agent agent = agentMapper.selectAgentById(consultPictureFolder.getAgentId());
        int num = consultPictureFolderMapper.countSystemPictureNum(consultPictureFolder.getAgentId(), consultPictureFolder.getExclusiveShopId());
        if(num == 0){
            List<ConsultPictureFolder> list = new ArrayList<>();
            for(ConsultPictureFolder item : consultPictureFolders){
                ConsultPictureFolder pictureFolder = new ConsultPictureFolder();
                pictureFolder.setPictureUrl(item.getPictureUrl());
                pictureFolder.setAid(agent.getAid());
                pictureFolder.setAgentId(consultPictureFolder.getAgentId());
                pictureFolder.setSysType(2);
                pictureFolder.setExclusiveShopId(consultPictureFolder.getExclusiveShopId());
                pictureFolder.setSort(item.getSort());
                pictureFolder.setIsHide(1);
                list.add(pictureFolder);
            }
            if(CollectionUtil.isNotEmpty(list)){
                consultPictureFolderMapper.batchInsert(list);
            }
        }
        return consultPictureFolderMapper.selectConsultPictureFolderList(consultPictureFolder);
    }

    @Override
    public List<ConsultPictureFolder> systemPictureList() {
        List<ConsultPictureFolder> consultPictureFolders = consultPictureFolderMapper.systemPictureFolderList();
        return consultPictureFolders;
    }

    /**
     * 新增咨询夹图片
     * 
     * @param consultPictureFolder 咨询夹图片
     * @return 结果
     */
    @Override
    public int insertConsultPictureFolder(ConsultPictureFolder consultPictureFolder)
    {
        Agent agent = agentMapper.selectAgentById(consultPictureFolder.getAgentId());
        consultPictureFolder.setCreateTime(DateUtils.getNowDate());
        consultPictureFolder.setSysType(2);
        consultPictureFolder.setAid(agent.getAid());
        return consultPictureFolderMapper.insertConsultPictureFolder(consultPictureFolder);
    }

    @Override
    public int batchUpdateSort(ConsultPictureFolderDTO consultPictureFolderDTO) {
        List<ConsultPictureFolderDTO.Child> childList = consultPictureFolderDTO.getChildList();
        if(CollectionUtil.isNotEmpty(childList)){
            childList.forEach(child -> {
                ConsultPictureFolder consultPictureFolder = new ConsultPictureFolder();
                consultPictureFolder.setId(child.getId());
                consultPictureFolder.setSort(child.getSort());
                consultPictureFolderMapper.updateConsultPictureFolder(consultPictureFolder);
            });
        }
        return 1;
    }

    /**
     * 修改咨询夹图片
     * 
     * @param consultPictureFolder 咨询夹图片
     * @return 结果
     */
    @Override
    public int updateConsultPictureFolder(ConsultPictureFolder consultPictureFolder)
    {
        return consultPictureFolderMapper.updateConsultPictureFolder(consultPictureFolder);
    }

    /**
     * 批量删除咨询夹图片
     * 
     * @param ids 需要删除的咨询夹图片主键
     * @return 结果
     */
    @Override
    public int deleteConsultPictureFolderByIds(Long[] ids)
    {

        return consultPictureFolderMapper.deleteConsultPictureFolderByIds(ids);
    }

    /**
     * 删除咨询夹图片信息
     * 
     * @param id 咨询夹图片主键
     * @return 结果
     */
    @Override
    public int deleteConsultPictureFolderById(Integer id)
    {
        ConsultPictureFolder pictureFolder = consultPictureFolderMapper.selectConsultPictureFolderById(id);
        if(pictureFolder.getPictureUrl().contains("introduce")){
            throw new ServiceException("系统图片不可删除");
        }
        return consultPictureFolderMapper.deleteConsultPictureFolderById(id);
    }
}
