package com.redbook.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.utils.DateUtils;
import com.redbook.system.domain.ProductCategory;
import com.redbook.system.mapper.ProductCategoryMapper;
import com.redbook.system.service.IProductCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 商品类别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-05-09
 */
@Service
public class ProductCategoryServiceImpl extends ServiceImpl<ProductCategoryMapper, ProductCategory> implements IProductCategoryService
{
    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    /**
     * 查询商品类别
     * 
     * @param id 商品类别主键
     * @return 商品类别
     */
    @Override
    public ProductCategory selectProductCategoryById(Integer id)
    {
        return productCategoryMapper.selectProductCategoryById(id);
    }

    /**
     * 查询商品类别列表
     * 
     * @param productCategory 商品类别
     * @return 商品类别
     */
    @Override
    public List<ProductCategory> selectProductCategoryList(ProductCategory productCategory)
    {
        return productCategoryMapper.selectProductCategoryList(productCategory);
    }

    /**
     * 新增商品类别
     * 
     * @param productCategory 商品类别
     * @return 结果
     */
    @Override
    public int insertProductCategory(ProductCategory productCategory)
    {
        productCategory.setCreateTime(DateUtils.getNowDate());
        return productCategoryMapper.insertProductCategory(productCategory);
    }

    /**
     * 修改商品类别
     * 
     * @param productCategory 商品类别
     * @return 结果
     */
    @Override
    public int updateProductCategory(ProductCategory productCategory)
    {
        productCategory.setUpdateTime(DateUtils.getNowDate());
        return productCategoryMapper.updateProductCategory(productCategory);
    }

    /**
     * 批量删除商品类别
     * 
     * @param ids 需要删除的商品类别主键
     * @return 结果
     */
    @Override
    public int deleteProductCategoryByIds(Integer[] ids)
    {
        return productCategoryMapper.deleteProductCategoryByIds(ids);
    }

    /**
     * 删除商品类别信息
     * 
     * @param id 商品类别主键
     * @return 结果
     */
    @Override
    public int deleteProductCategoryById(Integer id)
    {
        return productCategoryMapper.deleteProductCategoryById(id);
    }
}
