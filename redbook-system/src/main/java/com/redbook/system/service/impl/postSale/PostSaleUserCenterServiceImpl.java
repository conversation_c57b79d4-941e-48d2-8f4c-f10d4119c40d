package com.redbook.system.service.impl.postSale;

import com.alibaba.fastjson.JSONObject;
import com.redbook.common.constant.Constants;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.sms.ZTSmsSender;
import com.redbook.common.utils.IdentityGenerator;
import com.redbook.common.utils.StringUtils;
import com.redbook.system.domain.*;
import com.redbook.system.domain.vo.postSale.PostSaleConfigVo;
import com.redbook.system.enums.PostSaleConfigTypeEnum;
import com.redbook.system.enums.PostSaleSmsBusinessEnum;
import com.redbook.system.mapper.PostSaleConfigMapper;
import com.redbook.system.mapper.PostSaleQuestionArticleMapper;
import com.redbook.system.mapper.PostSaleSparePartMapper;
import com.redbook.system.mapper.PostSaleUserMapper;
import com.redbook.system.service.postSale.IPostSaleUserCenterService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 寄修单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@Service
public class PostSaleUserCenterServiceImpl implements IPostSaleUserCenterService {

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private PostSaleConfigMapper postSaleConfigMapper;
    @Autowired
    private PostSaleQuestionArticleMapper postSaleQuestionArticleMapper;
    @Autowired
    private PostSaleSparePartMapper postSaleSparePartMapper;
    @Autowired
    private PostSaleUserMapper postSaleUserMapper;
    public static final String POST_SALE_SMS_CODE_KEY = "postSaleSmsCodes:";

    @Override
    public PostSaleConfigVo serviceConfig() {
        PostSaleConfigVo postSaleConfigVo = new PostSaleConfigVo();
        List<PostSaleConfig> postSaleConfigs = postSaleConfigMapper.selectPostSaleConfigListFromApplet();
        if (CollectionUtils.isNotEmpty(postSaleConfigs)) {
            postSaleConfigs.stream().filter(item -> Objects.equals(item.getConfigType(), PostSaleConfigTypeEnum.SERVICE_CENTER)).findFirst().ifPresent(item -> {
                postSaleConfigVo.setPostSaleServiceCentre(JSONObject.parseObject(item.getConfigValue(), PostSaleServiceCentre.class));
            });
            postSaleConfigs.stream().filter(item -> Objects.equals(item.getConfigType(), PostSaleConfigTypeEnum.SERVICE_HOTLINE)).findFirst().ifPresent(item -> {
                postSaleConfigVo.setServiceHotline(item.getConfigValue());
            });
            postSaleConfigs.stream().filter(item -> Objects.equals(item.getConfigType(), PostSaleConfigTypeEnum.SPARE_PART_REMARKS)).findFirst().ifPresent(item -> {
                postSaleConfigVo.setSparePartRemarks(item.getConfigValue());
            });
        }
        return postSaleConfigVo;
    }

    @Override
    public List<PostSaleQuestionArticle> questionList(Integer pageNum, Integer pageSize) {
        int offset = (pageNum - 1) * pageSize;
        return postSaleQuestionArticleMapper.selectPostSaleQuestionArticleListFromApplet(offset, pageSize);
    }

    @Override
    public PostSaleQuestionArticle questionDetail(Integer quesitonId) {
        return postSaleQuestionArticleMapper.selectPostSaleQuestionArticleById(Long.valueOf(quesitonId));
    }

    @Override
    public List<PostSaleSparePart> sparePartList() {
        return postSaleSparePartMapper.selectPostSaleSparePartListFromApplet();
    }

    @Override
    public Boolean sms(String phone, PostSaleSmsBusinessEnum postSaleSmsBusinessEnum) {
        if (!StringUtils.isValidPhoneNumber(phone)) {
            throw new ServiceException("请输入正确的手机号");
        }
        switch (postSaleSmsBusinessEnum) {
            case VERIFICATION_CODE:
                //发送验证码
                // 保存验证码信息
                String verifyKey = POST_SALE_SMS_CODE_KEY + postSaleSmsBusinessEnum + ":" + phone;
                if (redisCache.getCacheObject(verifyKey) != null) {
                    throw new ServiceException("验证码已发送，请稍后再试");
                }
                // 生成验证码
                String code = IdentityGenerator.randomString(4);
                Boolean sendMsg = ZTSmsSender.sendVerificationCode(code, phone);
                if (sendMsg) {
                    redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
                }
                return true;
        }
        return null;
    }

    @Override
    public Boolean updatePhone(String phone, String verificationCode, String userId, int userType) {
        if (!StringUtils.isValidPhoneNumber(phone)) {
            throw new ServiceException("请输入正确的手机号");
        }
        String verifyKey = POST_SALE_SMS_CODE_KEY + PostSaleSmsBusinessEnum.VERIFICATION_CODE + ":" + phone;
        Object objectCode = redisCache.getCacheObject(verifyKey);
        if (objectCode == null) {
            throw new ServiceException("未获取到验证码，或验证码已失效，请重新获取。");
        }
        if (!StringUtils.equals(verificationCode, objectCode.toString())) {
            throw new ServiceException("验证码错误");
        }
        int i = 0;
        PostSaleUser postSaleUser = postSaleUserMapper.selectPostSaleUserById(Long.valueOf(userId));
        if (postSaleUser != null) {
            postSaleUser.setPhone(phone);
            i = postSaleUserMapper.updatePostSaleUser(postSaleUser);
        }
        return i > 0;
    }
}
