package com.redbook.system.service.oldSystem;


import com.redbook.system.domain.old.HuoXingThreeBean;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022 -05-29 11:44
 */
public interface IHuoXingThreeService {

    /**
     * 写入续费记录
     * @return
     */
    boolean addRenewRecord(HuoXingThreeBean huoXingThreeBean);
    List<HuoXingThreeBean> getRenewRecord(Long contractorId, String memberUserId);

    Map<String, Object> getQiJiYunAgent(long contractorId);
    boolean addQiJiYunPresentAccountNum(long contractorId, int addOneMonthNum, int addThreeMonthNum, int addSixMonthNum, int addTwelveMonthNum);
    List<Map<String,Object>> getQiJiYunAccountList(int channelAgentId, String timeLen, int seriesId);

    boolean openQiJiYunAccount(int channelAgentId, int openNum, int accountMonth, int seriesId);
}
