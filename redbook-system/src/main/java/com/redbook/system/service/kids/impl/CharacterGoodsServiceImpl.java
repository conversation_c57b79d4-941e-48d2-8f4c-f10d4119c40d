package com.redbook.system.service.kids.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.redbook.common.annotation.DataSource;
import com.redbook.common.enums.DataSourceType;
import com.redbook.system.domain.kids.CharacterGoods;
import com.redbook.system.mapper.CharacterGoodsMapper;
import com.redbook.system.service.kids.ICharacterGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * 角色道具Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
@Service
@DataSource(value = DataSourceType.KIDS)
public class CharacterGoodsServiceImpl extends ServiceImpl<CharacterGoodsMapper, CharacterGoods> implements ICharacterGoodsService
{
    @Autowired
    private CharacterGoodsMapper characterGoodsMapper;

    /**
     * 查询角色道具
     * 
     * @param id 角色道具主键
     * @return 角色道具
     */
    @Override
    public CharacterGoods selectCharacterGoodsById(Long id)
    {
        return characterGoodsMapper.selectCharacterGoodsById(id);
    }

    /**
     * 查询角色道具列表
     * 
     * @param characterGoods 角色道具
     * @return 角色道具
     */
    @Override
    public List<CharacterGoods> selectCharacterGoodsList(CharacterGoods characterGoods)
    {
        return characterGoodsMapper.selectCharacterGoodsList(characterGoods);
    }

    /**
     * 新增角色道具
     * 
     * @param characterGoods 角色道具
     * @return 结果
     */
    @Override
    public int insertCharacterGoods(CharacterGoods characterGoods)
    {
        return characterGoodsMapper.insertCharacterGoods(characterGoods);
    }

    /**
     * 修改角色道具
     * 
     * @param characterGoods 角色道具
     * @return 结果
     */
    @Override
    public int updateCharacterGoods(CharacterGoods characterGoods)
    {
        return characterGoodsMapper.updateCharacterGoods(characterGoods);
    }

    /**
     * 批量删除角色道具
     * 
     * @param ids 需要删除的角色道具主键
     * @return 结果
     */
    @Override
    public int deleteCharacterGoodsByIds(Long[] ids)
    {
        return characterGoodsMapper.deleteCharacterGoodsByIds(ids);
    }

    /**
     * 删除角色道具信息
     * 
     * @param id 角色道具主键
     * @return 结果
     */
    @Override
    public int deleteCharacterGoodsById(Long id)
    {
        return characterGoodsMapper.deleteCharacterGoodsById(id);
    }
}
