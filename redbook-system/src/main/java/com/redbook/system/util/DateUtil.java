package com.redbook.system.util;

import com.redbook.system.domain.DateSplit;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;


public class DateUtil {
    protected static Log logger = LogFactory.getLog(DateUtil.class);

    // 格式：年－月－日 小时：分钟：秒
    public static final String FORMAT_ONE = "yyyy-MM-dd HH:mm:ss";

    // 格式：年－月－日 小时：分钟
    public static final String FORMAT_TWO = "yyyy-MM-dd HH:mm";
    public static final String FORMAT_TWO2 = "yyyyMMddHHmmss";

    // 格式：年月日 小时分钟秒
    public static final String FORMAT_THREE = "yyyyMMdd-HHmmss";

    // 格式：年－月－日
    public static final String LONG_DATE_FORMAT = "yyyy-MM-dd";

    // 格式：月－日
    public static final String SHORT_DATE_FORMAT = "MM-dd";

    // 格式：小时：分钟：秒
    public static final String LONG_TIME_FORMAT = "HH:mm:ss";

    //格式：年-月
    public static final String MONTG_DATE_FORMAT = "yyyy-MM";

    // 年的加减
    public static final int SUB_YEAR = Calendar.YEAR;

    // 月加减
    public static final int SUB_MONTH = Calendar.MONTH;

    // 天的加减
    public static final int SUB_DAY = Calendar.DATE;

    // 小时的加减
    public static final int SUB_HOUR = Calendar.HOUR;

    // 分钟的加减
    public static final int SUB_MINUTE = Calendar.MINUTE;

    // 秒的加减
    public static final int SUB_SECOND = Calendar.SECOND;

    static final String[] dayNames = { "星期日", "星期一", "星期二", "星期三", "星期四",
            "星期五", "星期六" };

    @SuppressWarnings("unused")
    private static final SimpleDateFormat timeFormat = new SimpleDateFormat(
            "yyyy-MM-dd HH:mm:ss");

    public DateUtil() {
    }
    public static long getDateTime(String dateStr){
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    	long d = 0L;
    	try {
			 d = sdf.parse(dateStr).getTime();
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return d;
    }
    /**
     * 把符合日期格式的字符串转换为日期类型
     */
    public static Date stringtoDate(String dateStr, String format) {
        Date d = null;
        SimpleDateFormat formater = new SimpleDateFormat(format);
        try {
            formater.setLenient(false);
            d = formater.parse(dateStr);
        } catch (Exception e) {
            // log.error(e);
            d = null;
        }
        return d;
    }

    /**
     * 把符合日期格式的字符串转换为日期类型
     */
    public static Date stringtoDate(String dateStr, String format,
            ParsePosition pos) {
        Date d = null;
        SimpleDateFormat formater = new SimpleDateFormat(format);
        try {
            formater.setLenient(false);
            d = formater.parse(dateStr, pos);
        } catch (Exception e) {
            d = null;
        }
        return d;
    }

    /**
     * 把日期转换为字符串
     */
    public static String dateToString(Date date) {
        String result = "";
        SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
        try {
            result = formater.format(date);
        } catch (Exception e) {
            // log.error(e);
        }
        return result;
    }
    public static String dateToString(Date date, String format) {
        String result = "";
        SimpleDateFormat formater = new SimpleDateFormat(format);
        try {
            result = formater.format(date);
        } catch (Exception e) {
            // log.error(e);
        }
        return result;
    }

    /**
     * 获取当前时间的指定格式
     */
    public static String getCurrDate(String format) {
        return dateToString(new Date(), format);
    }

    public static String dateSub(int dateKind, String dateStr, int amount) {
        Date date = stringtoDate(dateStr, FORMAT_ONE);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(dateKind, amount);
        return dateToString(calendar.getTime(), FORMAT_ONE);
    }

    /**
     * 两个日期相减
     * @return 相减得到的秒数
     */
    public static long timeSub(String firstTime, String secTime) {
        long first = stringtoDate(firstTime, FORMAT_ONE).getTime();
        long second = stringtoDate(secTime, FORMAT_ONE).getTime();
        return (second - first) / 1000;
    }
    /**
     * 两个日期的天数差
     * @param firstDate "yyyy-MM-dd"
     * @param secDate
     * @return
     */
    public static int daySub(String firstDate, String secDate){
    	long first = stringtoDate(firstDate, LONG_DATE_FORMAT).getTime()/1000;
        long second = stringtoDate(secDate, LONG_DATE_FORMAT).getTime()/1000;
        return (int)((second - first) / (24*60*60));
    }

    /**
     * 获得某月的天数
     */
    public static int getDaysOfMonth(String year, String month) {
        int days = 0;
        if (month.equals("1") || month.equals("3") || month.equals("5")
                || month.equals("7") || month.equals("8") || month.equals("10")
                || month.equals("12")) {
            days = 31;
        } else if (month.equals("4") || month.equals("6") || month.equals("9")
                || month.equals("11")) {
            days = 30;
        } else {
            if ((Integer.parseInt(year) % 4 == 0 && Integer.parseInt(year) % 100 != 0)
                    || Integer.parseInt(year) % 400 == 0) {
                days = 29;
            } else {
                days = 28;
            }
        }

        return days;
    }

    /**
     * 获取某年某月的天数
     */
    public static int getDaysOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获得当前日期
     */
    public static int getToday() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获得当前月份
     */
    public static int getToMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 获得当前年份
     */
    public static int getToYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 返回日期的天
     */
    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DATE);
    }

    /**
     * 返回日期的年
     */
    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 返回日期的月份，1-12
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 计算两个日期相差的天数，如果date2 > date1 返回正数，否则返回负数
     */
    public static long dayDiff(Date date1, Date date2) {
        return (date2.getTime() - date1.getTime()) / 86400000;
    }

    /**
     * 比较两个日期的年差
     */
    public static int yearDiff(String before, String after) {
        Date beforeDay = stringtoDate(before, LONG_DATE_FORMAT);
        Date afterDay = stringtoDate(after, LONG_DATE_FORMAT);
        return getYear(afterDay) - getYear(beforeDay);
    }

    /**
     * 比较指定日期与当前日期的差
     */
    public static int yearDiffCurr(String after) {
        Date beforeDay = new Date();
        Date afterDay = stringtoDate(after, LONG_DATE_FORMAT);
        return getYear(beforeDay) - getYear(afterDay);
    }
    
    /**
     * 比较指定日期与当前日期的差
     */
    public static long dayDiffCurr(String before) {
        Date currDate = DateUtil.stringtoDate(currDate(LONG_DATE_FORMAT), LONG_DATE_FORMAT);
        Date beforeDate = stringtoDate(before, LONG_DATE_FORMAT);
        return (currDate.getTime() - beforeDate.getTime()) / 86400000;

    }

    public static String currDate(String dateType)
  {
     SimpleDateFormat sf = new SimpleDateFormat(dateType);
     return sf.format(new Date());
  }
    
    /**
     * 获取每月的第一周
     */
    public static int getFirstWeekdayOfMonth(int year, int month) {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.SATURDAY); // 星期天为第一天
        c.set(year, month - 1, 1);
        return c.get(Calendar.DAY_OF_WEEK);
    }
    /**
     * 获取每月的最后一周
     */
    public static int getLastWeekdayOfMonth(int year, int month) {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.SATURDAY); // 星期天为第一天
        c.set(year, month - 1, getDaysOfMonth(year, month));
        return c.get(Calendar.DAY_OF_WEEK);
    }
    
    /**
     * 获取本周一日期
     * @return
     */
	public static String getFirstDayOfWeek(){
    	Calendar calendar = Calendar.getInstance();
    	while(calendar.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) { 
    		calendar.add(Calendar.DATE, -1); 
    	}
    	return dateToString(calendar.getTime(), "yyyy-MM-dd");
    }
	
	/**
     * 获取上周一日期
     * @return
     */
	public static String getPrevWeekMondayDay(){
    	Calendar calendar = Calendar.getInstance();
    	calendar.add(Calendar.DATE, -7); 
    	while(calendar.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) { 
    		calendar.add(Calendar.DATE, -1); 
    	}
    	int year = calendar.get(Calendar.YEAR);
    	int month = calendar.get(Calendar.MONTH) + 1;
    	int day = calendar.get(Calendar.DAY_OF_MONTH);
    	return year+"-"+month+"-"+day;
    }

    /**
     * 获取下周一日期
     * @return
     */
    public static String getNextWeekMondayDay(){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, +7);
        while(calendar.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) {
            calendar.add(Calendar.DATE, -1);
        }
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        return year+"-"+month+"-"+day;
    }

    /**
     * 获得当前日期字符串，格式"yyyy_MM_dd_HH_mm_ss"
     * 
     * @return
     */
   /* public static String getCurrent() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int minute = cal.get(Calendar.MINUTE);
        int second = cal.get(Calendar.SECOND);
        StringBuffer sb = new StringBuffer();
        sb.append(year).append("_").append(FormatUtils.padZero(month, 2))
                .append("_").append(FormatUtils.padZero(day, 2)).append("_")
                .append(FormatUtils.padZero(hour, 2)).append("_").append(
                		FormatUtils.padZero(minute, 2)).append("_").append(
                				FormatUtils.padZero(second, 2));
        return sb.toString();
    }*/

    /**
     * 获得当前日期字符串，格式"yyyy-MM-dd HH:mm:ss"
     * 
     * @return
     */
    public static String getNow() {
        Calendar today = Calendar.getInstance();
        return dateToString(today.getTime(), FORMAT_ONE);
    }

    /**
     * 判断日期是否有效,包括闰年的情况
     * 
     * @param date
     *          YYYY-mm-dd
     * @return
     */
    public static boolean isDate(String date) {
        String reg = "^((\\d{2}(([02468][048])|([13579][26]))-?((((0?" + "[13578])|(1[02]))-?((0?[1-9])|([1-2][0-9])|(3[01])))" +
                "|(((0?[469])|(11))-?((0?[1-9])|([1-2][0-9])|(30)))|" +
                "(0?2-?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][12" +
                "35679])|([13579][01345789]))-?((((0?[13578])|(1[02]))" +
                "-?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))" +
                "-?((0?[1-9])|([1-2][0-9])|(30)))|(0?2-?((0?[" +
                "1-9])|(1[0-9])|(2[0-8]))))))";
        Pattern p = Pattern.compile(reg);
        return p.matcher(date).matches();
    }
    
    /**
     * 根据日期(周一)计算出该日期处于当年的第几周
     * @param date
     * @return
     */
    public static int getWeekOfYear(Date date){
    	Calendar cal = Calendar.getInstance();
    	cal.setTime(date);
    	int days = cal.get(Calendar.DAY_OF_YEAR);
    	int weekNum = (days-1)/7+1;
    	return weekNum;
    }
    
    /**
     * 根据周一的日期计算出这周周日的日期
     * @param date
     * @return
     */
    public static Date getWeekEndDate(Date date){
    	Calendar cal = Calendar.getInstance();
    	cal.setTime(date);
    	cal.add(Calendar.DATE, 6);
    	Date date2 = cal.getTime();
    	return date2;
    }
    
    /**
     * 获取年月 yyyy-MM
     * @return
     */
    public static String getYearAndMonth(){
    	return (new SimpleDateFormat("yyyy-MM")).format(new Date());
    }
    /**
     * 获取现在是上午AM还是下午PM
     * @return
     */
    public static String getAPM(){
    	if(Integer.valueOf((new SimpleDateFormat("HH")).format(new Date()))<12){
    		return "AM";
    	}else{
    		return "PM";
    	}
	}
    
    /**
     * 计算两个日期之间的日期差
     * @param early
     * @param later
     * @return 日期差拼成的字符串：yyyy年MM月dd天
     */
    public static String getDateDifference(Date early, Date later){
    	if(early.after(later)){
    		return "时间输入有误！";
    	}
    	int earlyYear = early.getYear();
    	int laterYear = later.getYear();
    	int earlyMonth = early.getMonth();
    	int laterMonth = later.getMonth();
    	int earlyDate = early.getDate();
    	int laterDate = later.getDate();
    	
    	//获取年月天的差值
    	int dateDiff = laterDate-earlyDate;
    	if(dateDiff<0){
    		if(--laterMonth<0){
    			laterYear--;
    		}
    		int[] MonthDays = {31,getDaysOfMonth(laterYear, laterMonth+1),31,30,31,30,31,31,30,31,30,31};
    		laterDate+=MonthDays[laterMonth];
    		dateDiff = laterDate-earlyDate;
    	}
    	int monthDiff = laterMonth-earlyMonth;
    	if(monthDiff<0){
    		laterYear--;
    		monthDiff +=12;
    	}
    	int yearDiff = laterYear-earlyYear;
    	
    	//拼接字符串
    	StringBuilder sb = new StringBuilder();
    	if(yearDiff>0){
    		sb.append(yearDiff+"年");
    	}
    	if(monthDiff>0){
    		sb.append(monthDiff+"月");
    	}
    	if(dateDiff>0){
    		sb.append(dateDiff+"天");
    	}
    	
    	return sb.toString();
    }
    
    /**
     * 把输入的秒数转换成：hh:mm:ss 的字符串
     * @param secondNum
     * @return
     */
    public static String formatSecond(long secondNum){
    	long hours = secondNum/(3600);
    	secondNum = secondNum%(3600);
    	long minutes = secondNum/60;
    	long seconds = secondNum%60;
        String sb = (hours < 10 ? "0" + hours + ":" : hours + ":") +
                (minutes < 10 ? "0" + minutes + ":" : minutes + ":") +
                (seconds < 10 ? "0" + seconds : String.valueOf(seconds));
    	return sb;
    }
    
    /**
     * 根据输入日期，获取该日期所在周的周一日期
     * @param date
     * @return
     */
    public static Date getFirstDayOfWeekByDate(Date date){
    	Calendar cal = Calendar.getInstance();
    	cal.setTime(date);
    	while(cal.get(Calendar.DAY_OF_WEEK)!=2){
    		cal.add(Calendar.DATE, -1);
    	}
    	cal.set(Calendar.HOUR_OF_DAY, 0);
    	cal.set(Calendar.MINUTE, 0);
    	cal.set(Calendar.SECOND, 0);
    	cal.set(Calendar.MILLISECOND, 0);
    	return cal.getTime();
    }
    /**
	 * 获取当前小时数
	 * @return
	 */
    public static Integer getCurrentHour(){
		Calendar c = Calendar.getInstance();
		return c.get(Calendar.HOUR_OF_DAY);
	}
    
    /**
     * 获取指定年月第一天的日期
     * @param year
     * @param month
     */
	public static Date getFirstDateOfMonth(int year, int month) {
		Calendar cal = Calendar.getInstance();
		cal.set(year, month-1, 1, 0, 0, 0);
		return cal.getTime();
	}
	
	/**
	 * 获取指定年月最后一天的日期
	 * @param year
	 * @param month 1-12
	 */
	public static Date getLastDateOfMonth(int year, int month) {
		Calendar cal = Calendar.getInstance();
		cal.set(year, month, 1, 0, 0, 0);
		cal.add(Calendar.DAY_OF_MONTH, -1);
		return cal.getTime();
	}
	
	/**
	 * 获取日期当周周日的日期
	 * @param date
	 * @return
	 */
	public static Date getLastDayOfWeekByDate(Date date) {
		Calendar cal = Calendar.getInstance();
    	cal.setTime(date);
    	while(cal.get(Calendar.DAY_OF_WEEK)!=Calendar.SUNDAY){
    		cal.add(Calendar.DATE, 1);
    	}
    	cal.set(Calendar.HOUR_OF_DAY, 0);
    	cal.set(Calendar.MINUTE, 0);
    	cal.set(Calendar.SECOND, 0);
    	cal.set(Calendar.MILLISECOND, 0);
    	return cal.getTime();
	}
	
	/**
	 * 获取今天剩余时间（秒）
	 * @return
	 */
	public static int getTodayRemainingTime(){
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return (int)((calendar.getTimeInMillis()- System.currentTimeMillis()) / 1000);
	}
	
	/**
	 * 获取今天0点时间戳
	 * @return
	 */
	public static long getToday0HoursTime(){
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTimeInMillis();
	}

    public static Date getYesterday() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return calendar.getTime();
    }
    /**
     * 获取下月的第一天
     * @return
     */
    public static String getPrevMonthFirstDay(){
        //获取前月的第一天
        Calendar   cal=Calendar.getInstance();//获取当前日期
        cal.add(Calendar.MONTH, -1);
        cal.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
        Date firstDay = cal.getTime();
        return dateToString(firstDay, "yyyy-MM-dd");
    }

    /**
     * 获取当月的第一天
     * @return
     */
    public static String getMonthFirstDay() {
        //获取前月的第一天
        Calendar cal = Calendar.getInstance();//获取当前日期
        cal.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
        Date firstDay = cal.getTime();
        return dateToString(firstDay, "yyyy-MM-dd");
    }
    /**
     * 获取下月的第一天
     * @return
     */
    public static String getNextMonthFirstDay(){
        //获取下月的第一天
        Calendar cal=Calendar.getInstance();//获取当前日期
        cal.add(Calendar.MONTH, +1);
        cal.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
        Date firstDay = cal.getTime();
        return dateToString(firstDay, "yyyy-MM-dd");
    }
    /**
     * 前后N月
     * @param date
     * @param n
     * @return
     */
    public static Date addMonth(Date date,int n){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, n);
        return cal.getTime();
    }
    /**
     * 前后N天
     *
     * @param date
     * @param n
     * @return
     */
    public static Date addDay(Date date, int n) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, n);
        return cal.getTime();
    }
    /**
     * 按照分钟切割时间区间
     *
     * @param startTime
     * @param endTime
     * @param intervalMinutes
     * @return
     */
    public static List<DateSplit> splitByMinute(Date startTime, Date endTime, int intervalMinutes) {
        if (endTime.getTime() <= startTime.getTime()) {
            return null;
        }
        List<DateSplit> dateSplits = new ArrayList<>();

        DateSplit param = new DateSplit();
        param.setStartDateTime(startTime);
        param.setEndDateTime(endTime);
        param.setEndDateTime(addMinute(startTime, intervalMinutes));
        while (true) {
            param.setStartDateTime(startTime);
            Date tempEndTime = addMinute(startTime, intervalMinutes);
            if (tempEndTime.getTime() >= endTime.getTime()) {
                tempEndTime = endTime;
            }
            param.setEndDateTime(tempEndTime);

            dateSplits.add(new DateSplit(param.getStartDateTime(), param.getEndDateTime()));

            startTime = addMinute(startTime, intervalMinutes);
            if (startTime.getTime() >= endTime.getTime()) {
                break;
            }
            if (param.getEndDateTime().getTime() >= endTime.getTime()) {
                break;
            }
        }
        return dateSplits;
    }
    public static Date addMinute(Date date, int minute) {
        return add(date, Calendar.MINUTE, minute);
    }

    public static Date add(final Date date, final int calendarField, final int amount) {
        final Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }
    /**
     * 获取该月份天数
     *
     * @param year
     * @param month
     * @return
     */
    public static int getTotalDays(int year, int month) {
        GregorianCalendar cal = new GregorianCalendar(year, month - 1, 1);
        return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 判断target时间是否在范围内，在范围内返回true,否则false
     * @param target
     * @param start
     * @param end
     * @return
     */
    public static boolean isWithinRange(Date target, Date start, Date end) {
        if (target == null || start == null || end == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        return target.compareTo(start) >= 0 && target.compareTo(end) <= 0;
    }

}
