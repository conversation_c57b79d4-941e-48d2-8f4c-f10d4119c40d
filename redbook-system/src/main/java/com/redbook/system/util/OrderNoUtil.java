package com.redbook.system.util;


import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

public class OrderNoUtil {

    /**
     * 获取订单号
     *  prefix 订单号前缀
     * @return
     */
    public static String genOrderNo(String prefix) {
        // 获取当前时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String currentTime = dateFormat.format(new Date());

        // 生成随机数
        Random random = new Random();
        int randomNum = random.nextInt(99999999);
        String randomStr = String.format("%08d", randomNum);

        // 拼接生成的14位时间和8位随机数
        String result = prefix + currentTime + randomStr;
        return result;
    }

    /**
     * 订单号
     * @return
     */
    public static String getOrderNo() {

        return genOrderNo("PJ");
    }

    /**
     * 退款订单号
     * @return
     */
    public static String getRefundOrderNo() {
        return genOrderNo("PJRFD");
    }

    /**
     * 通过寄修单号转换为寄修退款订单号
     * @param repairOrderNo
     * @return
     */
    public static String changeRepairRefundOrderNo(String repairOrderNo) {
        String replace = repairOrderNo.replace("JX", "JXRFD");
        // 生成3位随机数
        Random random = new Random();
        int randomNum = random.nextInt(999);
        String randomStr = String.format("%08d", randomNum);
        return replace + randomStr;
    }

    public static void main(String[] args) {
        System.out.println(changeRepairRefundOrderNo("JX2025042300009"));
    }

}
