package com.redbook.system.util;

import com.redbook.system.enums.PushActionEnum;
import org.apache.commons.lang3.StringUtils;

public class JpushMessageUtil {
    /**
     * 获取通知标题
     * @param pushActionEnum
     * @return
     */
    public static String getTile(PushActionEnum pushActionEnum) {
        String title = "";
        switch (pushActionEnum) {
            case OFFLINE_INTEGRAL_UPDATE:
                title = "线下金币";
                break;
            case STUDY_AGAIN:
                title = "再学一遍";
                break;
            case CREATE_YLB_PK:
                title = "pk提醒";
                break;
            case TEACHER_OPERATE_ORDER:
                title = "订单";
                break;
            case TEACHER_MODIFY_SYNC_COURSE:
                title = "同步课程变动";
                break;
            case TEACHER_MODIFY_TASK_COURSE:
                title = "任务课程通知";
                break;
            case TEACHER_MODIFY_TASK_COURSE_ISMUST:
                title = "任务课程通知";
                break;
            case TEACHER_MODIFY_MUST_MODULE:
                title = "必学模块通知";
                break;
            case TEACHER_MODIFY_MUST_MODULE_ISMUST:
                title = "必学模块通知";
                break;
            case TEACHER_MODIFY_HIGH_PERFORMANCE_MODE_SETTING:
                title = "闯关模式通知";
                break;
            case TEACHER_MODIFY_REVIEW_SETTING:
                title = "复习设置通知";
                break;
            case TEACHER_MODIFY_LOGIN_PASSWORD:
                title = "重置登录密码通知";
                break;
            case TEACHER_MODIFY_PAY_PASSWORD:
                title = "重置支付密码通知";
                break;
            case TEACHER_MODIFY_WORD_INTENSIFY_FLAG_SETTING:
                title = "词义强化通知";
                break;
            case TEACHER_MODIFY_WORD_STUDY_MODE_SETTING:
                title = "学习模式通知";
                break;
            case TEACHER_MODIFY_USER_FUNCTION_SETTING:
                title = "辅助功能开关设置通知";
                break;
            case TEACHER_MODIFY_LETTER_STUDY_VERSION_SETTING:
                title = "认字母版本通知";
                break;
            case COURSE_REFRESH:
                title = "课程刷新通知";
                break;
            case TEACHER_MODIFY_USER_CONFIG:
                title = "个性化设置修改";
                break;
            case PRIVATE_MESSAGE:
                title = "私信";
                break;
            case PRIVATE_MESSAGE_REVOKE:
                title = "私信撤回";
                break;
            default:
                break;
        }
        return title;
    }

    public static String getMsg(PushActionEnum pushActionEnum, String addMsg, Integer type) {
        String msg = "";
        switch (pushActionEnum) {
            case OFFLINE_INTEGRAL_UPDATE:
                msg = "原因：" + addMsg;
                break;
            case STUDY_AGAIN:
                if (1 == type) {
                    msg = "为了巩固学习，老师对你的" + addMsg + "模块进行了再学一遍操作";
                } else {
                    msg = "为了巩固学习，老师对你的" + addMsg + "模块进行了再学一遍操作";
                }
                break;
            case CREATE_YLB_PK:
                msg = addMsg + "对你发起了pk。";
                break;
            case TEACHER_OPERATE_ORDER:

                if (1 == type) {
                    msg = "老师已兑换你的订单：" + addMsg;
                } else {
                    msg = "老师已取消你的订单：" + addMsg;
                }
                break;
            case TEACHER_MODIFY_SYNC_COURSE:
                msg = "你的同步课程已由老师修改为：" + addMsg;
                break;
            case TEACHER_MODIFY_TASK_COURSE:
                if(StringUtils.isNotEmpty(addMsg)){
                    if (1 == type) {
                        msg = "老师给你添加了新的任务课程：" + addMsg;
                    } else {
                        msg = "老师给你移除了任务课程：" + addMsg;
                    }
                }else {
                    if (1 == type) {
                        msg = "老师给你添加了新的任务课程";
                    } else {
                        msg = "老师给你移除了任务课程";
                    }
                }

                break;
            case TEACHER_MODIFY_TASK_COURSE_ISMUST:
                if (1 == type) {
                    msg = "老师要求你只能学习同步课程和任务课程，其他课程将暂时上锁不可学习。";
                } else {
                    msg = "现在你可以学习所有课程。";
                }
                break;
            case TEACHER_MODIFY_MUST_MODULE:
                msg = "老师帮你修改了必学模块。";
                break;
            case TEACHER_MODIFY_MUST_MODULE_ISMUST:
                if (1 == type) {
                    msg = "老师要求你学习必学模块，选学模块将暂时上锁不可学习。";
                } else {
                    msg = "现在你可以学习所有模块。";
                }
                break;
            case TEACHER_MODIFY_HIGH_PERFORMANCE_MODE_SETTING:
                if (1 == type) {
                    msg = "老师已为你设置了高效闯关模式，学习更高效。";
                } else {
                    msg = "老师关闭了高效闯关模式，学习更稳健。";
                }
                break;
            case TEACHER_MODIFY_WORD_INTENSIFY_FLAG_SETTING:
                if (1 == type) {
                    msg = "老师已为你开启了词义强化，学习更稳健。";
                } else {
                    msg = "老师关闭了词义强化，学习更高效。";
                }
                break;
            case TEACHER_MODIFY_WORD_STUDY_MODE_SETTING:
                if (1 == type) {
                    msg = "老师已为你修改为经典模式";
                } else {
                    msg = "老师已为你修改为四选一模式";
                }
                break;
            case TEACHER_MODIFY_REVIEW_SETTING:
                msg = "老师帮你修改了复习设置。";
                break;
            case TEACHER_MODIFY_LOGIN_PASSWORD:
                msg = "老师帮你重置了登录密码。";
                break;
            case TEACHER_MODIFY_PAY_PASSWORD:
                msg = "老师帮你重置了支付密码。";
                break;
            case TEACHER_MODIFY_USER_FUNCTION_SETTING:
                msg = "老师帮你修改了辅助功能开关设置。";
                break;
            case COURSE_REFRESH:
                msg = "老师已为你刷新了课程进度。";
                break;
            case TEACHER_MODIFY_USER_CONFIG:
                msg = "老师为你修改了个性化设置，详细情况请咨询老师。";
                break;
            case TEACHER_MODIFY_LETTER_STUDY_VERSION_SETTING:
                msg = "老师帮你修改了认字母学习版本。";
                break;
            default:
                break;
        }
        return msg;
    }
}
