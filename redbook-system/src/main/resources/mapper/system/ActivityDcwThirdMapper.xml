<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ActivityDcwThirdMapper">

    <sql id="selectActivityUserVo1">
        select  au.user_id as userId,u1.user_name as userName,au.user_addr userAddr,au.`stage`,au.game_stage1_sum_score as score,au.game_stage1_max_score as maxScore,game_stage1_sum_score_update_time as scoreTime,
        CASE au.stage
        WHEN 1 THEN
        '小学'
        WHEN 2 THEN
        '初中'
        WHEN 11 THEN
        '小升初'
        WHEN 21 THEN
        '初升高'
        END AS stageDesc,'城市热度争夺赛' as gameStageDesc
        from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user au
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectGameStage1List" parameterType="activityDcwThirdIndexListDto" resultType="com.redbook.system.domain.ActivityDcwThirdUser">
        <include refid="selectActivityUserVo1"/>
        <where>
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="stage != null ">and au.stage = #{stage}</if>
            <if test="searchKey != null and searchKey != ''">
                and (au.user_id= #{searchKey} or u1.user_name like CONCAT('%',#{searchKey},'%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by au.game_stage1_sum_score desc,au.game_stage1_sum_score_update_time asc
    </select>
    <sql id="selectActivityUserVo2">
        select au.user_id as userId,u1.user_name as userName,au.user_addr userAddr,au.`stage`,au.game_stage2_sum_score as score,au.game_stage2_max_score as maxScore,game_stage2_sum_score_update_time as scoreTime,
        CASE au.stage
        WHEN 1 THEN
        '小学'
        WHEN 2 THEN
        '初中'
        WHEN 11 THEN
        '小升初'
        WHEN 21 THEN
        '初升高'
        END AS stageDesc,'城市举办权争夺赛' as gameStageDesc
        from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user au
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectGameStage2List" parameterType="activityDcwThirdIndexListDto" resultType="com.redbook.system.domain.ActivityDcwThirdUser">
        <include refid="selectActivityUserVo2"/>
        <where>
            u1.member_type in (1,2,3)
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="stage != null ">and au.stage = #{stage}</if>
            <if test="searchKey != null and searchKey != ''">
                and (au.user_id= #{searchKey} or u1.user_name like CONCAT('%',#{searchKey},'%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by game_stage2_sum_score desc,game_stage2_sum_score_update_time asc
    </select>
    <sql id="selectActivityUserVo3">
        select au.user_id as userId,u1.user_name as userName,au.user_addr userAddr,au.`stage`,au.game_stage3_max_score as maxScore,au.city_id as cityId,game_stage3_max_score_time as scoreTime,
        CASE au.stage
        WHEN 1 THEN
        '小学'
        WHEN 2 THEN
        '初中'
        WHEN 11 THEN
        '小升初'
        WHEN 21 THEN
        '初升高'
        END AS stageDesc,'城市英雄选拔赛' as gameStageDesc
        from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user au
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectGameStage3List" parameterType="activityDcwThirdIndexListDto" resultType="com.redbook.system.domain.ActivityDcwThirdUser">
        <include refid="selectActivityUserVo3"/>
        <where>
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="stage != null ">and au.stage = #{stage}</if>
            <if test="searchKey != null and searchKey != ''">
                and (au.user_id= #{searchKey} or u1.user_name like CONCAT('%',#{searchKey},'%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by game_stage3_max_score desc,game_stage3_max_score_time asc
    </select>
    <sql id="selectActivityUserVo5">
        select au.user_id as userId,u1.user_name as userName,au.user_addr userAddr,au.`stage`,au.final_max_score as score,au.final_max_score_efficiency as efficiency,au.rank_num as `rank`,
        CASE au.stage
        WHEN 1 THEN
        '小学'
        WHEN 2 THEN
        '初中'
        WHEN 11 THEN
        '小升初'
        WHEN 21 THEN
        '初升高'
        END AS stageDesc,'总决赛' as gameStageDesc
        from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion au
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectGameStage5List" parameterType="activityDcwThirdIndexListDto" resultType="com.redbook.system.domain.ActivityDcwThirdUser">
        <include refid="selectActivityUserVo5"/>
        <where>
            `is_enter_8`=1
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="stage != null ">and au.stage = #{stage}</if>
            <if test="searchKey != null and searchKey != ''">
                and (au.user_id= #{searchKey} or u1.user_name like CONCAT('%',#{searchKey},'%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by au.rank_num asc
    </select>

    <select id="selectGameStage1TopNumList" resultType="com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank">
 SELECT
	@rankNum := @rankNum + 1 AS rankNum,
	obj.*
    FROM(select user_id as userId
      from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user where activity_base_id=#{activityBaseId}  and game_stage1_sum_score>0
      order by game_stage1_sum_score desc,game_stage1_sum_score_update_time asc,id asc
      limit #{limitNum}) AS obj,
	( SELECT @rankNum := 0 ) r
    </select>
    <select id="selectGameStage2TopNumList" resultType="com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank">
    SELECT
	@rankNum := @rankNum + 1 AS rankNum,
	obj.*
    FROM(select user_id as userId
      from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user where activity_base_id=#{activityBaseId}  and game_stage2_sum_score>0
      order by game_stage2_sum_score desc,game_stage2_sum_score_update_time asc,id asc
      limit #{limitNum}) AS obj,
	( SELECT @rankNum := 0 ) r
    </select>
    <select id="selectGameStage3TopNumList" resultType="com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank">
SELECT
	@rankNum := @rankNum + 1 AS rankNum,
	obj.*
    FROM(select user_id as userId
      from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user where activity_base_id=#{activityBaseId} and city_id=#{cityId}  and game_stage3_max_score>0
      order by game_stage3_max_score desc,game_stage3_max_score_time asc,id asc
      limit #{limitNum}) AS obj,
	( SELECT @rankNum := 0 ) r
    </select>
    <select id="selectGameStage4Mode100TopList" resultType="com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank">
SELECT
	@rankNum := @rankNum + 1 AS rankNum,
	obj.*
    FROM( select user_id as userId,hero_challenge_max_score as score
 from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion
 where activity_base_id=#{activityBaseId} and stage=#{stage}
    order by hero_challenge_max_score desc,hero_challenge_max_score_efficiency asc,hero_challenge_max_score_time asc,id asc limit #{limitNum}) AS obj,
	( SELECT @rankNum := 0 ) r
    </select>
    <select id="selectGameStage4Mode32TopList" resultType="com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank">
SELECT
	@rankNum := @rankNum + 1 AS rankNum,
	obj.*
    FROM( select user_id as userId,final_100_to_32_max_score as score
 from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion
 where activity_base_id=#{activityBaseId} and stage=#{stage} and is_enter_100=1
    order by final_100_to_32_max_score desc,final_100_to_32_max_score_efficiency asc,final_100_to_32_max_score_time asc,id asc limit #{limitNum}) AS obj,
	( SELECT @rankNum := 0 ) r
    </select>
    <select id="selectGameStage4Mode16TopList" resultType="com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank">
 SELECT
	@rankNum := @rankNum + 1 AS rankNum,
	obj.*
    FROM( select user_id as userId,final_32_to_16_max_score as score
 from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion
 where activity_base_id=#{activityBaseId} and stage=#{stage} and is_enter_32=1
    order by final_32_to_16_max_score desc,final_32_to_16_max_score_efficiency asc,final_32_to_16_max_score_time asc,id asc limit #{limitNum}) AS obj,
	( SELECT @rankNum := 0 ) r
    </select>
    <select id="selectGameStage4Mode8TopList" resultType="com.redbook.system.domain.vo.ActivityDcwThirdPersonalRank">
 SELECT
	@rankNum := @rankNum + 1 AS rankNum,
	obj.*
    FROM( select user_id as userId,final_16_to_8_max_score as score
 from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion
 where activity_base_id=#{activityBaseId} and stage=#{stage} and is_enter_16=1
    order by final_16_to_8_max_score desc,final_16_to_8_max_score_efficiency asc,final_16_to_8_max_score_time asc,id asc limit #{limitNum}) AS obj,
	( SELECT @rankNum := 0 ) r
    </select>
    <sql id="selectActivityStage4ModelVo100">
        select au.user_id as userId,u1.user_name as userName,au.user_addr userAddr,au.`stage`,au.hero_challenge_max_score as score,au.is_enter_100 as isEnter100,hero_challenge_max_score_time as scoreTime,
        CASE au.stage
        WHEN 1 THEN
        '小学组'
        WHEN 2 THEN
        '初中组'
        END AS stageDesc,'全国100强' as gameStageDesc
        from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion au
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectGameStage4Mode100List" parameterType="activityDcwThirdIndexListDto" resultType="com.redbook.system.domain.ActivityDcwThirdUser">
        <include refid="selectActivityStage4ModelVo100"/>
        <where>
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="stage != null ">and au.stage = #{stage}</if>
            <if test="searchKey != null and searchKey != ''">
                and (au.user_id= #{searchKey} or u1.user_name like CONCAT('%',#{searchKey},'%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by hero_challenge_max_score desc,hero_challenge_max_score_time asc
    </select>
    <sql id="selectActivityStage4ModelVo32">
        select  au.user_id as userId,u1.user_name as userName,au.user_addr userAddr,au.`stage`,au.final_100_to_32_max_score as score,au.is_enter_32 as isEnter32,final_100_to_32_max_score_time as scoreTime,
        CASE au.stage
        WHEN 1 THEN
        '小学组'
        WHEN 2 THEN
        '初中组' END AS stageDesc,'全国32强' as gameStageDesc
        from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion au
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectGameStage4Mode32List" parameterType="activityDcwThirdIndexListDto" resultType="com.redbook.system.domain.ActivityDcwThirdUser">
        <include refid="selectActivityStage4ModelVo32"/>
        <where>
            is_enter_100=1
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="stage != null ">and au.stage = #{stage}</if>
            <if test="searchKey != null and searchKey != ''">
                and (au.user_id= #{searchKey} or u1.user_name like CONCAT('%',#{searchKey},'%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by final_100_to_32_max_score desc,final_100_to_32_max_score_time asc
    </select>
    <sql id="selectActivityStage4ModelVo16">
        select  au.user_id as userId,u1.user_name as userName,au.user_addr userAddr,au.`stage`,au.final_32_to_16_max_score as score,au.is_enter_16 as isEnter16,final_32_to_16_max_score_time as scoreTime,
        CASE au.stage
        WHEN 1 THEN
        '小学组'
        WHEN 2 THEN
        '初中组' END AS stageDesc,'全国16强' as gameStageDesc
        from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion au
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectGameStage4Mode16List" parameterType="activityDcwThirdIndexListDto" resultType="com.redbook.system.domain.ActivityDcwThirdUser">
        <include refid="selectActivityStage4ModelVo16"/>
        <where>
            is_enter_32=1
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="stage != null ">and au.stage = #{stage}</if>
            <if test="searchKey != null and searchKey != ''">
                and (au.user_id= #{searchKey} or u1.user_name like CONCAT('%',#{searchKey},'%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by final_32_to_16_max_score desc,final_32_to_16_max_score_time asc
    </select>
    <sql id="selectActivityStage4ModelVo8">
        select au.user_id as userId,u1.user_name as userName,au.user_addr userAddr,au.`stage`,au.final_16_to_8_max_score as score,au.is_enter_8 as isEnter8,final_16_to_8_max_score_time as scoreTime,
        CASE au.stage
        WHEN 1 THEN
        '小学组'
        WHEN 2 THEN
        '初中组' END AS stageDesc,'全国8强' as gameStageDesc
        from ${DB_RED_BOOK_ACTIVITY}.activity_dcw_third_user_promotion au
        left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=au.user_id
        left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        left join sys_user u2 ON a.contact_person = u2.user_id
        left join exclusive_shop es on u1.exclusive_shop_id=es.id
    </sql>
    <select id="selectGameStage4Mode8List" parameterType="activityDcwThirdIndexListDto" resultType="com.redbook.system.domain.ActivityDcwThirdUser">
        <include refid="selectActivityStage4ModelVo8"/>
        <where>
            is_enter_16=1
            <if test="activityBaseId != null ">and au.activity_base_id = #{activityBaseId}</if>
            <if test="stage != null ">and au.stage = #{stage}</if>
            <if test="searchKey != null and searchKey != ''">
                and (au.user_id= #{searchKey} or u1.user_name like CONCAT('%',#{searchKey},'%'))
            </if>
            <if test="agentId != null "> and a.id = #{agentId}</if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
        order by final_16_to_8_max_score desc,final_16_to_8_max_score_time asc
    </select>
</mapper>