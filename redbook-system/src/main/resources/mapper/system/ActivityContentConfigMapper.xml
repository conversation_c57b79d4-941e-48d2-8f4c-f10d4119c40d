<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ActivityContentConfigMapper">
    <select id="selectConfigByTypeAndKey" resultType="java.lang.String">
        select acc_value
        from ${DB_RED_BOOK_ACTIVITY}.activity_content_config
        where activity_content_id = #{activityContentId} and acc_type=#{accType} and acc_key=#{key} and acc_status=1
    </select>
    <select id="selectByType" resultType="java.lang.String">
        select acc_value
        from ${DB_RED_BOOK_ACTIVITY}.activity_content_config
        where activity_content_id = #{activityContentId}  and acc_status=1 and acc_type=#{type} order by id asc
    </select>
</mapper>