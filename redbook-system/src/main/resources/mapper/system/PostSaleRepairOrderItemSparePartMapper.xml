<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemSparePartMapper">

    <resultMap type="PostSaleRepairOrderItemSparePart" id="PostSaleRepairOrderItemSparePartResult">
        <result property="id" column="id"/>
        <result property="itemId" column="item_id"/>
        <result property="sparePartId" column="spare_part_id"/>
        <result property="useNum" column="use_num"/>
        <result property="sparePartCost" column="spare_part_cost"/>
        <result property="sparePartSalePrice" column="spare_part_sale_price"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="sparePartName" column="spare_part_name"/>
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemSparePartVo">
        select id,
               item_id,
               spare_part_id,
               use_num,
               spare_part_cost,
               spare_part_sale_price,
               create_time,
               create_by
        from post_sale_repair_order_item_spare_part
    </sql>
    <delete id="deleteByItemIdAndSparePartIds">
        delete from post_sale_repair_order_item_spare_part
        where item_id=#{itemId}
        and spare_part_id in
        <foreach item="sparePartId" collection="list" open="(" separator="," close=")">
            #{sparePartId}
        </foreach>
    </delete>

    <select id="selectPostSaleRepairOrderItemSparePartByItemIds" resultMap="PostSaleRepairOrderItemSparePartResult">
        select psroisp.*,pssp.spare_part_name as spare_part_name
        from post_sale_repair_order_item_spare_part psroisp
        left join  post_sale_spare_part pssp  on psroisp.spare_part_id=pssp.id
        where item_id in
        <foreach item="itemId" collection="list" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
    <select id="selectPostSaleRepairOrderItemSparePartByItemId" resultMap="PostSaleRepairOrderItemSparePartResult">
        select psroisp.*,pssp.spare_part_name as spare_part_name
        from post_sale_repair_order_item_spare_part psroisp
        left join  post_sale_spare_part pssp  on psroisp.spare_part_id=pssp.id
        where item_id =#{itemId}
    </select>

    <select id="selectByItemId" resultMap="PostSaleRepairOrderItemSparePartResult">
        select psroisp.*,pssp.spare_part_name as spare_part_name
        from post_sale_repair_order_item_spare_part psroisp
        left join  post_sale_spare_part pssp  on psroisp.spare_part_id=pssp.id
        where item_id =  #{itemId}
    </select>


    <insert id="insertBatch" parameterType="java.util.List">
        insert into post_sale_repair_order_item_spare_part
        (item_id, spare_part_id, use_num, spare_part_cost, spare_part_sale_price, create_by)
        values
        <foreach collection="list" item="sparePart" separator=",">
            (#{sparePart.itemId}, #{sparePart.sparePartId}, #{sparePart.useNum}, #{sparePart.sparePartCost},
            #{sparePart.sparePartSalePrice}, #{sparePart.createBy})
        </foreach>
    </insert>
    <update id="updatePostSaleRepairOrderItemSparePart" parameterType="PostSaleRepairOrderItemSparePart">
        update post_sale_repair_order_item_spare_part
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="sparePartId != null">spare_part_id = #{sparePartId},</if>
            <if test="useNum != null">use_num = #{useNum},</if>
            <if test="sparePartCost != null">spare_part_cost = #{sparePartCost},</if>
            <if test="sparePartSalePrice != null">spare_part_sale_price = #{sparePartSalePrice},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>