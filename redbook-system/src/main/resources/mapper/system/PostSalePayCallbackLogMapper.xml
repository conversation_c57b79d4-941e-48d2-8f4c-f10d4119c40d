<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSalePayCallbackLogMapper">
    
    <resultMap type="PostSalePayCallbackLog" id="PostSalePayCallbackLogResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="notifyContent"    column="notify_content"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectPostSalePayCallbackLogVo">
        select id, order_no, transaction_id, notify_content, status, create_time from post_sale_pay_callback_log
    </sql>

    <select id="selectPostSalePayCallbackLogList" parameterType="PostSalePayCallbackLog" resultMap="PostSalePayCallbackLogResult">
        <include refid="selectPostSalePayCallbackLogVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="transactionId != null  and transactionId != ''"> and transaction_id = #{transactionId}</if>
            <if test="notifyContent != null  and notifyContent != ''"> and notify_content = #{notifyContent}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectPostSalePayCallbackLogById" parameterType="Long" resultMap="PostSalePayCallbackLogResult">
        <include refid="selectPostSalePayCallbackLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSalePayCallbackLog" parameterType="PostSalePayCallbackLog" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_pay_callback_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="notifyContent != null">notify_content,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="notifyContent != null">#{notifyContent},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updatePostSalePayCallbackLog" parameterType="PostSalePayCallbackLog">
        update post_sale_pay_callback_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="notifyContent != null">notify_content = #{notifyContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSalePayCallbackLogById" parameterType="Long">
        delete from post_sale_pay_callback_log where id = #{id}
    </delete>

    <delete id="deletePostSalePayCallbackLogByIds" parameterType="String">
        delete from post_sale_pay_callback_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>