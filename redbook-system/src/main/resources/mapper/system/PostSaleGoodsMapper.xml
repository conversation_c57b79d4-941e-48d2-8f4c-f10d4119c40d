<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleGoodsMapper">
    
    <resultMap type="PostSaleGoods" id="PostSaleGoodsResult">
        <result property="id"    column="id"    />
        <result property="goodName"    column="good_name"    />
        <result property="goodCode"    column="good_code"    />
        <result property="goodClassifyId"    column="good_classify_id"    />
        <result property="goodModelId"    column="good_model_id"    />
        <result property="goodDesc"    column="good_desc"    />
        <result property="goodImgs"    column="good_imgs"    />
        <result property="isShow"    column="is_show"    />
        <result property="goodStoreNum"    column="good_store_num"    />
        <result property="remark"    column="remark"    />
        <result property="goodCost"    column="good_cost"    />
        <result property="goodTotalCost"    column="good_total_cost"    />
        <result property="goodSalePrice"    column="good_sale_price"    />
        <result property="createBy"    column="create_by"    />
        <result property="goodModelName"    column="good_model_name"    />
        <result property="goodModelCode"    column="good_model_code"    />
        <result property="goodClassifyName"    column="good_classify_name"    />
    </resultMap>

    <sql id="selectPostSaleGoodsVo">
        select id, good_name, good_code, good_classify_id, good_model_id, good_desc, good_imgs, is_show, good_store_num, remark, good_cost, good_total_cost, good_sale_price from post_sale_goods
    </sql>

    <select id="selectPostSaleGoodsList" parameterType="PostSaleGoods" resultMap="PostSaleGoodsResult">
        <include refid="selectPostSaleGoodsVo"/>
        <where>  
            <if test="goodName != null  and goodName != ''"> and good_name like concat('%', #{goodName}, '%')</if>
            <if test="goodCode != null  and goodCode != ''">  and good_code like concat('%', #{goodCode}, '%')</if>
            <if test="goodClassifyId != null "> and good_classify_id = #{goodClassifyId}</if>
            <if test="goodModelId != null "> and good_model_id = #{goodModelId}</if>
            <if test="goodDesc != null  and goodDesc != ''"> and good_desc like concat('%', #{goodDesc}, '%')</if>
            <if test="goodImgs != null  and goodImgs != ''"> and good_imgs = #{goodImgs}</if>
            <if test="isShow != null "> and is_show = #{isShow}</if>
            <if test="goodStoreNum != null "> and good_store_num = #{goodStoreNum}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
            <if test="goodCost != null "> and good_cost = #{goodCost}</if>
            <if test="goodTotalCost != null "> and good_total_cost = #{goodTotalCost}</if>
            <if test="goodSalePrice != null "> and good_sale_price = #{goodSalePrice}</if>
        </where>
    </select>

    <select id="selectPostSaleGoodsById" parameterType="Long" resultMap="PostSaleGoodsResult">
        select good.id, good.good_name, good.good_code, good.good_classify_id, good.good_model_id, good.good_desc, good.good_imgs, good.is_show, good.good_store_num, good.remark, good.good_cost, good.good_total_cost, good.good_sale_price,
               model.name as good_model_name,model.code as good_model_code,classify.classify_name as good_classify_name
        from post_sale_goods good
         left join redbook_tablet_model model on good.good_model_id = model.id
         left join post_sale_goods_classify classify on classify.id = good.good_classify_id
        where good.id = #{id}
    </select>


    <insert id="insertPostSaleGoods" parameterType="PostSaleGoods" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="goodName != null">good_name,</if>
            <if test="goodCode != null">good_code,</if>
            <if test="goodClassifyId != null">good_classify_id,</if>
            <if test="goodModelId != null">good_model_id,</if>
            <if test="goodDesc != null">good_desc,</if>
            <if test="goodImgs != null">good_imgs,</if>
            <if test="isShow != null">is_show,</if>
            <if test="goodStoreNum != null">good_store_num,</if>
            <if test="remark != null">remark,</if>
            <if test="goodCost != null">good_cost,</if>
            <if test="goodTotalCost != null">good_total_cost,</if>
            <if test="goodSalePrice != null">good_sale_price,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateUserId != null">update_user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="goodName != null">#{goodName},</if>
            <if test="goodCode != null">#{goodCode},</if>
            <if test="goodClassifyId != null">#{goodClassifyId},</if>
            <if test="goodModelId != null">#{goodModelId},</if>
            <if test="goodDesc != null">#{goodDesc},</if>
            <if test="goodImgs != null">#{goodImgs},</if>
            <if test="isShow != null">#{isShow},</if>
            <if test="goodStoreNum != null">#{goodStoreNum},</if>
            <if test="remark != null">#{remark},</if>
            <if test="goodCost != null">#{goodCost},</if>
            <if test="goodTotalCost != null">#{goodTotalCost},</if>
            <if test="goodSalePrice != null">#{goodSalePrice},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateUserId != null">#{updateUserId},</if>
         </trim>
    </insert>

    <update id="updatePostSaleGoods" parameterType="PostSaleGoods">
        update post_sale_goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodName != null">good_name = #{goodName},</if>
            <if test="goodCode != null">good_code = #{goodCode},</if>
            <if test="goodClassifyId != null">good_classify_id = #{goodClassifyId},</if>
            <if test="goodModelId != null">good_model_id = #{goodModelId},</if>
            <if test="goodDesc != null">good_desc = #{goodDesc},</if>
            <if test="goodImgs != null">good_imgs = #{goodImgs},</if>
            <if test="isShow != null">is_show = #{isShow},</if>
            <if test="goodStoreNum != null">good_store_num = #{goodStoreNum},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="goodCost != null">good_cost = #{goodCost},</if>
            <if test="goodTotalCost != null">good_total_cost = #{goodTotalCost},</if>
            <if test="goodSalePrice != null">good_sale_price = #{goodSalePrice},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUserId != null">update_user_id = #{updateUserId},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deletePostSaleGoodsById" parameterType="Long">
        delete from post_sale_goods where id = #{id}
    </delete>

    <delete id="deletePostSaleGoodsByIds" parameterType="String">
        delete from post_sale_goods where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>