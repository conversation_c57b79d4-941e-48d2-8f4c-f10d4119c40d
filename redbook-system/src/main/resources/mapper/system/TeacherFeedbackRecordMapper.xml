<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.TeacherFeedbackRecordMapper">
    
    <resultMap type="TeacherFeedbackRecord" id="TeacherFeedbackRecordResult">
        <result property="id"    column="id"    />
        <result property="feedbackTeacherId"    column="feedback_teacher_id"    />
        <result property="feedbackTitle"    column="feedback_title"    />
        <result property="feedbackType"    column="feedback_type"    />
        <result property="feedbackText"    column="feedback_text"    />
        <result property="createTime"    column="create_time"    />
        <result property="replyUserId"    column="reply_user_id"    />
        <result property="replyTime"    column="reply_time"    />
        <result property="replyText"    column="reply_text"    />
        <result property="replyUserName"    column="replyUserName"    />
        <result property="feedbackTeacherName"    column="feedbackTeacherName"    />
    </resultMap>

    <sql id="selectTeacherFeedbackRecordVo">
        select tfr.id, tfr.feedback_title,tfr.feedback_teacher_id, tfr.feedback_type, tfr.feedback_text, tfr.create_time,
               tfr.reply_user_id, tfr.reply_time, tfr.reply_text,su.nick_name as replyUserName,au.user_name as feedbackTeacherName
        from ${DB_RED_BOOK_TEACH}.teacher_feedback_record tfr
        left join agent_user au on tfr.feedback_teacher_id = au.user_id
        left join sys_user su on tfr.reply_user_id = su.user_name
    </sql>

    <select id="selectTeacherFeedbackRecordList" parameterType="TeacherFeedbackRecord" resultMap="TeacherFeedbackRecordResult">
        <include refid="selectTeacherFeedbackRecordVo"/>
        <where>  
            <if test="feedbackTitle != null  and feedbackTitle != ''"> and feedback_title = #{feedbackTitle}</if>
            <if test="feedbackTeacherId != null  and feedbackTeacherId != ''"> and feedback_teacher_id = #{feedbackTeacherId}</if>
            <if test="feedbackType != null "> and feedback_type = #{feedbackType}</if>
            <if test="feedbackText != null  and feedbackText != ''"> and feedback_text = #{feedbackText}</if>
            <if test="replyUserId != null  and replyUserId != ''"> and reply_user_id = #{replyUserId}</if>
            <if test="replyTime != null "> and reply_time = #{replyTime}</if>
            <if test="replyText != null  and replyText != ''"> and reply_text = #{replyText}</if>
            <if test="params!=null and params.createTimeS !=null">and tfr.create_time &gt;= #{params.createTimeS}' 00:00:00' </if>
            <if test="params!=null and params.createTimeE !=null">and tfr.create_time &lt;= #{params.createTimeE}' 23:59:59' </if>
            <if test="replyStatus">
                and reply_user_id is not null
            </if>
            <if test="!replyStatus">
                and reply_user_id is null
            </if>
        </where>
        <if test="!replyStatus">
            ORDER BY tfr.id ASC
        </if>
        <if test="replyStatus">
            ORDER BY tfr.reply_time DESC
        </if>
    </select>
    
    <select id="selectTeacherFeedbackRecordById" parameterType="Long" resultMap="TeacherFeedbackRecordResult">
        <include refid="selectTeacherFeedbackRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTeacherFeedbackRecord" parameterType="TeacherFeedbackRecord" useGeneratedKeys="true" keyProperty="id">
        insert into ${DB_RED_BOOK_TEACH}.teacher_feedback_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="feedbackTeacherId != null">feedback_teacher_id,</if>
            <if test="feedbackType != null">feedback_type,</if>
            <if test="feedbackText != null">feedback_text,</if>
            <if test="createTime != null">create_time,</if>
            <if test="replyUserId != null">reply_user_id,</if>
            <if test="replyTime != null">reply_time,</if>
            <if test="replyText != null">reply_text,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="feedbackTeacherId != null">#{feedbackTeacherId},</if>
            <if test="feedbackType != null">#{feedbackType},</if>
            <if test="feedbackText != null">#{feedbackText},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="replyUserId != null">#{replyUserId},</if>
            <if test="replyTime != null">#{replyTime},</if>
            <if test="replyText != null">#{replyText},</if>
         </trim>
    </insert>

    <update id="updateTeacherFeedbackRecord" parameterType="TeacherFeedbackRecord">
        update ${DB_RED_BOOK_TEACH}.teacher_feedback_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="feedbackTitle != null">feedback_title = #{feedbackTitle},</if>
            <if test="feedbackTeacherId != null">feedback_teacher_id = #{feedbackTeacherId},</if>
            <if test="feedbackType != null">feedback_type = #{feedbackType},</if>
            <if test="feedbackText != null">feedback_text = #{feedbackText},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="replyUserId != null">reply_user_id = #{replyUserId},</if>
            <if test="replyTime != null">reply_time = #{replyTime},</if>
            <if test="replyText != null">reply_text = #{replyText},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherFeedbackRecordById" parameterType="Long">
        delete from ${DB_RED_BOOK_TEACH}.teacher_feedback_record where id = #{id}
    </delete>

    <delete id="deleteTeacherFeedbackRecordByIds" parameterType="String">
        delete from ${DB_RED_BOOK_TEACH}.teacher_feedback_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>