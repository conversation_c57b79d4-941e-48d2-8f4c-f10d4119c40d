<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.UserInfoMapper">
    <resultMap type="UserInfo" id="UserInfoResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="password" column="password"/>
        <result property="openId" column="open_id"/>
        <result property="mobilePhone" column="mobile_phone"/>
        <result property="aid" column="aid"/>
        <result property="exclusiveShopId" column="exclusive_shop_id"/>
        <result property="teachId" column="teach_id"/>
        <result property="classId" column="class_id"/>
        <result property="tableSuffix" column="table_suffix"/>
        <result property="registerDate" column="register_date"/>
        <result property="firstPurchaseDate" column="first_purchase_date"/>
        <result property="lastPurchaseDate" column="last_purchase_date"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="memberType" column="member_type"/>
        <result property="stage1ExpirationDate" column="stage1_expiration_date"/>
        <result property="stage2ExpirationDate" column="stage2_expiration_date"/>
        <result property="stage3ExpirationDate" column="stage3_expiration_date"/>
        <result property="stage4ExpirationDate" column="stage4_expiration_date"/>
        <result property="stage5ExpirationDate" column="stage5_expiration_date"/>
        <result property="stage11ExpirationDate" column="stage11_expiration_date"/>
        <result property="stage21ExpirationDate" column="stage21_expiration_date"/>
        <result property="stage" column="stage"/>
        <result property="mainVersionId" column="main_version_id"/>
        <result property="mainCourseId" column="main_course_id"/>
        <result property="nowGrade" column="now_grade"/>
        <result property="firstLevelQuizScore" column="first_level_quiz_score"/>
        <result property="lastLevelQuizScore" column="last_level_quiz_score"/>
        <result property="credits" column="credits"/>
        <result property="integral" column="integral"/>
        <result property="diamonds" column="diamonds"/>
        <result property="liveness" column="liveness"/>
        <result property="headImageUrl" column="head_image_url"/>
        <result property="headPendantImg" column="head_pendant_img"/>
        <result property="indexSkinImg" column="index_skin_img"/>
        <result property="username" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="email" column="email"/>
        <result property="qq" column="qq"/>
        <result property="signature" column="signature"/>
        <result property="school" column="school"/>
        <result property="gradeClass" column="grade_class"/>
        <result property="address" column="address"/>
        <result property="lastScore" column="last_score"/>
        <result property="payPwd" column="pay_pwd"/>
        <result property="totalSignCount" column="total_sign_count"/>
        <result property="isDelete" column="is_delete"/>
        <result property="agentName" column="agent_name"/>
        <result property="teacherName" column="teach_name"/>
        <result property="className" column="class_name"/>
        <result property="exclusiveShopName" column="exclusive_shop_name"/>
    </resultMap>

    <sql id="selectUserInfoVo">
        select ui.id,
               ui.user_id,
               ui.password,
               ui.open_id,
               ui.mobile_phone,
               ui.aid,
               ui.exclusive_shop_id,
               ui.teach_id,
               ui.class_id,
               ui.table_suffix,
               ui.register_date,
               ui.first_purchase_date,
               ui.last_purchase_date,
               ui.expiration_date,
               ui.member_type,
               ui.stage1_expiration_date,
               ui.stage2_expiration_date,
               ui.stage3_expiration_date,
               ui.stage4_expiration_date,
               ui.stage5_expiration_date,
               ui.stage11_expiration_date,
               ui.stage21_expiration_date,
               ui.stage,
               ui.main_version_id,
               ui.main_course_id,
               ui.now_grade,
               ui.first_level_quiz_score,
               ui.last_level_quiz_score,
               ui.credits,
               ui.integral,
               ui.diamonds,
               ui.liveness,
               ui.head_image_url,
               ui.head_pendant_img,
               ui.index_skin_img,
               ui.user_name,
               ui.nick_name,
               ui.sex,
               ui.birthday,
               ui.email,
               ui.qq,
               ui.signature,
               ui.school,
               ui.grade_class,
               ui.address,
               ui.last_score,
               ui.pay_pwd,
               ui.total_sign_count,
               ui.is_delete
               ,a.name      as agent_name,
               es.name as exclusive_shop_name,
               t.user_name as teach_name,
               c.name      as class_name
        from ${DB_RED_BOOK}.user_info ui
        left join agent a on ui.aid = a.aid
        left join agent_user t on ui.teach_id = t.user_id
        left join ${DB_RED_BOOK}.class c on ui.class_id = c.id
        left join exclusive_shop es on ui.exclusive_shop_id=es.id
    </sql>

    <select id="selectUserInfoList" parameterType="UserInfo" resultMap="UserInfoResult">
        <include refid="selectUserInfoVo"/>
        <if test="mId != null and aidList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        <where>
            <if test="userId != null  and userId != ''">
                and ui.user_id = #{userId}
            </if>
            <if test="password != null  and password != ''">
                and ui.password = #{password}
            </if>
            <if test="mobilePhone != null  and mobilePhone != ''">
                and ui.mobile_phone = #{mobilePhone}
            </if>
            <if test="aid != null  and aid != ''">
                and ui.aid = #{aid}
            </if>
            <if test="teachId != null  and teachId != ''">
                and ui.teach_id = #{teachId}
            </if>
            <if test="classId != null">
                and ui.class_id = #{classId}
            </if>
            <if test="registerDate != null">
                and ui.register_date = #{registerDate}
            </if>
            <if test="memberType != null and memberType==-10">
                and ui.member_type &lt;= 0
            </if>
            <if test="memberType != null and memberType==10">
                and ui.member_type > 0
            </if>
            <if test="memberType != null and memberType!=-10 and memberType!=10">
                and ui.member_type = #{memberType}
            </if>
            <if test="stage != null">
                and ui.stage = #{stage}
            </if>
            <if test="username != null  and username != ''">
                and ui.user_name like concat(#{username},'%')
            </if>
            <if test="nickName != null  and nickName != ''">
                and ui.nick_name like concat(#{nickName},'%')
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId!=-1">
                and ui.exclusive_shop_id = #{exclusiveShopId}
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId==-1">
                and ui.exclusive_shop_id is null
            </if>
            <if test="params != null and params.registerDateS != null">
                and ui.register_date >= #{params.registerDateS}
            </if>
            <if test="params != null and params.registerDateE != null">
                and ui.register_date &lt;= #{params.registerDateE}
            </if>
            <if test="params != null and params.expirationDateS != null">
                and ui.expiration_date >= #{params.expirationDateS}
            </if>
            <if test="params != null and params.expirationDateE != null">
                and ui.expiration_date &lt;= #{params.expirationDateE}
            </if>
            <if test="params != null and params.expiration != null and params.expiration == 'true'">
                and ui.expiration_date  &lt; CURRENT_DATE ()
            </if>
            <if test="params != null and params.expiration != null and params.expiration == 'false'">
                and ui.expiration_date &gt;= CURRENT_DATE ()
            </if>
            <if test="params != null and params.userIdS != null and params.userIdE != null">
                and ui.user_id between #{params.userIdS} and #{params.userIdE}
            </if>
            <if test="params != null and params.userId != null and params.userId != ''">
                and ui.user_id = #{params.userId}
            </if>
            <if test="params != null and params.username != null and params.username != ''">
                and ui.user_name = #{params.username}
            </if>
            <!--<if test="exclusiveShopIdList.size>0">
                and ui.exclusive_shop_id in
                <foreach collection="exclusiveShopIdList" item="esId" open="(" separator="," close=")">
                    #{esId}
                </foreach>
            </if>exclusiveShopIdList.size==0 and -->
            <if test="aidList.size>0">
                and ui.aid in
                <foreach collection="aidList" item="agentAidStr" open="(" separator="," close=")">
                    #{agentAidStr}
                </foreach>
            </if>
            <if test="mId != null and aidList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
            <!--<if test="agentId != null">
                and a.id = #{agentId}
            </if>-->
        </where>
        <!--<if test="pageNum!=null and pageSize!=null">
            limit ${pageNum},${pageSize}
        </if>-->
    </select>

    <select id="countUserInfoList" parameterType="UserInfo" resultType="map">
        SELECT
            SUM(IF(ui.member_type=3,1,0)) svipNum,
            SUM(IF(ui.member_type=2,1,0)) vipNum,
            SUM(IF(ui.member_type=1,1,0)) ordinaryNum,
            SUM(IF(ui.member_type=0,1,0)) experienceNum,
            SUM(IF(ui.member_type=-1,1,0)) pcExperienceNum
        from ${DB_RED_BOOK}.user_info ui
            <if test="exclusiveShopManagerId != null">
                left join exclusive_shop es on ui.exclusive_shop_id=es.id
            </if>
            <if test="mId != null and aidList.size==0">
                left join agent a on ui.aid = a.aid
                LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
            </if>
        <where>
            <if test="userId != null  and userId != ''">
                and ui.user_id = #{userId}
            </if>
            <if test="password != null  and password != ''">
                and ui.password = #{password}
            </if>
            <if test="mobilePhone != null  and mobilePhone != ''">
                and ui.mobile_phone = #{mobilePhone}
            </if>
            <if test="aid != null  and aid != ''">
                and ui.aid = #{aid}
            </if>
            <if test="teachId != null  and teachId != ''">
                and ui.teach_id = #{teachId}
            </if>
            <if test="classId != null">
                and ui.class_id = #{classId}
            </if>
            <if test="registerDate != null">
                and ui.register_date = #{registerDate}
            </if>
            <if test="memberType != null and memberType==-10">
                and ui.member_type &lt;= 0
            </if>
            <if test="memberType != null and memberType==10">
                and ui.member_type > 0
            </if>
            <if test="memberType != null and memberType!=-10 and memberType!=10">
                and ui.member_type = #{memberType}
            </if>
            <if test="stage != null">
                and ui.stage = #{stage}
            </if>
            <if test="username != null  and username != ''">
                and ui.user_name like concat(#{username},'%')
            </if>
            <if test="nickName != null  and nickName != ''">
                and ui.nick_name like concat(#{nickName},'%')
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId!=-1">
                and ui.exclusive_shop_id = #{exclusiveShopId}
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId==-1">
                and ui.exclusive_shop_id is null
            </if>
            <if test="params != null and params.registerDateS != null">
                and ui.register_date >= #{params.registerDateS}
            </if>
            <if test="params != null and params.registerDateE != null">
                and ui.register_date &lt;= #{params.registerDateE}
            </if>
            <if test="params != null and params.expirationDateS != null">
                and ui.expiration_date >= #{params.expirationDateS}
            </if>
            <if test="params != null and params.expirationDateE != null">
                and ui.expiration_date &lt;= #{params.expirationDateE}
            </if>
            <if test="params != null and params.expiration != null and params.expiration == 'true'">
                and ui.expiration_date  &lt; CURRENT_DATE ()
            </if>
            <if test="params != null and params.expiration != null and params.expiration == 'false'">
                and ui.expiration_date &gt;= CURRENT_DATE ()
            </if>
            <if test="params != null and params.userIdS != null and params.userIdE != null">
                and ui.user_id between #{params.userIdS} and #{params.userIdE}
            </if>
            <if test="params != null and params.userId != null and params.userId != ''">
                and ui.user_id = #{params.userId}
            </if>
            <if test="params != null and params.username != null and params.username != ''">
                and ui.user_name = #{params.username}
            </if>
            <if test="aidList.size>0">
                and ui.aid in
                <foreach collection="aidList" item="agentAidStr" open="(" separator="," close=")">
                    #{agentAidStr}
                </foreach>
            </if>
            <if test="mId != null and aidList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
        </where>
    </select>

    <select id="selectFormalUserCount" resultType="int">
        select count(id) from ${DB_RED_BOOK}.user_info
        where member_type &gt; 0 and expiration_date>=current_date()
            <if test="aid != null  and aid != ''">
                and aid = #{aid}
            </if>
            <if test="exclusiveShopId != null">
                and exclusive_shop_id = #{exclusiveShopId}
            </if>
    </select>
    <select id="selectExperienceUserCount" resultType="int">
        select count(id) from ${DB_RED_BOOK}.user_info
        where member_type &lt;=0 and expiration_date>=current_date()
            <if test="aid != null  and aid != ''">
                and aid = #{aid}
            </if>
            <if test="exclusiveShopId != null">
                and exclusive_shop_id = #{exclusiveShopId}
            </if>
    </select>
    <select id="getAgentStudentBasicInfo" parameterType="String" resultType="java.util.HashMap">
        SELECT u.id,
               u.user_id                           userId,
               u.user_name                         username,
               u.nick_name                         nickName,
               u.head_image_url                    headImageUrl,
               u.sex,
               DATE_FORMAT(u.birthday, '%Y-%m-%d') birthday,
               u.now_grade,
               u.grade_class                       gradeClass,
               u.school,
               u.address,
               u.email,
               u.qq,
               u.mobile_phone                      mobilePhone,
               u.last_score                        lastScore,
               u.teach_id,
               u.class_id,
               u.table_suffix,
               u.register_date,
               u.first_purchase_date,
               u.last_purchase_date,
               u.expiration_date,
               u.member_type,
               u.stage1_expiration_date,
               u.stage2_expiration_date,
               u.stage3_expiration_date,
               u.stage4_expiration_date,
               u.stage5_expiration_date,
               u.stage11_expiration_date,
               u.stage21_expiration_date,
               u.stage,
               u.main_version_id,
               u.main_course_id,
               u.now_grade,
               uoi.`id`                            otherInfoId,
               uoi.`chinese_score`                 chineseScore,
               uoi.`maths_score`                   mathsScore,
               uoi.`history_score`                 historyScore,
               uoi.politics_score                  politicsScore,
               uoi.physics_score                   physicsScore,
               uoi.chemistry_score                 chemistryScore,
               uoi.biology_score                   biologyScore,
               uoi.geography_score                 geographyScore,
               uoi.school_ranking                  schoolRanking,
               uoi.class_ranking                   classRanking
        FROM ${DB_RED_BOOK}.`user_info` u
                 LEFT JOIN ${DB_RED_BOOK}.`user_other_info` uoi ON uoi.user_id = u.`user_id`
        WHERE u.user_id = #{userId};
    </select>


    <select id="selectUserInfoById" parameterType="Long" resultMap="UserInfoResult">
        <include refid="selectUserInfoVo"/>
        where ui.id = #{id}
    </select>

    <insert id="insertUserInfo" parameterType="UserInfo" useGeneratedKeys="true" keyProperty="id">
        insert into ${DB_RED_BOOK}.user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="password != null and password != ''">
                password,
            </if>
            <if test="openId != null">
                open_id,
            </if>
            <if test="mobilePhone != null">
                mobile_phone,
            </if>
            <if test="aid != null and aid != ''">
                aid,
            </if>
            <if test="teachId != null">
                teach_id,
            </if>
            <if test="classId != null">
                class_id,
            </if>
            <if test="tableSuffix != null">
                table_suffix,
            </if>
            <if test="registerDate != null">
                register_date,
            </if>
            <if test="firstPurchaseDate != null">
                first_purchase_date,
            </if>
            <if test="lastPurchaseDate != null">
                last_purchase_date,
            </if>
            <if test="expirationDate != null">
                expiration_date,
            </if>
            <if test="memberType != null">
                member_type,
            </if>
            <if test="stage1ExpirationDate != null">
                stage1_expiration_date,
            </if>
            <if test="stage2ExpirationDate != null">
                stage2_expiration_date,
            </if>
            <if test="stage3ExpirationDate != null">
                stage3_expiration_date,
            </if>
            <if test="stage != null">
                stage,
            </if>
            <if test="mainVersionId != null">
                main_version_id,
            </if>
            <if test="mainCourseId != null">
                main_course_id,
            </if>
            <if test="nowGrade != null">
                now_grade,
            </if>
            <if test="firstLevelQuizScore != null">
                first_level_quiz_score,
            </if>
            <if test="lastLevelQuizScore != null">
                last_level_quiz_score,
            </if>
            <if test="credits != null">
                credits,
            </if>
            <if test="integral != null">
                integral,
            </if>
            <if test="diamonds != null">
                diamonds,
            </if>
            <if test="liveness != null">
                liveness,
            </if>
            <if test="headImageUrl != null and headImageUrl != ''">
                head_image_url,
            </if>
            <if test="headPendantImg != null">
                head_pendant_img,
            </if>
            <if test="indexSkinImg != null">
                index_skin_img,
            </if>
            <if test="username != null">
                user_name,
            </if>
            <if test="nickName != null">
                nick_name,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="qq != null">
                qq,
            </if>
            <if test="signature != null">
                signature,
            </if>
            <if test="school != null">
                school,
            </if>
            <if test="gradeClass != null">
                grade_class,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="lastScore != null">
                last_score,
            </if>
            <if test="payPwd != null">
                pay_pwd,
            </if>
            <if test="totalSignCount != null">
                total_sign_count,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="password != null and password != ''">
                #{password},
            </if>
            <if test="openId != null">
                #{openId},
            </if>
            <if test="mobilePhone != null">
                #{mobilePhone},
            </if>
            <if test="aid != null and aid != ''">
                #{aid},
            </if>
            <if test="teachId != null">
                #{teachId},
            </if>
            <if test="classId != null">
                #{classId},
            </if>
            <if test="tableSuffix != null">
                #{tableSuffix},
            </if>
            <if test="registerDate != null">
                #{registerDate},
            </if>
            <if test="firstPurchaseDate != null">
                #{firstPurchaseDate},
            </if>
            <if test="lastPurchaseDate != null">
                #{lastPurchaseDate},
            </if>
            <if test="expirationDate != null">
                #{expirationDate},
            </if>
            <if test="memberType != null">
                #{memberType},
            </if>
            <if test="stage1ExpirationDate != null">
                #{stage1ExpirationDate},
            </if>
            <if test="stage2ExpirationDate != null">
                #{stage2ExpirationDate},
            </if>
            <if test="stage3ExpirationDate != null">
                #{stage3ExpirationDate},
            </if>
            <if test="stage != null">
                #{stage},
            </if>
            <if test="mainVersionId != null">
                #{mainVersionId},
            </if>
            <if test="mainCourseId != null">
                #{mainCourseId},
            </if>
            <if test="nowGrade != null">
                #{nowGrade},
            </if>
            <if test="firstLevelQuizScore != null">
                #{firstLevelQuizScore},
            </if>
            <if test="lastLevelQuizScore != null">
                #{lastLevelQuizScore},
            </if>
            <if test="credits != null">
                #{credits},
            </if>
            <if test="integral != null">
                #{integral},
            </if>
            <if test="diamonds != null">
                #{diamonds},
            </if>
            <if test="liveness != null">
                #{liveness},
            </if>
            <if test="headImageUrl != null and headImageUrl != ''">
                #{headImageUrl},
            </if>
            <if test="headPendantImg != null">
                #{headPendantImg},
            </if>
            <if test="indexSkinImg != null">
                #{indexSkinImg},
            </if>
            <if test="username != null">
                #{username},
            </if>
            <if test="nickName != null">
                #{nickName},
            </if>
            <if test="sex != null">
                #{sex},
            </if>
            <if test="birthday != null">
                #{birthday},
            </if>
            <if test="email != null">
                #{email},
            </if>
            <if test="qq != null">
                #{qq},
            </if>
            <if test="signature != null">
                #{signature},
            </if>
            <if test="school != null">
                #{school},
            </if>
            <if test="gradeClass != null">
                #{gradeClass},
            </if>
            <if test="address != null">
                #{address},
            </if>
            <if test="lastScore != null">
                #{lastScore},
            </if>
            <if test="payPwd != null">
                #{payPwd},
            </if>
            <if test="totalSignCount != null">
                #{totalSignCount},
            </if>
        </trim>
    </insert>

    <update id="updateUserInfo" parameterType="UserInfo">
        update ${DB_RED_BOOK}.user_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="openId != null">
                open_id = #{openId},
            </if>
            <if test="mobilePhone != null">
                mobile_phone = #{mobilePhone},
            </if>
            <if test="aid != null and aid != ''">
                aid = #{aid},
            </if>
            <if test="teachId != null">
                teach_id = #{teachId},
            </if>
            <if test="classId != null">
                class_id = #{classId},
            </if>
            <if test="tableSuffix != null">
                table_suffix = #{tableSuffix},
            </if>
            <if test="registerDate != null">
                register_date = #{registerDate},
            </if>
            <if test="firstPurchaseDate != null">
                first_purchase_date = #{firstPurchaseDate},
            </if>
            <if test="lastPurchaseDate != null">
                last_purchase_date = #{lastPurchaseDate},
            </if>
            <if test="expirationDate != null">
                expiration_date = #{expirationDate},
            </if>
            <if test="memberType != null">
                member_type = #{memberType},
            </if>
            <if test="stage1ExpirationDate != null">
                stage1_expiration_date = #{stage1ExpirationDate},
            </if>
            <if test="stage2ExpirationDate != null">
                stage2_expiration_date = #{stage2ExpirationDate},
            </if>
            <if test="stage3ExpirationDate != null">
                stage3_expiration_date = #{stage3ExpirationDate},
            </if>
            <if test="stage4ExpirationDate != null">
                stage4_expiration_date = #{stage4ExpirationDate},
            </if>
            <if test="stage5ExpirationDate != null">
                stage5_expiration_date = #{stage5ExpirationDate},
            </if>
            <if test="stage11ExpirationDate != null">
                stage11_expiration_date = #{stage11ExpirationDate},
            </if>
            <if test="stage21ExpirationDate != null">
                stage21_expiration_date = #{stage21ExpirationDate},
            </if>
            <if test="stage != null">
                stage = #{stage},
            </if>
            <if test="mainVersionId != null">
                main_version_id = #{mainVersionId},
            </if>
            <if test="mainCourseId != null">
                main_course_id = #{mainCourseId},
            </if>
            <if test="nowGrade != null">
                now_grade = #{nowGrade},
            </if>
            <if test="firstLevelQuizScore != null">
                first_level_quiz_score = #{firstLevelQuizScore},
            </if>
            <if test="lastLevelQuizScore != null">
                last_level_quiz_score = #{lastLevelQuizScore},
            </if>
            <if test="credits != null">
                credits = #{credits},
            </if>
            <if test="integral != null">
                integral = #{integral},
            </if>
            <if test="diamonds != null">
                diamonds = #{diamonds},
            </if>
            <if test="liveness != null">
                liveness = #{liveness},
            </if>
            <if test="headImageUrl != null and headImageUrl != ''">
                head_image_url = #{headImageUrl},
            </if>
            <if test="headPendantImg != null">
                head_pendant_img = #{headPendantImg},
            </if>
            <if test="indexSkinImg != null">
                index_skin_img = #{indexSkinImg},
            </if>
            <if test="username != null">
                user_name = #{username},
            </if>
            <if test="nickName != null">
                nick_name = #{nickName},
            </if>
            <if test="sex != null">
                sex = #{sex},
            </if>
            <if test="birthday != null">
                birthday = #{birthday},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="qq != null">
                qq = #{qq},
            </if>
            <if test="signature != null">
                signature = #{signature},
            </if>
            <if test="school != null">
                school = #{school},
            </if>
            <if test="gradeClass != null">
                grade_class = #{gradeClass},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="lastScore != null">
                last_score = #{lastScore},
            </if>
            <if test="payPwd != null">
                pay_pwd = #{payPwd},
            </if>
            <if test="totalSignCount != null">
                total_sign_count = #{totalSignCount},
            </if>
        </trim>
        where user_id = #{userId} and is_delete=0
    </update>

    <delete id="deleteUserInfoById" parameterType="Long">
        delete
        from ${DB_RED_BOOK}.user_info
        where id = #{id}
    </delete>

    <delete id="deleteUserInfoByIds" parameterType="String">
        delete
        from ${DB_RED_BOOK}.user_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 获取小红本会员的续费记录 -->
    <select id="getTydRenewRecord" resultType="map">
        select a.name,
               trr.id,
               case trr.renew_stage
                   when 1 then '小学'
                   when 2 then '初中'
                   when 3 then '高中'
                   when 4 then '大学'
                   when 5 then '出国'
                   when 11 then '小升初'
                   when 21 then '初升高'
                   when 99 then '少儿'
                   ELSE '-' END      renew_stage,
               case trr.renew_time_len
                   when 1 then '1个月'
                   when 3 then '3个月'
                   when 6 then '6个月'
                   when 12 then '12个月'
                   ELSE
                       '其他' END    renew_time_len,
               case trr.first_renew
                   when 1 then '首次购买'
                   when 0 then '续费'
                   when -1 then '0元转'
                   ELSE '其他' END   first_renew,
               renew_duration,
               trr.pay_money,
               trr.present_time_len,
               trr.member_user_id as member_user_id,
               trr.pay_time,
               trr.ecard_number,
               ati.fund_type         fundType,
               trr.activity_content_id activityContentId,
               trr.buy_time_len      buyTimeLen
        from tyd_renew_record trr
                 left join agent a on a.id = trr.agent_id
                 left join agent_transaction_info ati on ati.indent_number = trr.indent_number
        Where trr.member_user_id = #{userId}
          and trr.`is_refund` = 'N'
        group by trr.id
        order by trr.pay_time desc
    </select>

    <select id="getTydRenewRecordCount" resultType="integer">
        select count(trr.member_user_id)
        from tyd_renew_record trr
                 left join agent a on a.id = trr.agent_id
        Where trr.member_user_id = #{userId}
          and trr.`is_refund` = 'N'
    </select>

    <select id="getUserIpRedBook" resultType="map">
        select u.user_name,
               u.aid,
               ul.id,
               ul.user_id,
               ul.ip,
               ul.section,
               ul.logon_time,
               ul.version_code,
               ul.version_name,
               ul.SN,
               ul.MAC,
               ul.other_provinces_login
        from ${DB_RED_BOOK}.user_logon_record ul
             left join ${DB_RED_BOOK}.user_info u on u.user_id = ul.user_id
        where
        <if test="userId!=null and userId!=''">
            ul.user_id = #{userId}
        </if>
        <if test="userId==null and aid!=null and aid!=''">
            u.aid = #{aid}
        </if>
        <if test="otherProvincesLogin!=null">
            and ul.other_provinces_login = #{otherProvincesLogin}
        </if>
<!--        <if test="limitStartTime != null and limitStartTime != ''">-->
<!--            and ul.logon_time &gt;=#{limitStartTime}-->
<!--            and ul.logon_time &lt;=#{limitEndTime}-->
<!--        </if>-->
        ORDER BY ul.logon_time DESC
        <if test="pageIndex != null and pageIndex != -1 and pageSize != null and pageSize != -1">
            LIMIT ${pageIndex},${pageSize}
        </if>
    </select>
    <select id="getUserIpRedBookCount" resultType="integer">
        select count(ul.id)
        from ${DB_RED_BOOK}.user_logon_record ul
            left join ${DB_RED_BOOK}.user_info u on u.user_id = ul.user_id
        where
        <if test="userId!=null and userId!=''">
            ul.user_id = #{userId}
        </if>
        <if test="userId==null and aid!=null and aid!=''">
            u.aid = #{aid}
        </if>
        <if test="otherProvincesLogin!=null">
            and ul.other_provinces_login = #{otherProvincesLogin}
        </if>
<!--        <if test="limitStartTime != null and limitStartTime != ''">-->
<!--            and ul.logon_time &gt;=#{limitStartTime}-->
<!--            and ul.logon_time &lt;=#{limitEndTime}-->
<!--        </if>-->
    </select>


    <select id="getUserOtherInfo" resultType="com.redbook.system.domain.UserOtherInfoBean">
        SELECT id,
               user_id         userId,
               chinese_score   chineseScore,
               maths_score     mathsScore,
               history_score   historyScore
                ,
               politics_score  politicsScore,
               physics_score   physicsScore,
               chemistry_score chemistryScore,
               biology_score   biologyScore,
               geography_score geographyScore,
               school_ranking  schoolRanking,
               class_ranking   classRanking
        FROM ${DB_RED_BOOK}.`user_other_info`
        WHERE user_id = #{userId}
    </select>
    <select id="getTeacherList" resultType="com.redbook.system.domain.vo.TeacherInfo" >
        select au.user_id userId, au.user_name userName, au.aid aid, a.id agentId,au.user_type userType
        from agent_user au
        left join agent a on au.aid = a.aid
        where au.user_type in (32,33)
        <if test="aid != null">
            and au.aid = #{aid}
        </if>
        <if test="exclusiveShopId != null">
            and au.exclusive_shop_id=#{exclusiveShopId}
        </if>
    </select>
    <select id="getClassList" resultType="com.redbook.system.domain.vo.ClassInfo"
            parameterType="java.lang.String">
        select id, name, user_id teacherId, aid
        from ${DB_RED_BOOK}.class
        where user_id = #{userId}
    </select>


    <insert id="insertUserOtherInfo">
        INSERT INTO ${DB_RED_BOOK}.`user_other_info`(user_id,
                                                     chinese_score,
                                                     maths_score,
                                                     history_score,
                                                     politics_score,
                                                     physics_score,
                                                     chemistry_score,
                                                     biology_score,
                                                     geography_score,
                                                     school_ranking,
                                                     class_ranking)
        VALUES (#{userId},
                #{chineseScore},
                #{mathsScore},
                #{historyScore},
                #{politicsScore},
                #{physicsScore},
                #{chemistryScore},
                #{biologyScore},
                #{geographyScore},
                #{schoolRanking},
                #{classRanking});
    </insert>
    <update id="updateUserOtherInfo">
        UPDATE ${DB_RED_BOOK}.`user_other_info`
        SET chinese_score=#{chineseScore},
            maths_score=#{mathsScore},
            history_score=#{historyScore},
            politics_score=#{politicsScore},
            physics_score=#{physicsScore},
            chemistry_score=#{chemistryScore},
            biology_score=#{biologyScore},
            geography_score=#{geographyScore},
            school_ranking=#{schoolRanking},
            class_ranking=#{classRanking}
        WHERE user_id = #{userId}
    </update>

    <select id="selectUserInfoByUserId" resultType="com.redbook.system.domain.UserInfo">
        <include refid="selectUserInfoVo"/>
        where ui.user_id = #{userId}
    </select>

    <update id="updateStudentExpire">
        update ${DB_RED_BOOK}.user_info
        set expiration_date        = #{expirationDate},
            stage1_expiration_date = #{stage1ExpirationDate},
            stage2_expiration_date = #{stage2ExpirationDate},
            stage3_expiration_date = #{stage3ExpirationDate},
            stage4_expiration_date = #{stage4ExpirationDate},
            stage5_expiration_date = #{stage5ExpirationDate},
            stage11_expiration_date = #{stage11ExpirationDate},
            stage21_expiration_date = #{stage21ExpirationDate},
            stage                  = #{stage},
            member_type            = #{memberType},
            integral            = #{integral}
        where user_id = #{userId} and is_delete=0
    </update>
    <update id="updateStudentAgentByTeacher">
        update ${DB_RED_BOOK}.user_info set aid=#{aid},exclusive_shop_id=#{exclusiveShopId} where teach_id=#{teachId}
    </update>
</mapper>