<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.AgentActivityShopMapper">
    
    <resultMap type="AgentActivityShop" id="AgentActivityShopResult">
        <result property="id"    column="id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="shopName"    column="shopName"    />
    </resultMap>

    <sql id="selectAgentActivityShopVo">
        select id, activity_id, shop_id from agent_activity_shop
    </sql>

    <select id="selectAgentActivityShopList" parameterType="AgentActivityShop" resultMap="AgentActivityShopResult">
        <include refid="selectAgentActivityShopVo"/>
        <where>  
            <if test="activityId != null "> and activity_id = #{activityId}</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
        </where>
    </select>
    
    <select id="selectAgentActivityShopById" resultMap="AgentActivityShopResult">
        <include refid="selectAgentActivityShopVo"/>
        where id = #{id}
    </select>

    <select id="shopList"  resultMap="AgentActivityShopResult">
        select s.id, s.activity_id, s.shop_id,e.name as shopName
        from agent_activity_shop s
        left join exclusive_shop e on s.shop_id = e.id
        where s.activity_id = #{shopList}
    </select>
        
    <insert id="insertAgentActivityShop" parameterType="AgentActivityShop" useGeneratedKeys="true" keyProperty="id">
        insert into agent_activity_shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="shopId != null">shop_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="shopId != null">#{shopId},</if>
         </trim>
    </insert>

    <update id="updateAgentActivityShop" parameterType="AgentActivityShop">
        update agent_activity_shop
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgentActivityShopById">
        delete from agent_activity_shop where id = #{id}
    </delete>

    <delete id="deleteAgentActivityShopByActivityId">
        delete from agent_activity_shop where activity_id = #{activityId}
    </delete>

    <delete id="deleteAgentActivityShopByIds" parameterType="String">
        delete from agent_activity_shop where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>