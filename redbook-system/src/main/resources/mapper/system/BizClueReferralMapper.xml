<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.BizClueReferralMapper">
    
    <resultMap type="BizClueReferral" id="BizClueReferralResult">
        <result property="clueReferralId"    column="clue_referral_id"    />
        <result property="clueId"    column="clue_id"    />
        <result property="guestClueId"    column="guest_clue_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createById"    column="create_by_id"    />
    </resultMap>

    <sql id="selectBizClueReferralVo">
        select clue_referral_id, clue_id, guest_clue_id, create_time, create_by, create_by_id 
        from ${DB_RED_BOOK_SALE}.biz_clue_referral
    </sql>

    <select id="selectBizClueReferralList" parameterType="BizClueReferral" resultMap="BizClueReferralResult">
        <include refid="selectBizClueReferralVo"/>
        <where>  
            <if test="clueId != null "> and clue_id = #{clueId}</if>
            <if test="guestClueId != null "> and guest_clue_id = #{guestClueId}</if>
            <if test="createById != null "> and create_by_id = #{createById}</if>
        </where>
    </select>
    
    <select id="selectBizClueReferralByClueReferralId" parameterType="Long" resultMap="BizClueReferralResult">
        <include refid="selectBizClueReferralVo"/>
        where clue_referral_id = #{clueReferralId}
    </select>
        
    <insert id="insertBizClueReferral" parameterType="BizClueReferral" useGeneratedKeys="true" keyProperty="clueReferralId">
        insert into ${DB_RED_BOOK_SALE}.biz_clue_referral
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clueId != null">clue_id,</if>
            <if test="guestClueId != null">guest_clue_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createById != null">create_by_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clueId != null">#{clueId},</if>
            <if test="guestClueId != null">#{guestClueId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createById != null">#{createById},</if>
         </trim>
    </insert>

    <update id="updateBizClueReferral" parameterType="BizClueReferral">
        update ${DB_RED_BOOK_SALE}.biz_clue_referral
        <trim prefix="SET" suffixOverrides=",">
            <if test="clueId != null">clue_id = #{clueId},</if>
            <if test="guestClueId != null">guest_clue_id = #{guestClueId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
        </trim>
        where clue_referral_id = #{clueReferralId}
    </update>

    <delete id="deleteBizClueReferralByClueReferralId" parameterType="Long">
        delete from ${DB_RED_BOOK_SALE}.biz_clue_referral where clue_referral_id = #{clueReferralId}
    </delete>

    <delete id="deleteBizClueReferralByClueReferralIds" parameterType="String">
        delete from ${DB_RED_BOOK_SALE}.biz_clue_referral where clue_referral_id in 
        <foreach item="clueReferralId" collection="array" open="(" separator="," close=")">
            #{clueReferralId}
        </foreach>
    </delete>
</mapper>