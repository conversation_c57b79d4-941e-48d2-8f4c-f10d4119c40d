<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.MarketIntegrativeQuizMapper">
    
    <resultMap type="MarketIntegrativeQuiz" id="MarketIntegrativeQuizResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="exclusiveShopId"    column="exclusive_shop_id"    />
        <result property="shopCode"    column="shopCode"    />
        <result property="quizType"    column="quiz_type"    />
        <result property="quizName"    column="quiz_name"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseMap"    column="course_map"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectMarketIntegrativeQuizVo">
        select id, agent_id, exclusive_shop_id,quiz_type, quiz_name,course_name,course_map,img_url, create_time from market_integrative_quiz
    </sql>

    <select id="selectMarketIntegrativeQuizList" parameterType="MarketIntegrativeQuiz" resultMap="MarketIntegrativeQuizResult">
        select q.id, q.agent_id, q.exclusive_shop_id,q.quiz_type, q.quiz_name,q.course_name,q.course_map,q.img_url, q.create_time,s.code as shopCode
        from market_integrative_quiz q
        left join exclusive_shop s on s.id = q.exclusive_shop_id
        <where>
            and q.agent_id = #{agentId}
            <if test="exclusiveShopId != null "> and q.exclusive_shop_id = #{exclusiveShopId}</if>
            <if test="exclusiveShopId == null "> and q.exclusive_shop_id is null</if>
        </where>
    </select>
    
    <select id="selectMarketIntegrativeQuizById" parameterType="Long" resultMap="MarketIntegrativeQuizResult">
        <include refid="selectMarketIntegrativeQuizVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMarketIntegrativeQuiz" parameterType="MarketIntegrativeQuiz" useGeneratedKeys="true" keyProperty="id">
        insert into market_integrative_quiz
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">agent_id,</if>
            <if test="exclusiveShopId != null ">exclusive_shop_id,</if>
            <if test="quizType != null">quiz_type,</if>
            <if test="quizName != null">quiz_name,</if>
            <if test="courseName != null">course_name,</if>
            <if test="courseMap != null">course_map,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">#{agentId},</if>
            <if test="exclusiveShopId != null ">#{exclusiveShopId},</if>
            <if test="quizType != null">#{quizType},</if>
            <if test="quizName != null">#{quizName},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="courseMap != null">#{courseMap},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateMarketIntegrativeQuiz" parameterType="MarketIntegrativeQuiz">
        update market_integrative_quiz
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="exclusiveShopId != null "> exclusive_shop_id = #{exclusiveShopId},</if>
            <if test="quizType != null">quiz_type = #{quizType},</if>
            <if test="quizName != null">quiz_name = #{quizName},</if>
            <if test="courseName != null">course_name= #{courseName},</if>
            <if test="courseMap != null">course_map= #{courseMap},</if>
            img_url = #{imgUrl},
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMarketIntegrativeQuizById" parameterType="Long">
        delete from market_integrative_quiz where id = #{id}
    </delete>

    <delete id="deleteMarketIntegrativeQuizByIds" parameterType="String">
        delete from market_integrative_quiz where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>