<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ElectronicRenewCardMapper">
    <resultMap type="ElectronicRenewCard" id="ElectronicRenewCardResult">
        <result property="id" column="id"/>
        <result property="agentId" column="agent_id"/>
        <result property="agentName" column="agent_name"/>
        <result property="buyAgentId" column="buy_agent_id"/>
        <result property="cardNumber" column="card_number"/>
        <result property="password" column="password"/>
        <result property="renewTimeLen" column="renew_time_len"/>
        <result property="buyDate" column="buy_date"/>
        <result property="status" column="status"/>
        <result property="money" column="money"/>
        <result property="expireDate" column="expire_date"/>
        <result property="usageTime" column="usage_time"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="renewStage" column="renew_stage"/>
        <result property="renewBeforeDate" column="renew_before_date"/>
        <result property="renewAfterDate" column="renew_after_date"/>
        <result property="indentNumber" column="indent_number"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="exclusiveShopId" column="exclusive_shop_id"/>
        <result property="exclusiveShopName" column="exclusive_shop_name"/>
    </resultMap>

    <sql id="selectElectronicRenewCardVo">
        select erc.id,
               erc.agent_id,
               erc.exclusive_shop_id,
               erc.buy_agent_id,
               erc.card_number,
               erc.password,
               erc.renew_time_len,
               erc.buy_date,
               erc.status,
               erc.money,
               erc.expire_date,
               erc.usage_time,
               erc.user_id,
               erc.user_name,
               erc.renew_stage,
               erc.indent_number,
               erc.renew_before_date,
               erc.renew_after_date,
               erc.create_time,
               erc.update_time,
               a.name as agent_name,
               es.name exclusive_shop_name
        from electronic_renew_card erc
                 LEFT JOIN agent a ON a.id = erc.agent_id
                 LEFT JOIN sys_user u2 on a.contact_person = u2.user_id
                 left join exclusive_shop es on erc.exclusive_shop_id=es.id
    </sql>

    <select id="selectElectronicRenewCardList" parameterType="ElectronicRenewCard"
            resultMap="ElectronicRenewCardResult">
        <include refid="selectElectronicRenewCardVo"/>
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        <where>
            <if test="agentId != null">
                and erc.agent_id = #{agentId}
            </if>
            <if test="buyAgentId != null">
                and erc.buy_agent_id = #{buyAgentId}
            </if>
            <if test="cardNumber != null  and cardNumber != ''">
                and erc.card_number = #{cardNumber}
            </if>
            <if test="password != null  and password != ''">
                and erc.password = #{password}
            </if>
            <if test="renewTimeLen != null">
                and erc.renew_time_len = #{renewTimeLen}
            </if>
            <if test="buyDate != null">
                and erc.buy_date = #{buyDate}
            </if>
            <if test="status != null">
                and erc.status = #{status}
            </if>
            <if test="money != null">
                and erc.money = #{money}
            </if>
            <if test="expireDate != null">
                and erc.expire_date = #{expireDate}
            </if>
            <if test="usageTime != null">
                and erc.usage_time = #{usageTime}
            </if>
            <if test="userId != null  and userId != ''">
                and erc.user_id = #{userId}
            </if>
            <if test="userName != null  and userName != ''">
                and erc.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="renewStage != null">
                and erc.renew_stage = #{renewStage}
            </if>
            <if test="renewBeforeDate != null">
                and erc.renew_before_date = #{renewBeforeDate}
            </if>
            <if test="renewAfterDate != null">
                and erc.renew_after_date = #{renewAfterDate}
            </if>
            <if test="params != null and params.usageTimeS != null">
                and erc.usage_time &gt;=
                    #{params.usageTimeS}' 00:00:00'
            </if>
            <if test="params != null and params.usageTimeE != null">
                and erc.usage_time &lt;=
                    #{params.usageTimeE}' 23:59:59'
            </if>
            <if test="params != null and params.buyDateS != null">
                and erc.buy_date &gt;=
                    #{params.buyDateS}' 00:00:00'
            </if>
            <if test="params != null and params.buyDateE != null">
                and erc.buy_date &lt;=
                    #{params.buyDateE}' 23:59:59'
            </if>
            <if test="params != null and params.expire != null and params.expire == 'true'">
                and erc.expire_date
                    &lt; CURRENT_DATE()
            </if>
            <if test="params != null and params.expire != null and params.expire == 'false'">
                and erc.expire_date >= CURRENT_DATE()
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
                and es.status=0
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId!=-1">
                and erc.exclusive_shop_id = #{exclusiveShopId}
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId==-1">
                and erc.exclusive_shop_id is null
            </if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectElectronicRenewCardById" parameterType="Long" resultMap="ElectronicRenewCardResult">
        <include refid="selectElectronicRenewCardVo"/>
        where erc.id = #{id}
    </select>

    <insert id="insertElectronicRenewCard" parameterType="ElectronicRenewCard" useGeneratedKeys="true" keyProperty="id">
        insert into electronic_renew_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">
                agent_id,
            </if>
            <if test="buyAgentId != null">
                buy_agent_id,
            </if>
            <if test="cardNumber != null and cardNumber != ''">
                card_number,
            </if>
            <if test="password != null and password != ''">
                password,
            </if>
            <if test="renewTimeLen != null">
                renew_time_len,
            </if>
            <if test="buyDate != null">
                buy_date,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="money != null">
                money,
            </if>
            <if test="expireDate != null">
                expire_date,
            </if>
            <if test="usageTime != null">
                usage_time,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="renewStage != null">
                renew_stage,
            </if>
            <if test="renewBeforeDate != null">
                renew_before_date,
            </if>
            <if test="renewAfterDate != null">
                renew_after_date,
            </if>
            <if test="indentNumber != null and indentNumber != ''">
                indent_number,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">
                #{agentId},
            </if>
            <if test="buyAgentId != null">
                #{buyAgentId},
            </if>
            <if test="cardNumber != null and cardNumber != ''">
                #{cardNumber},
            </if>
            <if test="password != null and password != ''">
                #{password},
            </if>
            <if test="renewTimeLen != null">
                #{renewTimeLen},
            </if>
            <if test="buyDate != null">
                #{buyDate},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="money != null">
                #{money},
            </if>
            <if test="expireDate != null">
                #{expireDate},
            </if>
            <if test="usageTime != null">
                #{usageTime},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="userName != null">
                #{userName},
            </if>
            <if test="renewStage != null">
                #{renewStage},
            </if>
            <if test="renewBeforeDate != null">
                #{renewBeforeDate},
            </if>
            <if test="renewAfterDate != null">
                #{renewAfterDate},
            </if>
            <if test="indentNumber != null and indentNumber != ''">
                #{indentNumber},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <update id="updateElectronicRenewCard" parameterType="ElectronicRenewCard">
        update electronic_renew_card
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">
                agent_id = #{agentId},
            </if>
            <if test="exclusiveShopId != null">
                exclusive_shop_id = #{exclusiveShopId},
            </if>
            <if test="buyAgentId != null">
                buy_agent_id = #{buyAgentId},
            </if>
            <if test="cardNumber != null and cardNumber != ''">
                card_number = #{cardNumber},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="renewTimeLen != null">
                renew_time_len = #{renewTimeLen},
            </if>
            <if test="buyDate != null">
                buy_date = #{buyDate},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="money != null">
                money = #{money},
            </if>
            <if test="expireDate != null">
                expire_date = #{expireDate},
            </if>
            <if test="usageTime != null">
                usage_time = #{usageTime},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="renewStage != null">
                renew_stage = #{renewStage},
            </if>
            <if test="renewBeforeDate != null">
                renew_before_date = #{renewBeforeDate},
            </if>
            <if test="renewAfterDate != null">
                renew_after_date = #{renewAfterDate},
            </if>
            <if test="indentNumber != null and indentNumber != ''">
                indent_number = #{indentNumber},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteElectronicRenewCardById" parameterType="Long">
        delete
        from electronic_renew_card
        where id = #{id}
    </delete>

    <delete id="deleteElectronicRenewCardByIds" parameterType="String">
        delete
        from electronic_renew_card where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectElectronicRenewCardCountByCardNumber" resultType="int">
        select count(*)
        from electronic_renew_card
        where card_number = #{cardNumber}
    </select>

    <select id="selectElectronicRenewCardByIds" resultType="com.redbook.system.domain.ElectronicRenewCard">
        select *
        from electronic_renew_card where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectElectronicRenewCardByCardNumber" resultType="com.redbook.system.domain.ElectronicRenewCard">
        select *
        from electronic_renew_card
        where card_number = #{cardNumber}
    </select>

    <update id="refundCard">
        update electronic_renew_card
        set status            = 0
          , user_id           = null
          , user_name         = null
          , renew_stage       = null
          , renew_before_date = null
          , renew_after_date  = null
          , usage_time        = null
        where card_number = #{cardNumber}
    </update>
</mapper>