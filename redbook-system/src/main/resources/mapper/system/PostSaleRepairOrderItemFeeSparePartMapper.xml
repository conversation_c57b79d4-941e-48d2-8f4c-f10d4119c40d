<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemFeeSparePartMapper">
    
    <resultMap type="PostSaleRepairOrderItemFeeSparePart" id="PostSaleRepairOrderItemFeeSparePartResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="feeId"    column="fee_id"    />
        <result property="sparePartId"    column="spare_part_id"    />
        <result property="useNum"    column="use_num"    />
        <result property="sparePartCost"    column="spare_part_cost"    />
        <result property="sparePartSalePrice"    column="spare_part_sale_price"    />
        <result property="sparePartName"    column="spare_part_name"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemFeeSparePartVo">
        select id, fee_id, spare_part_id, use_num, spare_part_cost, spare_part_sale_price from post_sale_repair_order_item_fee_spare_part
    </sql>

    <select id="selectPostSaleRepairOrderItemFeeSparePartList" parameterType="PostSaleRepairOrderItemFeeSparePart" resultMap="PostSaleRepairOrderItemFeeSparePartResult">
        <include refid="selectPostSaleRepairOrderItemFeeSparePartVo"/>
        <where>  
            <if test="feeId != null "> and fee_id = #{feeId}</if>
            <if test="sparePartId != null "> and spare_part_id = #{sparePartId}</if>
            <if test="useNum != null "> and use_num = #{useNum}</if>
            <if test="sparePartCost != null "> and spare_part_cost = #{sparePartCost}</if>
            <if test="sparePartSalePrice != null "> and spare_part_sale_price = #{sparePartSalePrice}</if>
        </where>
    </select>
    
    <select id="selectPostSaleRepairOrderItemFeeSparePartById" parameterType="Long" resultMap="PostSaleRepairOrderItemFeeSparePartResult">
        <include refid="selectPostSaleRepairOrderItemFeeSparePartVo"/>
        where id = #{id}
    </select>
    <select id="selectPostSaleRepairOrderItemFeeSparePartByItemId" resultMap="PostSaleRepairOrderItemFeeSparePartResult">
        select * from post_sale_repair_order_item_fee_spare_part where item_id = #{itemId}
    </select>
    <select id="selectFeeSpartPartByFeeIds" resultMap="PostSaleRepairOrderItemFeeSparePartResult">
        select psroifsp.*,  pssp.spare_part_name from post_sale_repair_order_item_fee_spare_part psroifsp
                 left join post_sale_spare_part pssp on psroifsp.spare_part_id = pssp.id
                 where fee_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertPostSaleRepairOrderItemFeeSparePart" parameterType="PostSaleRepairOrderItemFeeSparePart" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_repair_order_item_fee_spare_part
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="feeId != null">fee_id,</if>
            <if test="sparePartId != null">spare_part_id,</if>
            <if test="useNum != null">use_num,</if>
            <if test="sparePartCost != null">spare_part_cost,</if>
            <if test="sparePartSalePrice != null">spare_part_sale_price,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="feeId != null">#{feeId},</if>
            <if test="sparePartId != null">#{sparePartId},</if>
            <if test="useNum != null">#{useNum},</if>
            <if test="sparePartCost != null">#{sparePartCost},</if>
            <if test="sparePartSalePrice != null">#{sparePartSalePrice},</if>
         </trim>
    </insert>
    <insert id="insertBatch">
        insert into post_sale_repair_order_item_fee_spare_part
        (item_id, fee_id, spare_part_id, use_num, spare_part_cost, spare_part_sale_price)
        values
        <foreach collection="list" item="sparePart" separator=",">
            (#{sparePart.itemId}, #{sparePart.feeId}, #{sparePart.sparePartId}, #{sparePart.useNum}, #{sparePart.sparePartCost}, #{sparePart.sparePartSalePrice})
        </foreach>
    </insert>

    <update id="updatePostSaleRepairOrderItemFeeSparePart" parameterType="PostSaleRepairOrderItemFeeSparePart">
        update post_sale_repair_order_item_fee_spare_part
        <trim prefix="SET" suffixOverrides=",">
            <if test="feeId != null">fee_id = #{feeId},</if>
            <if test="sparePartId != null">spare_part_id = #{sparePartId},</if>
            <if test="useNum != null">use_num = #{useNum},</if>
            <if test="sparePartCost != null">spare_part_cost = #{sparePartCost},</if>
            <if test="sparePartSalePrice != null">spare_part_sale_price = #{sparePartSalePrice},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleRepairOrderItemFeeSparePartById" parameterType="Long">
        delete from post_sale_repair_order_item_fee_spare_part where id = #{id}
    </delete>

    <delete id="deletePostSaleRepairOrderItemFeeSparePartByIds" parameterType="String">
        delete from post_sale_repair_order_item_fee_spare_part where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>