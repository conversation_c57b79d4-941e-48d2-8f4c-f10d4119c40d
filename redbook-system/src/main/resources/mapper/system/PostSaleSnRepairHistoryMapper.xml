<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleSnRepairHistoryMapper">
    
    <resultMap type="PostSaleSnRepairHistory" id="PostSaleRepairHistoryResult">
        <result property="id"    column="id"    />
        <result property="itemId"    column="item_id"    />
        <result property="productSn"    column="product_sn"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectPostSaleRepairHistoryVo">
        select id, item_id, product_sn, create_time from post_sale_sn_repair_history
    </sql>

    <insert id="insertPostSaleRepairHistory" parameterType="PostSaleSnRepairHistory" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_sn_repair_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="productSn != null">product_sn,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="productSn != null">#{productSn},</if>
         </trim>
    </insert>
    <select id="selectRecordListBySn" resultType="com.redbook.system.domain.PostSaleSnRepairHistory">
        select psrh.id,psrh.item_id as itemId, psrh.product_sn as productSn,psroi.rejudgment_result_type as rejudgmentResultType,psrh.create_time as createTime
        from post_sale_sn_repair_history psrh
        left join post_sale_repair_order_item psroi on psrh.item_id = psroi.id
        where psrh.product_sn = #{productSn} order by psrh.id asc
    </select>
    <select id="selectRecordByItemId" resultMap="PostSaleRepairHistoryResult">
        select id from post_sale_sn_repair_history where item_id = #{itemId}
    </select>
    <select id="selectCountBySn" resultType="java.lang.Integer">
        select count(id) from post_sale_sn_repair_history  where product_sn = #{productSn}
    </select>

</mapper>