<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ActivityUserReportMapper">

    <resultMap type="ActivityUserReport" id="ActivityUserReportResult">
        <result property="id" column="id"/>
        <result property="activityBaseId" column="activity_base_id"/>
        <result property="userId" column="user_id"/>
        <result property="userAddr" column="user_addr"/>
        <result property="createTime" column="create_time"/>
        <result property="totalVoteNum" column="total_vote_num"/>
        <result property="alias" column="alias"/>
        <result property="userName" column="user_name"/>
        <result property="stage" column="stage"/>
        <result property="stageDesc" column="stageDesc"/>

        <result property="topAreaName" column="topAreaName"/>
        <result property="studyHour" column="study_hour"/>
        <result property="wordSpeed" column="word_speed"/>
        <result property="masterWord" column="master_word"/>
        <result property="masterSentence" column="master_sentence"/>
        <result property="integral" column="integral"/>
        <result property="studyEfficiency" column="study_efficiency"/>
        <result property="reportScore" column="report_score"/>
        <result property="reportDetailId" column="reportDetailId"/>
    </resultMap>
    <!--1小学/2初中/3高中/4大学/5 出国/11小升初/21 初升高-->
    <sql id="selectActivityUserReportVo">
        select
        acr.id,acr.user_id, acr.user_addr, acr.create_time, acr.total_vote_num,acr.alias, aa.name as topAreaName, acr.user_name,
         CASE acr.stage
		WHEN 1 THEN
		'小学'
		WHEN 2 THEN
		'初中'
		WHEN 3 THEN
		'高中'
		WHEN 4 THEN
		'大学'
		WHEN 5 THEN
		'出国'
		WHEN 11 THEN
		'小升初'
		WHEN 21 THEN
		'初升高'
	    END AS stageDesc,
        acrd.study_hour,acrd.word_speed,acrd.master_word,acrd.master_sentence,acrd.integral,acrd.study_efficiency,acrd.report_score,acrd.id as reportDetailId
         from  ${DB_RED_BOOK_ACTIVITY}.activity_user_report acr
         left join ${DB_RED_BOOK_ACTIVITY}.activity_user_report_detail acrd on acr.id=acrd.report_id
         left join ${DB_RED_BOOK}.user_info u1 on u1.user_id=acr.user_id
         left join agent a on u1.aid = a.aid
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
         left join sys_user u2 ON a.contact_person = u2.user_id
         left join area aa on acr.top_area_id=aa.id
    </sql>

    <select id="selectActivityUserReportList" parameterType="ActivityUserReport" resultMap="ActivityUserReportResult">
        <include refid="selectActivityUserReportVo"/>
        <where>
            <if test="activityBaseId != null ">and acr.activity_base_id = #{activityBaseId}</if>
            <if test="userId != null  and userId != ''">and acr.user_id = #{userId}</if>
            <if test="userAddr != null  and userAddr != ''">and user_addr = #{userAddr}</if>
            <if test="topAreaId != null ">and acr.top_area_id = #{topAreaId}</if>
            <if test="stage != null ">and acr.stage = #{stage}</if>
            <if test="rewardStatus != null ">and acr.reward_status = #{rewardStatus}</if>
            <if test="params!=null and params.createTimeS !=null">and acr.create_time &gt;=
                #{params.createTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.createTimeE !=null">and acr.create_time &lt;=
                #{params.createTimeE}' 23:59:59'
            </if>
            <if test="userName != null and userName != ''">
                and ( acr.user_name like concat('%', #{userName}, '%')
                or u1.user_name like concat('%', #{userName}, '%'))
            </if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
        </where>
        GROUP BY acr.id order by acrd.report_score desc,acrd.word_speed desc,acrd.study_time desc,acrd.id asc
    </select>
</mapper>