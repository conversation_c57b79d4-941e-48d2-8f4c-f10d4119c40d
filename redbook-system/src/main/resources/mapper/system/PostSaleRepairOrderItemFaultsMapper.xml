<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemFaultsMapper">

    <resultMap type="PostSaleRepairOrderItemFaults" id="PostSaleRepairOrderItemFaultsResult">
        <result property="id" column="id"/>
        <result property="itemId" column="item_id"/>
        <result property="faultClassifyId" column="fault_classify_id"/>
        <result property="faultClassifyParentId" column="fault_classify_parent_id"/>
        <result property="classifyName" column="classify_name"/>
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemFaultsVo">
        select id, item_id, fault_classify_id, fault_classify_parent_id
        from post_sale_repair_order_item_faults
    </sql>

    <select id="selectPostSaleRepairOrderItemFaultsByItemIds" resultMap="PostSaleRepairOrderItemFaultsResult">
        select psroif.item_id, psfc.classify_name
        from post_sale_repair_order_item_faults psroif
        left join post_sale_fault_classify psfc on psroif.fault_classify_id=psfc.id
        where item_id in
        <foreach item="itemId" collection="list" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
    <select id="selectPostSaleRepairOrderItemFaultsByItemId" resultMap="PostSaleRepairOrderItemFaultsResult">
        select psroif.item_id, psfc.classify_name
        from post_sale_repair_order_item_faults psroif
                 left join post_sale_fault_classify psfc on psroif.fault_classify_id = psfc.id
        where item_id = #{itemId}
    </select>

    <select id="selectPostSaleRepairOrderItemFaultsNamesByItemId" resultType="java.lang.String">
        select psfc.classify_name
        from post_sale_repair_order_item_faults psroif
                 left join post_sale_fault_classify psfc on psroif.fault_classify_id = psfc.id
        where item_id = #{itemId}
    </select>


    <insert id="insertBatch">
        INSERT INTO post_sale_repair_order_item_faults (item_id, fault_classify_id, fault_classify_parent_id)
        VALUES
        <foreach collection="list" item="fault" separator=",">
            (#{fault.itemId}, #{fault.faultClassifyId}, #{fault.faultClassifyParentId})
        </foreach>
    </insert>

    <delete id="deleteBatch">
        DELETE FROM post_sale_repair_order_item_faults
        WHERE item_id=#{itemId} and fault_classify_id IN
        <foreach collection="list" item="faultClassifyId" open="(" separator="," close=")">
            #{faultClassifyId}
        </foreach>
    </delete>
</mapper>