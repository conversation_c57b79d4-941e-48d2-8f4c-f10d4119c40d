<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.AgentActivityMapper">
    
    <resultMap type="com.redbook.system.domain.AgentActivity" id="AgentActivityResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="aid"    column="aid"    />
        <result property="name"    column="name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="integralDiscount"    column="integral_discount"    />
        <result property="diamondDiscount"    column="diamond_discount"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAgentActivityVo">
        select id, agent_id, aid, name, start_time, end_time, integral_discount, diamond_discount, status, create_time, update_time from agent_activity
    </sql>

    <select id="selectAgentActivityList" resultMap="AgentActivityResult">
        select DISTINCT a.id, a.agent_id, a.aid, a.name, a.start_time, a.end_time, a.integral_discount, a.diamond_discount, a.status, a.create_time, a.update_time,
        ag.name as agentName
        from agent_activity a
        left join agent_activity_shop aas on a.id = aas.activity_id
        LEFT JOIN agent ag ON ag.id = a.agent_id
        <where>  
            <if test="agentId != null "> and a.agent_id = #{agentId}</if>
            <if test="aid != null  and aid != ''"> and a.aid = #{aid}</if>
            <if test="exclusiveShopId != null"> and aas.shop_id = #{exclusiveShopId}</if>
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="startTime != null "> and DATE_FORMAT(a.start_time,'%Y-%m-%d')  &gt;= #{startTime}</if>
            <if test="endTime != null "> and DATE_FORMAT(a.end_time,'%Y-%m-%d') &lt;= #{endTime}</if>
        </where>
    </select>
    
    <select id="selectAgentActivityById" resultMap="AgentActivityResult">
        <include refid="selectAgentActivityVo"/>
        where id = #{id}
    </select>

    <select id="listGoodsList" resultType="map">
        select * from (

            <if test="exclusiveShopId == null">
                select DISTINCT g.*,ga.status as updown, ga.aid, ga.user_id_fk as userIdFk,ga.exclusive_shop_id exclusiveShopId
                from ${DB_RED_BOOK}.goods_agent ga
                left join ${DB_RED_BOOK}.goods g on g.id = ga.goods_id
                where  ga.status != -1 and g.status = 1 and ga.aid = #{aid}
                and g.id not in (
                select goods_id from agent_activity_item
                )

                union
            </if>

            select DISTINCT g.*,ga.status as updown, ga.aid, ga.user_id_fk as userIdFk,ga.exclusive_shop_id exclusiveShopId
            from ${DB_RED_BOOK}.goods_agent ga
            left join ${DB_RED_BOOK}.goods g on g.id = ga.goods_id
            where ga.status != -1  and g.status = 1
            and g.id not in (
            select goods_id from agent_activity_item
            )
            <if test="exclusiveShopId == null">
                and ga.aid = #{aid}
            </if>
            <if test="exclusiveShopId != null">
                and ga.exclusive_shop_id = #{exclusiveShopId}
            </if>
        )t order by t.sort is null asc,t.sort asc
    </select>

    <select id="goodsList" resultType="com.redbook.system.domain.old.GoodsBean">
        select g.*
        from agent_activity_item a
        left join ${DB_RED_BOOK}.goods g on g.id = a.goods_id
        where a.activity_id = #{id}
    </select>

    <select id="countActivityGoodsNum" resultType="int">
        SELECT count(1)
        from  agent_activity_item t
        left join agent_activity a on a.id = t.activity_id
        where a.id = #{id}
    </select>



    <update id="onShelf">
        update agent_activity
        set status = 1
        where id = #{id}
        and aid = #{aid}
    </update>
    <update id="offShelf">
        update agent_activity
        set status = 2
        where id = #{id}
          and aid = #{aid}
    </update>

    <select id="getGoodsAgentName" resultType="com.redbook.system.domain.AgentShopName">
        SELECT DISTINCT a.name as agentName
        from ${DB_RED_BOOK}.goods_agent ga
        left join agent a on a.aid = ga.aid
        where ga.goods_id = #{goodsId} limit 0,1
    </select>
    <select id="getGoodsShopName" resultType="com.redbook.system.domain.AgentShopName">
        SELECT DISTINCT s.name as shopName
        from ${DB_RED_BOOK}.goods_agent ga
        left join exclusive_shop s on s.id = ga.exclusive_shop_id
        where ga.goods_id = #{goodsId} limit 0,1
    </select>

    <select id="countGoodsByActivity" resultType="int">
        select count(*)
        from ${DB_RED_BOOK}.store_order
        where status != 2 and goods_id = #{goodsId}
        <if test="startTime != null "> and create_time  &gt;= #{startTime}</if>
        <if test="endTime != null "> and create_time &lt;= #{endTime}</if>
    </select>
        
    <insert id="insertAgentActivity" parameterType="AgentActivity" useGeneratedKeys="true" keyProperty="id">
        insert into agent_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">agent_id,</if>
            <if test="aid != null">aid,</if>
            <if test="name != null">name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="integralDiscount != null">integral_discount,</if>
            <if test="diamondDiscount != null">diamond_discount,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">#{agentId},</if>
            <if test="aid != null">#{aid},</if>
            <if test="name != null">#{name},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="integralDiscount != null">#{integralDiscount},</if>
            <if test="diamondDiscount != null">#{diamondDiscount},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAgentActivity" parameterType="AgentActivity">
        update agent_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="aid != null">aid = #{aid},</if>
            <if test="name != null">name = #{name},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="integralDiscount != null">integral_discount = #{integralDiscount},</if>
            <if test="diamondDiscount != null">diamond_discount = #{diamondDiscount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAgentActivityById">
        delete from agent_activity where id = #{id}
    </delete>

    <delete id="deleteAgentActivityByIds" parameterType="String">
        delete from agent_activity where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>