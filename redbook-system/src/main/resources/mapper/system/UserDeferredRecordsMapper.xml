<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.UserDeferredRecordsMapper">
    
    <resultMap type="UserDeferredRecords" id="UserDeferredRecordsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="memberType"    column="member_type"    />
        <result property="beforeMemberType"    column="before_member_type"    />
        <result property="stage1ExpirationDate"    column="stage1_expiration_date"    />
        <result property="stage2ExpirationDate"    column="stage2_expiration_date"    />
        <result property="stage3ExpirationDate"    column="stage3_expiration_date"    />

        <result property="stage4ExpirationDate"    column="stage4_expiration_date"    />
        <result property="stage5ExpirationDate"    column="stage5_expiration_date"    />
        <result property="stage11ExpirationDate"    column="stage11_expiration_date"    />
        <result property="stage21ExpirationDate"    column="stage21_expiration_date"    />

        <result property="beforeStage1ExpirationDate"    column="before_stage1_expiration_date"    />
        <result property="beforeStage2ExpirationDate"    column="before_stage2_expiration_date"    />
        <result property="beforeStage3ExpirationDate"    column="before_stage3_expiration_date"    />

        <result property="beforeStage4ExpirationDate"    column="before_stage4_expiration_date"    />
        <result property="beforeStage5ExpirationDate"    column="before_stage5_expiration_date"    />
        <result property="beforeStage11ExpirationDate"    column="before_stage11_expiration_date"    />
        <result property="beforeStage21ExpirationDate"    column="before_stage21_expiration_date"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="beforeExpirationDate"    column="before_expiration_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectUserDeferredRecordsVo">
        select id, user_id, user_name, member_type, before_member_type,
        stage1_expiration_date, stage2_expiration_date, stage3_expiration_date, stage4_expiration_date, stage5_expiration_date, stage11_expiration_date, stage21_expiration_date,
        before_stage1_expiration_date, before_stage2_expiration_date, before_stage3_expiration_date, before_stage4_expiration_date, before_stage5_expiration_date, before_stage11_expiration_date, before_stage21_expiration_date,
        expiration_date, before_expiration_date, create_by, create_time, update_by, update_time, remark from user_deferred_records
    </sql>

    <select id="selectUserDeferredRecordsList" parameterType="UserDeferredRecords" resultMap="UserDeferredRecordsResult">
        <include refid="selectUserDeferredRecordsVo"/>
        <where>  
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="memberType != null "> and member_type = #{memberType}</if>
            <if test="beforeMemberType != null "> and before_member_type = #{beforeMemberType}</if>
            <if test="stage1ExpirationDate != null "> and stage1_expiration_date = #{stage1ExpirationDate}</if>
            <if test="stage2ExpirationDate != null "> and stage2_expiration_date = #{stage2ExpirationDate}</if>
            <if test="stage3ExpirationDate != null "> and stage3_expiration_date = #{stage3ExpirationDate}</if>

            <if test="stage4ExpirationDate != null "> and stage4_expiration_date = #{stage4ExpirationDate}</if>
            <if test="stage5ExpirationDate != null "> and stage5_expiration_date = #{stage5ExpirationDate}</if>
            <if test="stage11ExpirationDate != null "> and stage11_expiration_date = #{stage11ExpirationDate}</if>
            <if test="stage21ExpirationDate != null "> and stage21_expiration_date = #{stage21ExpirationDate}</if>

            <if test="beforeStage1ExpirationDate != null "> and before_stage1_expiration_date = #{beforeStage1ExpirationDate}</if>
            <if test="beforeStage2ExpirationDate != null "> and before_stage2_expiration_date = #{beforeStage2ExpirationDate}</if>
            <if test="beforeStage3ExpirationDate != null "> and before_stage3_expiration_date = #{beforeStage3ExpirationDate}</if>

            <if test="beforeStage4ExpirationDate != null "> and before_stage4_expiration_date = #{beforeStage4ExpirationDate}</if>
            <if test="beforeStage5ExpirationDate != null "> and before_stage5_expiration_date = #{beforeStage5ExpirationDate}</if>
            <if test="beforeStage11ExpirationDate != null "> and before_stage11_expiration_date = #{beforeStage11ExpirationDate}</if>
            <if test="beforeStage21ExpirationDate != null "> and before_stage21_expiration_date = #{beforeStage21ExpirationDate}</if>
            <if test="expirationDate != null "> and expiration_date = #{expirationDate}</if>
            <if test="beforeExpirationDate != null "> and before_expiration_date = #{beforeExpirationDate}</if>
        </where>
    </select>
    
    <select id="selectUserDeferredRecordsById" parameterType="Long" resultMap="UserDeferredRecordsResult">
        <include refid="selectUserDeferredRecordsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserDeferredRecords" parameterType="UserDeferredRecords" useGeneratedKeys="true" keyProperty="id">
        insert into user_deferred_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="memberType != null">member_type,</if>
            <if test="beforeMemberType != null">before_member_type,</if>
            <if test="stage1ExpirationDate != null">stage1_expiration_date,</if>
            <if test="stage2ExpirationDate != null">stage2_expiration_date,</if>
            <if test="stage3ExpirationDate != null">stage3_expiration_date,</if>

            <if test="stage4ExpirationDate != null">stage4_expiration_date,</if>
            <if test="stage5ExpirationDate != null">stage5_expiration_date,</if>
            <if test="stage11ExpirationDate != null">stage11_expiration_date,</if>
            <if test="stage21ExpirationDate != null">stage21_expiration_date,</if>

            <if test="beforeStage1ExpirationDate != null">before_stage1_expiration_date,</if>
            <if test="beforeStage2ExpirationDate != null">before_stage2_expiration_date,</if>
            <if test="beforeStage3ExpirationDate != null">before_stage3_expiration_date,</if>

            <if test="beforeStage4ExpirationDate != null">before_stage4_expiration_date,</if>
            <if test="beforeStage5ExpirationDate != null">before_stage5_expiration_date,</if>
            <if test="beforeStage11ExpirationDate != null">before_stage11_expiration_date,</if>
            <if test="beforeStage21ExpirationDate != null">before_stage21_expiration_date,</if>

            <if test="expirationDate != null">expiration_date,</if>
            <if test="beforeExpirationDate != null">before_expiration_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="memberType != null">#{memberType},</if>
            <if test="beforeMemberType != null">#{beforeMemberType},</if>
            <if test="stage1ExpirationDate != null">#{stage1ExpirationDate},</if>
            <if test="stage2ExpirationDate != null">#{stage2ExpirationDate},</if>
            <if test="stage3ExpirationDate != null">#{stage3ExpirationDate},</if>

            <if test="stage4ExpirationDate != null">#{stage4ExpirationDate},</if>
            <if test="stage5ExpirationDate != null">#{stage5ExpirationDate},</if>
            <if test="stage11ExpirationDate != null">#{stage11ExpirationDate},</if>
            <if test="stage21ExpirationDate != null">#{stage21ExpirationDate},</if>

            <if test="beforeStage1ExpirationDate != null">#{beforeStage1ExpirationDate},</if>
            <if test="beforeStage2ExpirationDate != null">#{beforeStage2ExpirationDate},</if>
            <if test="beforeStage3ExpirationDate != null">#{beforeStage3ExpirationDate},</if>

            <if test="beforeStage4ExpirationDate != null">#{beforeStage4ExpirationDate},</if>
            <if test="beforeStage5ExpirationDate != null">#{beforeStage5ExpirationDate},</if>
            <if test="beforeStage11ExpirationDate != null">#{beforeStage11ExpirationDate},</if>
            <if test="beforeStage21ExpirationDate != null">#{beforeStage21ExpirationDate},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="beforeExpirationDate != null">#{beforeExpirationDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateUserDeferredRecords" parameterType="UserDeferredRecords">
        update user_deferred_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="memberType != null">member_type = #{memberType},</if>
            <if test="beforeMemberType != null">before_member_type = #{beforeMemberType},</if>
            <if test="stage1ExpirationDate != null">stage1_expiration_date = #{stage1ExpirationDate},</if>
            <if test="stage2ExpirationDate != null">stage2_expiration_date = #{stage2ExpirationDate},</if>
            <if test="stage3ExpirationDate != null">stage3_expiration_date = #{stage3ExpirationDate},</if>

            <if test="stage4ExpirationDate != null">stage4_expiration_date = #{stage4ExpirationDate},</if>
            <if test="stage5ExpirationDate != null">stage5_expiration_date = #{stage5ExpirationDate},</if>
            <if test="stage11ExpirationDate != null">stage11_expiration_date = #{stage11ExpirationDate},</if>
            <if test="stage21ExpirationDate != null">stage21_expiration_date = #{stage21ExpirationDate},</if>

            <if test="beforeStage1ExpirationDate != null">before_stage1_expiration_date = #{beforeStage1ExpirationDate},</if>
            <if test="beforeStage2ExpirationDate != null">before_stage2_expiration_date = #{beforeStage2ExpirationDate},</if>
            <if test="beforeStage3ExpirationDate != null">before_stage3_expiration_date = #{beforeStage3ExpirationDate},</if>

            <if test="beforeStage4ExpirationDate != null">before_stage4_expiration_date = #{beforeStage4ExpirationDate},</if>
            <if test="beforeStage5ExpirationDate != null">before_stage5_expiration_date = #{beforeStage5ExpirationDate},</if>
            <if test="beforeStage11ExpirationDate != null">before_stage11_expiration_date = #{beforeStage11ExpirationDate},</if>
            <if test="beforeStage21ExpirationDate != null">before_stage21_expiration_date = #{beforeStage21ExpirationDate},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="beforeExpirationDate != null">before_expiration_date = #{beforeExpirationDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserDeferredRecordsById" parameterType="Long">
        delete from user_deferred_records where id = #{id}
    </delete>

    <delete id="deleteUserDeferredRecordsByIds" parameterType="String">
        delete from user_deferred_records where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>