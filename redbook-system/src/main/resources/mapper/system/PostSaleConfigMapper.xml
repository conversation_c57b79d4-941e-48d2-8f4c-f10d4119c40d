<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleConfigMapper">
    
    <resultMap type="PostSaleConfig" id="PostSaleConfigResult">
        <result property="id"    column="id"    />
        <result property="configType"    column="config_type"    />
        <result property="configTypeCn"    column="config_type_cn"    />
        <result property="configValue"    column="config_value"    />
        <result property="configValueCn"    column="config_value_cn"    />
    </resultMap>

    <sql id="selectPostSaleConfigVo">
        select id, config_type, config_type_cn, config_value, config_value_cn from post_sale_config
    </sql>

    <select id="selectPostSaleConfigList" parameterType="PostSaleConfig" resultMap="PostSaleConfigResult">
        <include refid="selectPostSaleConfigVo"/>
        <where>  
            <if test="configType != null  and configType.toString() != ''"> and config_type = #{configType}</if>
            <if test="configTypeCn != null  and configTypeCn != ''"> and config_type_cn = #{configTypeCn}</if>
            <if test="configValue != null  and configValue != ''"> and config_value = #{configValue}</if>
            <if test="configValueCn != null  and configValueCn != ''"> and config_value_cn = #{configValueCn}</if>
        </where>
    </select>
    
    <select id="selectPostSaleConfigById" parameterType="Long" resultMap="PostSaleConfigResult">
        <include refid="selectPostSaleConfigVo"/>
        where id = #{id}
    </select>
    <select id="selectPostSaleConfigByType" resultType="com.redbook.system.domain.PostSaleConfig">
        select * from post_sale_config where config_type = #{type}
    </select>
    <select id="selectPostSaleConfigListFromApplet" resultMap="PostSaleConfigResult">
        select * from post_sale_config
    </select>

    <insert id="insertPostSaleConfig" parameterType="PostSaleConfig">
        insert into post_sale_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="configType != null">config_type,</if>
            <if test="configTypeCn != null">config_type_cn,</if>
            <if test="configValue != null">config_value,</if>
            <if test="configValueCn != null">config_value_cn,</if>
            <if test="isUnique != null">is_unique,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="configType != null">#{configType},</if>
            <if test="configTypeCn != null">#{configTypeCn},</if>
            <if test="configValue != null">#{configValue},</if>
            <if test="configValueCn != null">#{configValueCn},</if>
            <if test="isUnique != null">#{isUnique},</if>
         </trim>
    </insert>

    <update id="updatePostSaleConfig" parameterType="PostSaleConfig">
        update post_sale_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configType != null">config_type = #{configType},</if>
            <if test="configTypeCn != null">config_type_cn = #{configTypeCn},</if>
            <if test="configValue != null">config_value = #{configValue},</if>
            <if test="configValueCn != null">config_value_cn = #{configValueCn},</if>
            <if test="isUnique != null">is_unique = #{isUnique},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleConfigById" parameterType="Long">
        delete from post_sale_config where id = #{id}
    </delete>

    <delete id="deletePostSaleConfigByIds" parameterType="String">
        delete from post_sale_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>