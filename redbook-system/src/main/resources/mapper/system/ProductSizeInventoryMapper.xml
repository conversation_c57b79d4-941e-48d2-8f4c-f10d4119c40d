<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ProductSizeInventoryMapper">
    
    <resultMap type="ProductSizeInventory" id="ProductSizeInventoryResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="size"    column="size"    />
        <result property="inventory"    column="inventory"    />
    </resultMap>

    <sql id="selectProductSizeInventoryVo">
        select id, product_id, size, inventory from product_size_inventory
    </sql>

    <select id="selectProductSizeInventoryList" parameterType="ProductSizeInventory" resultMap="ProductSizeInventoryResult">
        <include refid="selectProductSizeInventoryVo"/>
        <where>  
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="size != null  and size != ''"> and size = #{size}</if>
            <if test="inventory != null "> and inventory = #{inventory}</if>
        </where>
    </select>
    
    <select id="selectProductSizeInventoryById" parameterType="Integer" resultMap="ProductSizeInventoryResult">
        <include refid="selectProductSizeInventoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertProductSizeInventory" parameterType="ProductSizeInventory" useGeneratedKeys="true" keyProperty="id">
        insert into product_size_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="size != null and size != ''">size,</if>
            <if test="inventory != null">inventory,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="size != null and size != ''">#{size},</if>
            <if test="inventory != null">#{inventory},</if>
         </trim>
    </insert>

    <update id="updateProductSizeInventory" parameterType="ProductSizeInventory">
        update product_size_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="size != null and size != ''">size = #{size},</if>
            <if test="inventory != null">inventory = #{inventory},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductSizeInventoryById" parameterType="Integer">
        delete from product_size_inventory where id = #{id}
    </delete>

    <delete id="deleteProductSizeInventoryByIds" parameterType="String">
        delete from product_size_inventory where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="addInventory">
        update product_size_inventory set inventory = inventory + #{inventory} where id = #{productSizeInventoryId}
    </update>
    <update id="updateProductSizeInventoryCount">
           update product_size_inventory set inventory = inventory - #{orderCount} where id = #{id}
    </update>

    <update id="addInventoryByProductId">
    update product_size_inventory set inventory = inventory + #{inventory} where product_id = #{productId}
    </update>
    <update id="updateProductSizeInventoryCountByProductId">
        update product_size_inventory set inventory = inventory - #{inventory} where product_id = #{productId}
    </update>


</mapper>