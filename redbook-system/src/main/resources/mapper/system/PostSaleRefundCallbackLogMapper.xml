<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRefundCallbackLogMapper">
    
    <resultMap type="PostSaleRefundCallbackLog" id="PostSaleRefundCallbackLogResult">
        <result property="id"    column="id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="refundId"    column="refund_id"    />
        <result property="notifyContent"    column="notify_content"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectPostSaleRefundCallbackLogVo">
        select id, order_no, refund_id, notify_content, status, create_time from post_sale_refund_callback_log
    </sql>

    <select id="selectPostSaleRefundCallbackLogList" parameterType="PostSaleRefundCallbackLog" resultMap="PostSaleRefundCallbackLogResult">
        <include refid="selectPostSaleRefundCallbackLogVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="refundId != null  and refundId != ''"> and refund_id = #{refundId}</if>
            <if test="notifyContent != null  and notifyContent != ''"> and notify_content = #{notifyContent}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectPostSaleRefundCallbackLogById"  resultMap="PostSaleRefundCallbackLogResult">
        <include refid="selectPostSaleRefundCallbackLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleRefundCallbackLog" parameterType="PostSaleRefundCallbackLog" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_refund_callback_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no,</if>
            <if test="refundId != null">refund_id,</if>
            <if test="notifyContent != null">notify_content,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="refundId != null">#{refundId},</if>
            <if test="notifyContent != null">#{notifyContent},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updatePostSaleRefundCallbackLog" parameterType="PostSaleRefundCallbackLog">
        update post_sale_refund_callback_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="refundId != null">refund_id = #{refundId},</if>
            <if test="notifyContent != null">notify_content = #{notifyContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleRefundCallbackLogById" >
        delete from post_sale_refund_callback_log where id = #{id}
    </delete>

    <delete id="deletePostSaleRefundCallbackLogByIds" parameterType="String">
        delete from post_sale_refund_callback_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>