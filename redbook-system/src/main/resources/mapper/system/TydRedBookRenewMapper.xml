<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ITydRenewCardDao">

    <update id="renewRedBookUser">
        UPDATE ${DB_RED_BOOK}.user_info SET
            member_type=#{memberType},
            expiration_date=#{expirationDate},
            stage1_expiration_date=#{stage1ExpirationDate},
            stage2_expiration_date=#{stage2ExpirationDate},
            stage3_expiration_date=#{stage3ExpirationDate},
            stage4_expiration_date=#{stage4ExpirationDate},
            stage5_expiration_date=#{stage5ExpirationDate},
            stage11_expiration_date=#{stage11ExpirationDate},
            stage21_expiration_date=#{stage21ExpirationDate}
            <if test="firstPurchaseDate != null">
                ,first_purchase_date = #{firstPurchaseDate}
            </if>
            <if test="lastPurchaseDate != null">
                ,last_purchase_date = #{lastPurchaseDate}
            </if>
        WHERE user_id=#{userId} and is_delete=0
    </update>
    <update id="renewRedBookUserBack">
        UPDATE ${DB_RED_BOOK}.user_info SET
        member_type=#{memberType},
        stage=#{stage},
        expiration_date=#{expirationDate},
        stage1_expiration_date=#{stage1ExpirationDate},
        stage2_expiration_date=#{stage2ExpirationDate},
        stage3_expiration_date=#{stage3ExpirationDate},
        stage4_expiration_date=#{stage4ExpirationDate},
        stage5_expiration_date=#{stage5ExpirationDate},
        stage11_expiration_date=#{stage11ExpirationDate},
        stage21_expiration_date=#{stage21ExpirationDate},
        first_purchase_date = #{firstPurchaseDate},
        last_purchase_date = #{lastPurchaseDate}
        WHERE user_id=#{userId} and is_delete=0
    </update>
    <update id="resetRedBookUserMainCourseInfo">
        UPDATE ${DB_RED_BOOK}.user_info SET
            stage=#{stage},main_version_id=-1,main_course_id=-1,now_grade=-1
        WHERE user_id=#{userId}
    </update>


    <update id="updateRedBookUserAgentId">
		UPDATE ${DB_RED_BOOK}.user_info
		SET
			aid = #{aid},
			class_id=-1,
			teach_id=null
		WHERE
			user_id = #{userId} and is_delete=0
	</update>
    <update id="updateRedBookUserAid">
		UPDATE ${DB_RED_BOOK}.user_info
		SET
			aid = #{aid}
		WHERE
			user_id = #{userId} and is_delete=0
	</update>

    
</mapper>