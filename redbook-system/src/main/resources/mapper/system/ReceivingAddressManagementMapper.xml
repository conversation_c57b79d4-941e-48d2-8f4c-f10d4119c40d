<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.IReceivingAddressManagementDao">

    <select id="getAddressList" resultType="map">
        SELECT *
        FROM `receiving_address_management`
        WHERE
        <if test="agentId != null">
          and  agent_id in
            <foreach collection="agentId" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            or user_id=#{uid}
        </if>
        <if test="agentId == null">
            user_id=#{uid}
        </if>
    </select>

    <insert id="insertReceivingAddress">
        <choose>
            <when test="aid!=null and aid=''">
                INSERT
                `receiving_address_management`
                (agent_id, `name`, phone, address, `time`,user_id)
                VALUES (
                #{agentId},
                #{name},
                #{phone},
                #{address},
                #{time},
                #{uid}
                )
            </when>
            <otherwise>
                INSERT
                `receiving_address_management`
                (agent_id, `name`, phone, address, `time`, aid)
                VALUES (
                #{agentId},
                #{name},
                #{phone},
                #{address},
                #{time},
                #{aid}
                )
            </otherwise>
        </choose>

    </insert>

    <select id="getAddressById" resultType="map">
        SELECT *
        FROM `receiving_address_management`
        WHERE id = #{id};
    </select>

    <update id="updateReceivingAddress">
        UPDATE `receiving_address_management`
        SET `name` =#{name},
            phone=#{phone},
            address=#{address},
            `time`=#{time}
        WHERE id = #{id}
    </update>

    <delete id="deleteReceivingAddress">
        DELETE
        FROM `receiving_address_management`
        WHERE id = #{id}
    </delete>

    <select id="getAddressByAgentId" resultType="map">
        SELECT *
        FROM `receiving_address_management`
        WHERE agent_id
        <foreach collection="agentIds" separator="," item="e" open="in (" close=")">
            #{e}
        </foreach>
    </select>
</mapper>