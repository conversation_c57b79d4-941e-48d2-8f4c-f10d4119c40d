<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.AccountApplyApprovalMapper">

    <resultMap type="AccountApplyApproval" id="AccountApplyApprovalResult">
        <result property="id"    column="id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="stage"    column="stage"    />
        <result property="accountType"    column="account_type"    />
        <result property="applyCount"    column="apply_count"    />
        <result property="enableCount"    column="enable_count"    />
        <result property="latestActivationDate"    column="latest_activation_date"    />
        <result property="availableTimeNum"    column="available_time_num"    />
        <result property="availableTimeType"    column="available_time_type"    />
        <result property="recordNo"    column="record_no"    />
        <result property="remark"    column="remark"    />
        <result property="approvalId"    column="approval_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="agentName"    column="agent_name"    />
    </resultMap>

    <sql id="selectAccountApplyApprovalVo">
        select aaa.id, aaa.agent_id,a.name agent_name, aaa.stage, aaa.account_type, aaa.apply_count, aaa.enable_count, aaa.latest_activation_date,
         aaa.available_time_num, aaa.available_time_type,aaa.record_no, aaa.remark, aaa.approval_id, aaa.create_time
         from account_apply_approval aaa
         LEFT JOIN agent a ON a.id = aaa.agent_id
         <!--LEFT JOIN sys_user u2 ON a.contact_person = u2.user_id
         LEFT JOIN sys_user u3 ON aaa.first_approver_id = u3.user_id
         LEFT JOIN sys_user u4 ON aaa.final_approver_id = u4.user_id
         LEFT JOIN sys_user u5 ON aaa.create_by = u5.user_id-->
    </sql>

    <select id="selectAccountApplyApprovalList" parameterType="AccountApplyApproval" resultMap="AccountApplyApprovalResult">
        <include refid="selectAccountApplyApprovalVo"/>
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        <where>
            <if test="agentId != null "> and aaa.agent_id = #{agentId}</if>
            <if test="stage != null "> and aaa.stage = #{stage}</if>
            <if test="accountType != null"> and aaa.account_type = #{accountType}</if>
            <if test="applyCount != null "> and aaa.apply_count = #{applyCount}</if>
            <if test="enableCount != null "> and aaa.enable_count = #{enableCount}</if>
            <if test="latestActivationDate != null "> and aaa.latest_activation_date = #{latestActivationDate}</if>
            <if test="availableTimeNum != null "> and aaa.available_time_num = #{availableTimeNum}</if>
            <if test="availableTimeType != null  and availableTimeType != ''"> and aaa.available_time_type = #{availableTimeType}</if>
            <if test="recordNo != null  and recordNo != ''"> and aaa.record_no like concat('%', #{recordNo}, '%') </if>
            <if test="remark != null  and remark != ''"> and aaa.remark like concat('%', #{remark}, '%')</if>
            <if test="approvalId != null "> and aaa.approval_id = #{approvalId}</if>
            <if test="mId != null and agentIdList.size==0"> and am.user_id = #{mId} </if>
            <if test="params!=null and params.createTimeS !=null">and aaa.create_time &gt;= #{params.createTimeS}' 00:00:00' </if>
            <if test="params!=null and params.createTimeE !=null">and aaa.create_time &lt;= #{params.createTimeE}' 23:59:59' </if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
        </where>
        order by aaa.id desc
    </select>

    <select id="selectAccountApplyApprovalById" parameterType="Long" resultMap="AccountApplyApprovalResult">
        <include refid="selectAccountApplyApprovalVo"/>
        where aaa.id = #{id}
    </select>
    <select id="selectRecordNoList" resultType="java.lang.String">
        select record_no from account_apply_approval
    </select>
    <select id="selectCountByRedocrdNo" resultType="java.lang.Integer">
        select count(1) as count  from account_apply_approval where record_no=#{recordNo}
    </select>

    <insert id="insertAccountApplyApproval" parameterType="AccountApplyApproval">
        insert into account_apply_approval
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="agentId != null">
                agent_id,
            </if>
            <if test="stage != null">
                stage,
            </if>
            <if test="accountType != null">
                account_type,
            </if>
            <if test="applyCount != null">
                apply_count,
            </if>
            <if test="enableCount != null">
                enable_count,
            </if>
            <if test="latestActivationDate != null">
                latest_activation_date,
            </if>
            <if test="availableTimeNum != null">
                available_time_num,
            </if>
            <if test="availableTimeType != null">
                available_time_type,
            </if>
            <if test="recordNo != null">
                record_no,
            </if>
            <if test="approvalId != null">
                approval_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="remark != null">
                remark,
            </if>

            <if test="firstApproverId != null">
                first_approver_id,
            </if>
            <if test="finalApproverId != null">
                final_approver_id,
            </if>
            <if test="createById != null">
                create_by,
            </if>
            <if test="extra != null">
                extra,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="agentId != null">
                #{agentId},
            </if>
            <if test="stage != null">
                #{stage},
            </if>
            <if test="accountType != null">
                #{accountType},
            </if>
            <if test="applyCount != null">
                #{applyCount},
            </if>
            <if test="enableCount != null">
                #{enableCount},
            </if>
            <if test="latestActivationDate != null">
                #{latestActivationDate},
            </if>
            <if test="availableTimeNum != null">
                #{availableTimeNum},
            </if>
            <if test="availableTimeType != null">
                #{availableTimeType},
            </if>
            <if test="recordNo != null">
                #{recordNo},
            </if>
            <if test="approvalId != null">
                #{approvalId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>

            <if test="firstApproverId != null">
                #{firstApproverId},
            </if>
            <if test="finalApproverId != null">
                #{finalApproverId},
            </if>
            <if test="createById != null">
                #{createById},
            </if>
            <if test="extra != null">
                #{extra},
            </if>
        </trim>
    </insert>

    <update id="updateAccountApplyApproval" parameterType="AccountApplyApproval">
        update account_apply_approval
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="stage != null">stage = #{stage},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="applyCount != null">apply_count = #{applyCount},</if>
            <if test="enableCount != null">enable_count = #{enableCount},</if>
            <if test="latestActivationDate != null">latest_activation_date = #{latestActivationDate},</if>
            <if test="availableTimeNum != null">available_time_num = #{availableTimeNum},</if>
            <if test="availableTimeType != null">available_time_type = #{availableTimeType},</if>
            <if test="recordNo != null">record_no = #{recordNo},</if>
            <if test="remark != null">apply_reason = #{remark},</if>
            <if test="approvalId != null">approval_id = #{approvalId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAccountApplyApprovalById" parameterType="Long">
        delete from account_apply_approval where id = #{id}
    </delete>

    <delete id="deleteAccountApplyApprovalByIds" parameterType="String">
        delete from account_apply_approval where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAccountApplyApprovalByRecordNo" resultType="com.redbook.system.domain.AccountApplyApproval">
        <include refid="selectAccountApplyApprovalVo"/>
        where aaa.record_no = #{recordNo}
    </select>
</mapper>