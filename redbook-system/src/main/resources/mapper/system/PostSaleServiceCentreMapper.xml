<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleServiceCentreMapper">
    
    <resultMap type="PostSaleServiceCentre" id="PostSaleServiceCentreResult">
        <result property="id"    column="id"    />
        <result property="serviceCentreName"    column="service_centre_name"    />
        <result property="isFactory"    column="is_factory"    />
        <result property="contactUserName"    column="contact_user_name"    />
        <result property="contactUserPhone"    column="contact_user_phone"    />
        <result property="serviceCentreAddress"    column="service_centre_address"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectPostSaleServiceCentreVo">
        select id, service_centre_name, is_factory, contact_user_name, contact_user_phone, service_centre_address, create_time, create_by, update_time, update_by, is_delete from post_sale_service_centre
    </sql>

    <select id="selectPostSaleServiceCentreList" parameterType="PostSaleServiceCentre" resultMap="PostSaleServiceCentreResult">
        <include refid="selectPostSaleServiceCentreVo"/>
        <where>
            is_delete=0
            <if test="serviceCentreName != null  and serviceCentreName != ''"> and service_centre_name like concat('%', #{serviceCentreName}, '%')</if>
            <if test="isFactory != null "> and is_factory = #{isFactory}</if>
            <if test="contactUserName != null  and contactUserName != ''"> and contact_user_name like concat('%', #{contactUserName}, '%')</if>
            <if test="contactUserPhone != null  and contactUserPhone != ''"> and contact_user_phone = #{contactUserPhone}</if>
            <if test="serviceCentreAddress != null  and serviceCentreAddress != ''"> and service_centre_address = #{serviceCentreAddress}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="updateBy != null "> and update_by = #{updateBy}</if>
        </where>
    </select>
    
    <select id="selectPostSaleServiceCentreById" parameterType="Long" resultMap="PostSaleServiceCentreResult">
        <include refid="selectPostSaleServiceCentreVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleServiceCentre" parameterType="PostSaleServiceCentre" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_service_centre
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceCentreName != null and serviceCentreName != ''">service_centre_name,</if>
            <if test="isFactory != null">is_factory,</if>
            <if test="contactUserName != null">contact_user_name,</if>
            <if test="contactUserPhone != null">contact_user_phone,</if>
            <if test="serviceCentreAddress != null">service_centre_address,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceCentreName != null and serviceCentreName != ''">#{serviceCentreName},</if>
            <if test="isFactory != null">#{isFactory},</if>
            <if test="contactUserName != null">#{contactUserName},</if>
            <if test="contactUserPhone != null">#{contactUserPhone},</if>
            <if test="serviceCentreAddress != null">#{serviceCentreAddress},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updatePostSaleServiceCentre" parameterType="PostSaleServiceCentre">
        update post_sale_service_centre
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceCentreName != null and serviceCentreName != ''">service_centre_name = #{serviceCentreName},</if>
            <if test="isFactory != null">is_factory = #{isFactory},</if>
            <if test="contactUserName != null">contact_user_name = #{contactUserName},</if>
            <if test="contactUserPhone != null">contact_user_phone = #{contactUserPhone},</if>
            <if test="serviceCentreAddress != null">service_centre_address = #{serviceCentreAddress},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleServiceCentreById" parameterType="Long">
        update post_sale_service_centre set is_delete=1 where id = #{id}
    </delete>

    <delete id="deletePostSaleServiceCentreByIds" parameterType="String">
        update post_sale_service_centre set is_delete=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>