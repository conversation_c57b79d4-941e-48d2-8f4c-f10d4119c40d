<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleRepairOrderItemSignMapper">
    
    <resultMap type="PostSaleRepairOrderItemSign" id="PostSaleRepairOrderItemSignResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="deviceType"    column="device_type"    />
        <result property="productTypes"    column="product_types"    />
        <result property="batchId"    column="batch_id"    />
        <result property="modelId"    column="model_id"    />
        <result property="productSn"    column="product_sn"    />
        <result property="activationDate"    column="activation_date"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="model2Id"    column="model2_id"    />
        <result property="productSn2"    column="product_sn2"    />
        <result property="productOtherText"    column="product_other_text"    />
    </resultMap>

    <sql id="selectPostSaleRepairOrderItemSignVo">
        select id, order_id, item_id, device_type, product_types,batch_id, model_id, product_sn, activation_date, expiration_date, model2_id, product_sn2, product_other_text from post_sale_repair_order_item_sign
    </sql>

    <select id="selectPostSaleRepairOrderItemSignList" parameterType="PostSaleRepairOrderItemSign" resultMap="PostSaleRepairOrderItemSignResult">
        <include refid="selectPostSaleRepairOrderItemSignVo"/>
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="deviceType != null "> and device_type = #{deviceType}</if>
            <if test="productTypes != null  and productTypes != ''"> and product_types = #{productTypes}</if>
            <if test="batchId != null "> and batch_id = #{batchId}</if>
            <if test="modelId != null "> and model_id = #{modelId}</if>
            <if test="productSn != null "> and product_sn = #{productSn}</if>
            <if test="activationDate != null "> and activation_date = #{activationDate}</if>
            <if test="expirationDate != null "> and expiration_date = #{expirationDate}</if>
            <if test="model2Id != null  and model2Id != ''"> and model2_id = #{model2Id}</if>
            <if test="productSn2 != null "> and product_sn2 = #{productSn2}</if>
            <if test="productOtherText != null  and productOtherText != ''"> and product_other_text = #{productOtherText}</if>
        </where>
    </select>
    
    <select id="selectPostSaleRepairOrderItemSignById" parameterType="Long" resultMap="PostSaleRepairOrderItemSignResult">
        <include refid="selectPostSaleRepairOrderItemSignVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleRepairOrderItemSign" parameterType="PostSaleRepairOrderItemSign" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_repair_order_item_sign
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="itemId != null">item_id,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="productTypes != null">product_types,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="productSn != null">product_sn,</if>
            <if test="activationDate != null">activation_date,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="model2Id != null">model2_id,</if>
            <if test="productSn2 != null">product_sn2,</if>
            <if test="productOtherText != null">product_other_text,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="itemId != null">#{itemId},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="productTypes != null">#{productTypes},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="productSn != null">#{productSn},</if>
            <if test="activationDate != null">#{activationDate},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="model2Id != null">#{model2Id},</if>
            <if test="productSn2 != null">#{productSn2},</if>
            <if test="productOtherText != null">#{productOtherText},</if>
         </trim>
    </insert>

    <update id="updatePostSaleRepairOrderItemSign" parameterType="PostSaleRepairOrderItemSign">
        update post_sale_repair_order_item_sign
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="productTypes != null">product_types = #{productTypes},</if>
            <if test="batchId != null">batch_id = #{batchId},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="productSn != null">product_sn = #{productSn},</if>
            <if test="activationDate != null">activation_date = #{activationDate},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="model2Id != null">model2_id = #{model2Id},</if>
            <if test="productSn2 != null">product_sn2 = #{productSn2},</if>
            <if test="productOtherText != null">product_other_text = #{productOtherText},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleRepairOrderItemSignById" parameterType="Long">
        delete from post_sale_repair_order_item_sign where id = #{id}
    </delete>

    <delete id="deletePostSaleRepairOrderItemSignByIds" parameterType="String">
        delete from post_sale_repair_order_item_sign where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>