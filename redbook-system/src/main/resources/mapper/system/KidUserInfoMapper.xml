<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.KidUserInfoMapper">
    
    <resultMap type="KidUserInfo" id="KidUserInfoResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="password"    column="password"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="aid"    column="aid"    />
        <result property="exclusiveShopId"    column="exclusive_shop_id"    />
        <result property="teachId"    column="teach_id"    />
        <result property="classId"    column="class_id"    />
        <result property="tableSuffix"    column="table_suffix"    />
        <result property="registerDate"    column="register_date"    />
        <result property="firstPurchaseDate"    column="first_purchase_date"    />
        <result property="lastPurchaseDate"    column="last_purchase_date"    />
        <result property="expirationDate"    column="expiration_date"    />
        <result property="memberType"    column="member_type"    />
        <result property="lollipop"    column="lollipop"    />
        <result property="headImageUrl"    column="head_image_url"    />
        <result property="headPendantImg"    column="head_pendant_img"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="sex"    column="sex"    />
        <result property="birthday"    column="birthday"    />
        <result property="email"    column="email"    />
        <result property="qq"    column="qq"    />
        <result property="signature"    column="signature"    />
        <result property="school"    column="school"    />
        <result property="gradeClass"    column="grade_class"    />
        <result property="address"    column="address"    />
        <result property="payPwd"    column="pay_pwd"    />
        <result property="totalSignCount"    column="total_sign_count"    />
        <result property="init"    column="init"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectKidUserInfoVo">
        select id, user_id, password, mobile_phone, aid, exclusive_shop_id, teach_id, class_id, table_suffix, register_date, first_purchase_date, last_purchase_date, expiration_date, member_type, lollipop, head_image_url, head_pendant_img, user_name, nick_name, sex, birthday, email, qq, signature, school, grade_class, address, pay_pwd, total_sign_count, init, is_delete from user_info ui
    </sql>

    <select id="selectKidUserInfoList" parameterType="KidUserInfo" resultMap="KidUserInfoResult">
        <include refid="selectKidUserInfoVo"/>
        <where>  
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="aid != null  and aid != ''"> and aid = #{aid}</if>
            <if test="exclusiveShopId != null "> and exclusive_shop_id = #{exclusiveShopId}</if>
            <if test="teachId != null  and teachId != ''"> and teach_id = #{teachId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="tableSuffix != null  and tableSuffix != ''"> and table_suffix = #{tableSuffix}</if>
            <if test="registerDate != null "> and register_date = #{registerDate}</if>
            <if test="firstPurchaseDate != null "> and first_purchase_date = #{firstPurchaseDate}</if>
            <if test="lastPurchaseDate != null "> and last_purchase_date = #{lastPurchaseDate}</if>
            <if test="expirationDate != null "> and expiration_date = #{expirationDate}</if>
            <if test="memberType != null "> and member_type = #{memberType}</if>
            <if test="lollipop != null "> and lollipop = #{lollipop}</if>
            <if test="headImageUrl != null  and headImageUrl != ''"> and head_image_url = #{headImageUrl}</if>
            <if test="headPendantImg != null  and headPendantImg != ''"> and head_pendant_img = #{headPendantImg}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="sex != null "> and sex = #{sex}</if>
            <if test="birthday != null "> and birthday = #{birthday}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="qq != null  and qq != ''"> and qq = #{qq}</if>
            <if test="signature != null  and signature != ''"> and signature = #{signature}</if>
            <if test="school != null  and school != ''"> and school = #{school}</if>
            <if test="gradeClass != null  and gradeClass != ''"> and grade_class = #{gradeClass}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="payPwd != null  and payPwd != ''"> and pay_pwd = #{payPwd}</if>
            <if test="totalSignCount != null "> and total_sign_count = #{totalSignCount}</if>
            <if test="init != null "> and init = #{init}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>
    <select id="selectUserInfoList" parameterType="KidUserInfo" resultMap="KidUserInfoResult">
        <include refid="selectKidUserInfoVo"/>
        <where>
            <if test="userId != null  and userId != ''">
                and ui.user_id = #{userId}
            </if>
            <if test="password != null  and password != ''">
                and ui.password = #{password}
            </if>
            <if test="mobilePhone != null  and mobilePhone != ''">
                and ui.mobile_phone = #{mobilePhone}
            </if>
            <if test="aid != null  and aid != ''">
                and ui.aid = #{aid}
            </if>
            <if test="teachId != null  and teachId != ''">
                and ui.teach_id = #{teachId}
            </if>
            <if test="classId != null">
                and ui.class_id = #{classId}
            </if>
            <if test="registerDate != null">
                and ui.register_date = #{registerDate}
            </if>
            <if test="memberType != null and memberType==-10">
                and ui.member_type &lt;= 0
            </if>
            <if test="memberType != null and memberType==10">
                and ui.member_type > 0
            </if>
            <if test="memberType != null and memberType!=-10 and memberType!=10">
                and ui.member_type = #{memberType}
            </if>
            <if test="userName != null and userName != ''">
                and ui.user_name like concat(#{userName},'%')
            </if>
            <if test="nickName != null  and nickName != ''">
                and ui.nick_name like concat(#{nickName},'%')
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId!=-1">
                and ui.exclusive_shop_id = #{exclusiveShopId}
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId==-1">
                and ui.exclusive_shop_id is null
            </if>
            <if test="params != null and params.registerDateS != null">
                and ui.register_date >= #{params.registerDateS}
            </if>
            <if test="params != null and params.registerDateE != null">
                and ui.register_date &lt;= #{params.registerDateE}
            </if>
            <if test="params != null and params.expirationDateS != null">
                and ui.expiration_date >= #{params.expirationDateS}
            </if>
            <if test="params != null and params.expirationDateE != null">
                and ui.expiration_date &lt;= #{params.expirationDateE}
            </if>
            <if test="params != null and params.expiration != null and params.expiration == 'true'">
                and ui.expiration_date  &lt; CURRENT_DATE ()
            </if>
            <if test="params != null and params.expiration != null and params.expiration == 'false'">
                and ui.expiration_date &gt;= CURRENT_DATE ()
            </if>
            <if test="params != null and params.userIdS != null and params.userIdE != null">
                and ui.user_id between #{params.userIdS} and #{params.userIdE}
            </if>
            <if test="params != null and params.userId != null and params.userId != ''">
                and ui.user_id = #{params.userId}
            </if>
            <if test="params != null and params.username != null and params.username != ''">
                and ui.user_name = #{params.username}
            </if>
            <if test="aidList.size>0">
                and ui.aid in
                <foreach collection="aidList" item="agentAidStr" open="(" separator="," close=")">
                    #{agentAidStr}
                </foreach>
            </if>
            <if test="exclusiveShopIdList.size>0">
                and ui.exclusive_shop_id in
                <foreach collection="exclusiveShopIdList" item="exclusiveShopIdStr" open="(" separator="," close=")">
                    #{exclusiveShopIdStr}
                </foreach>
            </if>
        </where>
    </select>


    <select id="countUserInfoList" parameterType="UserInfo" resultType="map">
        SELECT
        SUM(IF(ui.member_type=3,1,0)) svipNum,
        SUM(IF(ui.member_type=2,1,0)) vipNum,
        SUM(IF(ui.member_type=1,1,0)) ordinaryNum,
        SUM(IF(ui.member_type=0,1,0)) experienceNum,
        SUM(IF(ui.member_type=-1,1,0)) pcExperienceNum
        from user_info ui
        <where>
            <if test="userId != null  and userId != ''">
                and ui.user_id = #{userId}
            </if>
            <if test="password != null  and password != ''">
                and ui.password = #{password}
            </if>
            <if test="mobilePhone != null  and mobilePhone != ''">
                and ui.mobile_phone = #{mobilePhone}
            </if>
            <if test="aid != null  and aid != ''">
                and ui.aid = #{aid}
            </if>
            <if test="teachId != null  and teachId != ''">
                and ui.teach_id = #{teachId}
            </if>
            <if test="classId != null">
                and ui.class_id = #{classId}
            </if>
            <if test="registerDate != null">
                and ui.register_date = #{registerDate}
            </if>
            <if test="memberType != null and memberType==-10">
                and ui.member_type &lt;= 0
            </if>
            <if test="memberType != null and memberType==10">
                and ui.member_type > 0
            </if>
            <if test="memberType != null and memberType!=-10 and memberType!=10">
                and ui.member_type = #{memberType}
            </if>
            <if test="userName != null and userName != ''">
                and ui.user_name like concat(#{userName},'%')
            </if>
            <if test="nickName != null  and nickName != ''">
                and ui.nick_name like concat(#{nickName},'%')
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId!=-1">
                and ui.exclusive_shop_id = #{exclusiveShopId}
            </if>
            <if test="exclusiveShopId != null and exclusiveShopId==-1">
                and ui.exclusive_shop_id is null
            </if>
            <if test="params != null and params.registerDateS != null">
                and ui.register_date >= #{params.registerDateS}
            </if>
            <if test="params != null and params.registerDateE != null">
                and ui.register_date &lt;= #{params.registerDateE}
            </if>
            <if test="params != null and params.expirationDateS != null">
                and ui.expiration_date >= #{params.expirationDateS}
            </if>
            <if test="params != null and params.expirationDateE != null">
                and ui.expiration_date &lt;= #{params.expirationDateE}
            </if>
            <if test="params != null and params.expiration != null and params.expiration == 'true'">
                and ui.expiration_date  &lt; CURRENT_DATE ()
            </if>
            <if test="params != null and params.expiration != null and params.expiration == 'false'">
                and ui.expiration_date &gt;= CURRENT_DATE ()
            </if>
            <if test="params != null and params.userIdS != null and params.userIdE != null">
                and ui.user_id between #{params.userIdS} and #{params.userIdE}
            </if>
            <if test="params != null and params.userId != null and params.userId != ''">
                and ui.user_id = #{params.userId}
            </if>
            <if test="params != null and params.username != null and params.username != ''">
                and ui.user_name = #{params.username}
            </if>
            <if test="aidList.size>0">
                and ui.aid in
                <foreach collection="aidList" item="agentAidStr" open="(" separator="," close=")">
                    #{agentAidStr}
                </foreach>
            </if>
            <if test="exclusiveShopIdList.size>0">
                and ui.exclusive_shop_id in
                <foreach collection="exclusiveShopIdList" item="exclusiveShopIdStr" open="(" separator="," close=")">
                    #{exclusiveShopIdStr}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectKidUserInfoById" parameterType="Long" resultMap="KidUserInfoResult">
        <include refid="selectKidUserInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKidUserInfo" parameterType="KidUserInfo" useGeneratedKeys="true" keyProperty="id">
        insert into user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="aid != null and aid != ''">aid,</if>
            <if test="exclusiveShopId != null">exclusive_shop_id,</if>
            <if test="teachId != null">teach_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="tableSuffix != null">table_suffix,</if>
            <if test="registerDate != null">register_date,</if>
            <if test="firstPurchaseDate != null">first_purchase_date,</if>
            <if test="lastPurchaseDate != null">last_purchase_date,</if>
            <if test="expirationDate != null">expiration_date,</if>
            <if test="memberType != null">member_type,</if>
            <if test="lollipop != null">lollipop,</if>
            <if test="headImageUrl != null and headImageUrl != ''">head_image_url,</if>
            <if test="headPendantImg != null">head_pendant_img,</if>
            <if test="userName != null">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="sex != null">sex,</if>
            <if test="birthday != null">birthday,</if>
            <if test="email != null">email,</if>
            <if test="qq != null">qq,</if>
            <if test="signature != null">signature,</if>
            <if test="school != null">school,</if>
            <if test="gradeClass != null">grade_class,</if>
            <if test="address != null">address,</if>
            <if test="payPwd != null">pay_pwd,</if>
            <if test="totalSignCount != null">total_sign_count,</if>
            <if test="init != null">init,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="aid != null and aid != ''">#{aid},</if>
            <if test="exclusiveShopId != null">#{exclusiveShopId},</if>
            <if test="teachId != null">#{teachId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="tableSuffix != null">#{tableSuffix},</if>
            <if test="registerDate != null">#{registerDate},</if>
            <if test="firstPurchaseDate != null">#{firstPurchaseDate},</if>
            <if test="lastPurchaseDate != null">#{lastPurchaseDate},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="memberType != null">#{memberType},</if>
            <if test="lollipop != null">#{lollipop},</if>
            <if test="headImageUrl != null and headImageUrl != ''">#{headImageUrl},</if>
            <if test="headPendantImg != null">#{headPendantImg},</if>
            <if test="userName != null">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="sex != null">#{sex},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="email != null">#{email},</if>
            <if test="qq != null">#{qq},</if>
            <if test="signature != null">#{signature},</if>
            <if test="school != null">#{school},</if>
            <if test="gradeClass != null">#{gradeClass},</if>
            <if test="address != null">#{address},</if>
            <if test="payPwd != null">#{payPwd},</if>
            <if test="totalSignCount != null">#{totalSignCount},</if>
            <if test="init != null">#{init},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updateKidUserInfo" parameterType="KidUserInfo">
        update user_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="aid != null and aid != ''">aid = #{aid},</if>
            <if test="exclusiveShopId != null">exclusive_shop_id = #{exclusiveShopId},</if>
            <if test="teachId != null">teach_id = #{teachId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="tableSuffix != null">table_suffix = #{tableSuffix},</if>
            <if test="registerDate != null">register_date = #{registerDate},</if>
            <if test="firstPurchaseDate != null">first_purchase_date = #{firstPurchaseDate},</if>
            <if test="lastPurchaseDate != null">last_purchase_date = #{lastPurchaseDate},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="memberType != null">member_type = #{memberType},</if>
            <if test="lollipop != null">lollipop = #{lollipop},</if>
            <if test="headImageUrl != null and headImageUrl != ''">head_image_url = #{headImageUrl},</if>
            <if test="headPendantImg != null">head_pendant_img = #{headPendantImg},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="email != null">email = #{email},</if>
            <if test="qq != null">qq = #{qq},</if>
            <if test="signature != null">signature = #{signature},</if>
            <if test="school != null">school = #{school},</if>
            <if test="gradeClass != null">grade_class = #{gradeClass},</if>
            <if test="address != null">address = #{address},</if>
            <if test="payPwd != null">pay_pwd = #{payPwd},</if>
            <if test="totalSignCount != null">total_sign_count = #{totalSignCount},</if>
            <if test="init != null">init = #{init},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where user_id =  #{userId}
    </update>

    <delete id="deleteKidUserInfoById" parameterType="Long">
        delete from user_info where id = #{id}
    </delete>

    <delete id="deleteKidUserInfoByIds" parameterType="String">
        delete from user_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getClassList" resultType="com.redbook.system.domain.vo.ClassInfo"
            parameterType="java.lang.String">
        select id, name, user_id teacherId, aid
        from class
        where user_id = #{userId}
    </select>

    <insert id="createDefaultClass">
        insert into class (user_id,name,aid, type) values (#{teacherId},'（少儿）默认班级',#{aid} , 1)
    </insert>

    <update id="renewKidUser">
        update user_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="firstPurchaseDate != null">first_purchase_date = #{firstPurchaseDate},</if>
            <if test="lastPurchaseDate != null">last_purchase_date = #{lastPurchaseDate},</if>
            <if test="expirationDate != null">expiration_date = #{expirationDate},</if>
            <if test="memberType != null">member_type = #{memberType},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <select id="selectKidUserInfoByUserId" resultMap="KidUserInfoResult">
        <include refid="selectKidUserInfoVo"/>
        where user_id = #{userId}
    </select>

    <update id="updateStudentClass">
        update user_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="classId != null">class_id = #{classId},</if>
            <if test="teacherId != null">teach_id = #{teacherId},</if>
            <if test="aid != null">aid = #{aid},</if>
            <if test="exclusiveShopId != null">exclusive_shop_id = #{exclusiveShopId},</if>
        </trim>
        where user_id in
        <foreach collection="userId" open="(" close=")" separator="," item="studentId">
            #{studentId}
        </foreach>
    </update>

    <select id="getClassName" resultType="java.lang.String">
        select name from class where id = #{classId}
    </select>
</mapper>