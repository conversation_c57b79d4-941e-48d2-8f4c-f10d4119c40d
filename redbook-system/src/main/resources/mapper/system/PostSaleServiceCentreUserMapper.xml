<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.PostSaleServiceCentreUserMapper">
    
    <resultMap type="PostSaleServiceCentreUser" id="PostSaleServiceCentreUserResult">
        <result property="id"    column="id"    />
        <result property="serviceCentreId"    column="service_centre_id"    />
        <result property="userId"    column="user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectPostSaleServiceCentreUserVo">
        select id, service_centre_id, user_id, create_time, create_by, update_time, update_by, is_delete from post_sale_service_centre_user
    </sql>

    <select id="selectPostSaleServiceCentreUserList" parameterType="PostSaleServiceCentreUser" resultMap="PostSaleServiceCentreUserResult">
        <include refid="selectPostSaleServiceCentreUserVo"/>
        <where>  
            <if test="serviceCentreId != null "> and service_centre_id = #{serviceCentreId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="updateBy != null "> and update_by = #{updateBy}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>
    
    <select id="selectPostSaleServiceCentreUserById" parameterType="Long" resultMap="PostSaleServiceCentreUserResult">
        <include refid="selectPostSaleServiceCentreUserVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPostSaleServiceCentreUser" parameterType="PostSaleServiceCentreUser" useGeneratedKeys="true" keyProperty="id">
        insert into post_sale_service_centre_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceCentreId != null">service_centre_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDelete != null">is_delete,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceCentreId != null">#{serviceCentreId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDelete != null">#{isDelete},</if>
         </trim>
    </insert>

    <update id="updatePostSaleServiceCentreUser" parameterType="PostSaleServiceCentreUser">
        update post_sale_service_centre_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceCentreId != null">service_centre_id = #{serviceCentreId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePostSaleServiceCentreUserById" parameterType="Long">
        update post_sale_service_centre_user set is_delete = 1 where id = #{id}
    </delete>

    <delete id="deletePostSaleServiceCentreUserByIds" parameterType="String">
        update post_sale_service_centre_user set is_delete = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deletePostSaleServiceCentreUserByServiceCentreId">
        update post_sale_service_centre_user set is_delete = 1 where service_centre_id = #{serviceCentreId} and is_delete=0
    </delete>
</mapper>