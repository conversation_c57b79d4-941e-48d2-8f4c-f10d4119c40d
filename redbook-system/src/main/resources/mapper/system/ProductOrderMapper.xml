<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.ProductOrderMapper">

    <resultMap type="ProductOrder" id="ProductOrderResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="agentId"    column="agent_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="productId"    column="product_id"    />
        <result property="productCategoryId"    column="product_category_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productCategoryName"    column="product_category_name"    />
        <result property="orderCount"    column="order_count"    />
        <result property="orderCountDetail"    column="order_count_detail"    />
        <result property="trackingNumber"    column="tracking_number"    />
        <result property="trackingName"    column="tracking_name"    />
        <result property="reciever"    column="reciever"    />
        <result property="recieverPhone"    column="reciever_phone"    />
        <result property="recieverAddess"    column="reciever_addess"    />
        <result property="status"    column="status"    />
        <result property="payMoney"    column="pay_money"    />
        <result property="payMoneyType"    column="pay_money_type"    />
        <result property="tabletIds"    column="tablet_ids"    />
        <result property="createTime"    column="create_time"    />
        <result property="isSended"    column="is_sended"    />
        <result property="sendProductTime"    column="send_product_time"    />
        <result property="sendProductRemarks"    column="send_product_remarks"    />
        <result property="extra"    column="extra"    />
        <result property="updateTime"    column="update_time"    />
        <result property="agentName"    column="agent_name"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="payExclusiveShopId"    column="pay_exclusive_shop_id"    />
        <result property="payExclusiveShopMoney"    column="pay_exclusive_shop_money"    />
        <result property="signPerson"    column="sign_person"    />
    </resultMap>

    <sql id="selectProductOrderVo">
        select po.id,
               po.user_id,
               po.agent_id,
               po.order_no,
               po.product_id,
               po.product_category_id,
               po.product_name,
               po.product_category_name,
               po.order_count,
               po.order_count_detail,
               po.tracking_number,
               po.tracking_name,
               po.reciever,
               po.reciever_phone,
               po.reciever_addess,
               po.status,
               po.pay_money,
               po.pay_money_type,
               po.tablet_ids,
               po.create_time,
               po.update_time,
               po.is_sended,
               po.send_product_time,
               po.send_product_remarks,
               po.extra,
               a.name       as agent_name,
               u2.nick_name as contact_person,
               po.pay_exclusive_shop_id,
               po.pay_exclusive_shop_money,
               if(a.type=1,a.company_name,a.person_name) as sign_person
        from product_order po
                 LEFT JOIN agent a ON a.id = po.agent_id
                 LEFT JOIN sys_user u2 on a.contact_person = u2.user_id
                 LEFT JOIN exclusive_shop es on po.pay_exclusive_shop_id = es.id
    </sql>

    <select id="selectProductOrderList" parameterType="ProductOrder" resultMap="ProductOrderResult">
        <include refid="selectProductOrderVo"/>
        <if test="mId != null and agentIdList.size==0">
            LEFT JOIN agent_mapping am ON a.id = am.agent_id and am.user_id = #{mId}
        </if>
        <where>
            <if test="userId != null "> and po.user_id = #{userId}</if>
            <if test="agentId != null "> and po.agent_id = #{agentId}</if>
            <if test="onlyExclusiveShopOrder != null and onlyExclusiveShopOrder">
                and po.pay_exclusive_shop_id is not null
            </if>
            <if test="exclusiveShopId != null "> and po.pay_exclusive_shop_id = #{exclusiveShopId}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productCategoryId != null "> and product_category_id = #{productCategoryId}</if>
            <if test="productName != null "> and product_name  like concat('%', #{productName}, '%')</if>
            <if test="orderCount != null "> and order_count = #{orderCount}</if>
            <if test="orderCountDetail != null  and orderCountDetail != ''"> and order_count_detail = #{orderCountDetail}</if>
            <if test="trackingNumber != null  and trackingNumber != ''"> and tracking_number = #{trackingNumber}</if>
            <if test="trackingName != null  and trackingName != ''"> and tracking_name like concat('%', #{trackingName}, '%')</if>
            <if test="reciever != null  and reciever != ''"> and reciever = #{reciever}</if>
            <if test="recieverPhone != null  and recieverPhone != ''"> and reciever_phone = #{recieverPhone}</if>
            <if test="recieverAddess != null  and recieverAddess != ''"> and reciever_addess = #{recieverAddess}</if>
            <if test="status != null "> and po.status = #{status}</if>
            <if test="payMoney != null "> and pay_money = #{payMoney}</if>
            <if test="payMoneyType != null "> and pay_money_type = #{payMoneyType}</if>
            <if test="tabletIds != null  and tabletIds != ''"> and tablet_ids = #{tabletIds}</if>
            <if test="isSended != null  and isSended != ''"> and is_sended = #{isSended}</if>
            <if test="sendProductTime != null "> and send_product_time = #{sendProductTime}</if>
            <if test="extra != null  and extra != ''"> and extra = #{extra}</if>
            <if test="params!=null and params.sendProductTimeS !=null">and po.send_product_time &gt;=
                #{params.sendProductTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.sendProductTimeE !=null">and po.send_product_time &lt;=
                #{params.sendProductTimeE}' 23:59:59'
            </if>
            <if test="params!=null and params.createTimeS !=null">and po.create_time &gt;=
                #{params.createTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.createTimeE !=null">and po.create_time &lt;=
                #{params.createTimeE}' 23:59:59'
            </if>
            <if test="mId != null and agentIdList.size==0">
                and am.user_id = #{mId}
            </if>
            <if test="exclusiveShopManagerId != null">
                and es.manager_id = #{exclusiveShopManagerId}
            </if>
            <if test="agentIdList.size>0">
                and a.id in
                <foreach collection="agentIdList" item="agentIdStr" open="(" separator="," close=")">
                    #{agentIdStr}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectProductOrderById" parameterType="Long" resultMap="ProductOrderResult">
        <include refid="selectProductOrderVo"/>
        where po.id = #{id}
    </select>
    <select id="selectProductOrderByOrderNo" parameterType="String" resultMap="ProductOrderResult">
        <include refid="selectProductOrderVo"/>
        where po.order_no = #{orderNo}
    </select>

    <insert id="insertProductOrder" parameterType="ProductOrder" useGeneratedKeys="true" keyProperty="id">
        insert into product_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="productCategoryId != null">product_category_id,</if>
            <if test="productCategoryName != null">product_category_name,</if>
            <if test="orderCount != null">order_count,</if>
            <if test="orderCountDetail != null">order_count_detail,</if>
            <if test="trackingNumber != null">tracking_number,</if>
            <if test="trackingName != null">tracking_name,</if>
            <if test="reciever != null">reciever,</if>
            <if test="recieverPhone != null">reciever_phone,</if>
            <if test="recieverAddess != null">reciever_addess,</if>
            <if test="status != null">status,</if>
            <if test="payMoney != null">pay_money,</if>
            <if test="payMoneyType != null">pay_money_type,</if>
            <if test="tabletIds != null">tablet_ids,</if>
            <if test="createTime != null">create_time,</if>
            <if test="isSended != null and isSended != ''">is_sended,</if>
            <if test="sendProductTime != null">send_product_time,</if>
            <if test="sendProductRemarks != null">send_product_remarks,</if>
            <if test="extra != null">extra,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="payExclusiveShopId != null">pay_exclusive_shop_id,</if>
            <if test="payExclusiveShopMoney != null">pay_exclusive_shop_money,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productCategoryId != null">#{productCategoryId},</if>
            <if test="productCategoryName != null">#{productCategoryName},</if>
            <if test="orderCount != null">#{orderCount},</if>
            <if test="orderCountDetail != null">#{orderCountDetail},</if>
            <if test="trackingNumber != null">#{trackingNumber},</if>
            <if test="trackingName != null">#{trackingName},</if>
            <if test="reciever != null">#{reciever},</if>
            <if test="recieverPhone != null">#{recieverPhone},</if>
            <if test="recieverAddess != null">#{recieverAddess},</if>
            <if test="status != null">#{status},</if>
            <if test="payMoney != null">#{payMoney},</if>
            <if test="payMoneyType != null">#{payMoneyType},</if>
            <if test="tabletIds != null">#{tabletIds},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="isSended != null and isSended != ''">#{isSended},</if>
            <if test="sendProductTime != null">#{sendProductTime},</if>
            <if test="sendProductRemarks != null">#{sendProductRemarks},</if>
            <if test="extra != null">#{extra},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="payExclusiveShopId != null">#{payExclusiveShopId},</if>
            <if test="payExclusiveShopMoney != null">#{payExclusiveShopMoney},</if>
         </trim>
    </insert>

    <update id="updateProductOrder" parameterType="ProductOrder">
        update product_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="orderCount != null">order_count = #{orderCount},</if>
            <if test="orderCountDetail != null">order_count_detail = #{orderCountDetail},</if>
            <if test="trackingNumber != null">tracking_number = #{trackingNumber},</if>
            <if test="trackingName != null">tracking_name = #{trackingName},</if>
            <if test="reciever != null">reciever = #{reciever},</if>
            <if test="recieverPhone != null">reciever_phone = #{recieverPhone},</if>
            <if test="recieverAddess != null">reciever_addess = #{recieverAddess},</if>
            <if test="status != null">status = #{status},</if>
            <if test="payMoney != null">pay_money = #{payMoney},</if>
            <if test="payMoneyType != null">pay_money_type = #{payMoneyType},</if>
            <if test="tabletIds != null">tablet_ids = #{tabletIds},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="isSended != null and isSended != ''">is_sended = #{isSended},</if>
            <if test="sendProductTime != null">send_product_time = #{sendProductTime},</if>
            <if test="sendProductRemarks != null">send_product_remarks = #{sendProductRemarks},</if>
            <if test="extra != null">extra = #{extra},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductOrderById" parameterType="Long">
        delete from product_order where id = #{id}
    </delete>

    <delete id="deleteProductOrderByIds" parameterType="String">
        delete from product_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="cancelProductOrder">
        update product_order set tracking_number = null, tracking_name = null, send_product_time = null,status=2 where id = #{id}
    </update>
</mapper>