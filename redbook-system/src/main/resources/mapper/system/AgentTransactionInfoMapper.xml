<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.redbook.system.mapper.AgentTransactionInfoMapper">

    <resultMap type="AgentTransactionInfo" id="AgentTransactionInfoResult">
        <result property="id" column="id"/>
        <result property="agentId" column="agent_id"/>
        <result property="transactionType" column="transaction_type"/>
        <result property="fundType" column="fund_type"/>
        <result property="indentNumber" column="indent_number"/>
        <result property="money" column="money"/>
        <result property="paymentType" column="payment_type"/>
        <result property="balance" column="balance"/>
        <result property="transactionTime" column="transaction_time"/>
        <result property="topUpWayId" column="top_up_way_id"/>
        <result property="topUpTypeId" column="top_up_type_id"/>
        <result property="remark" column="remark"/>
        <result property="refundStatus" column="refund_status"/>
        <result property="invoiceStatus" column="invoice_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="exclusiveShopName" column="exclusiveShopName"/>
    </resultMap>

    <sql id="selectAgentTransactionInfoVo">
        select ati.id,
               ati.agent_id,
               ati.transaction_type,
               ati.fund_type,
               ati.indent_number,
               ati.money,
               ati.payment_type,
               ati.balance,
               ati.transaction_time,
               ati.top_up_way_id,
               ati.top_up_type_id,
               ati.remark,
               ati.refund_status,
               ati.invoice_status,
               ati.create_time,
               ati.update_time,
               a.name as agentName,
               es.name as exclusiveShopName
        from agent_transaction_info ati
                 left join agent a on ati.agent_id = a.id
                left join agent_mapping am on a.id = am.agent_id
                left join exclusive_shop_transaction_info esti on ati.indent_number= esti.indent_number
                left join exclusive_shop es on es.id = esti.exclusive_shop_id and es.agent_id = a.id
    </sql>

    <select id="selectAgentTransactionInfoList" parameterType="AgentTransactionInfo"
            resultMap="AgentTransactionInfoResult">
        <include refid="selectAgentTransactionInfoVo"/>
        <where>
            <if test="agentId != null">
                and ati.agent_id = #{agentId}
            </if>
            <if test="transactionType != null  and ati.transactionType != ''">
                and ati.transaction_type = #{transactionType}
            </if>
            <if test="fundType != null">
                and ati.fund_type = #{fundType}
            </if>
            <if test="paymentType !=null ">
                and ati.payment_type = #{paymentType}
            </if>
            <if test="refundStatus !=null ">
                and ati.refund_status = #{refundStatus}
            </if>
            <if test="invoiceStatus !=null ">
                and ati.invoice_status = #{invoiceStatus}
            </if>
            <if test="indentNumber != null  and indentNumber != ''">
                and ati.indent_number = #{indentNumber}
            </if>
            <if test="params!=null and params.transactionTimeS !=null">and ati.transaction_time >=#{params.transactionTimeS}' 00:00:00'
            </if>
            <if test="params!=null and params.transactionTimeE !=null">and ati.transaction_time &lt;=#{params.transactionTimeE}' 23:59:59'
            </if>
            <if test="startTime!=null and startTime != ''">and ati.transaction_time >=#{startTime}' 00:00:00'
            </if>
            <if test="endTime!=null and endTime != ''">and ati.transaction_time &lt;=#{endTime}' 23:59:59'
            </if>
            <if test="mId != null and mId != ''">
                and am.user_id = #{mId}
            </if>
        </where>
        group by ati.id
    </select>


    <select id="selectAgentTransactionInfoById" parameterType="Long" resultMap="AgentTransactionInfoResult">
        <include refid="selectAgentTransactionInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertAgentTransactionInfo" parameterType="AgentTransactionInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into agent_transaction_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">agent_id,</if>
            <if test="transactionType != null and transactionType != ''">transaction_type,</if>
            <if test="fundType != null">fund_type,</if>
            <if test="indentNumber != null">indent_number,</if>
            <if test="money != null">money,</if>
            <if test="paymentType != null">payment_type,</if>
            <if test="balance != null">balance,</if>
            <if test="transactionTime != null">transaction_time,</if>
            <if test="topUpWayId != null">top_up_way_id,</if>
            <if test="topUpTypeId != null">top_up_type_id,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentId != null">#{agentId},</if>
            <if test="transactionType != null and transactionType != ''">#{transactionType},</if>
            <if test="fundType != null">#{fundType},</if>
            <if test="indentNumber != null">#{indentNumber},</if>
            <if test="money != null">#{money},</if>
            <if test="paymentType != null">#{paymentType},</if>
            <if test="balance != null">#{balance},</if>
            <if test="transactionTime != null">#{transactionTime},</if>
            <if test="topUpWayId != null">#{topUpWayId},</if>
            <if test="topUpTypeId != null">#{topUpTypeId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAgentTransactionInfo" parameterType="AgentTransactionInfo">
        update agent_transaction_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="transactionType != null and transactionType != ''">transaction_type = #{transactionType},</if>
            <if test="fundType != null">fund_type = #{fundType},</if>
            <if test="indentNumber != null">indent_number = #{indentNumber},</if>
            <if test="money != null">money = #{money},</if>
            <if test="paymentType != null">payment_type = #{paymentType},</if>
            <if test="balance != null">balance = #{balance},</if>
            <if test="transactionTime != null">transaction_time = #{transactionTime},</if>
            <if test="topUpWayId != null">top_up_way_id = #{topUpWayId},</if>
            <if test="topUpTypeId != null">top_up_type_id = #{topUpTypeId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="refundStatus != null">refund_status = #{refundStatus},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteAgentTransactionInfoById" parameterType="Long">
        delete
        from agent_transaction_info
        where id = #{id}
    </delete>

    <delete id="deleteAgentTransactionInfoByIds" parameterType="String">
        delete from agent_transaction_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>