package com.redbook.dashboard.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 数据看板配置类
 * 管理数据看板相关的配置参数
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Configuration
@ConfigurationProperties(prefix = "dashboard")
public class DashboardConfig {

    /** 是否启用缓存 */
    private boolean cacheEnabled = true;

    /** 缓存过期时间（秒） */
    private int cacheExpireTime = 3600;

    /** 默认页面大小 */
    private int defaultPageSize = 10;

    /** 最大页面大小 */
    private int maxPageSize = 1000;

    /** 是否启用数据权限控制 */
    private boolean dataPermissionEnabled = true;

    /** 默认时间范围（天） */
    private int defaultTimeRange = 30;

    /** 最大时间范围（天） */
    private int maxTimeRange = 365;

    /** 是否启用异步处理 */
    private boolean asyncEnabled = true;

    /** 异步线程池大小 */
    private int asyncThreadPoolSize = 10;

    /** 是否启用数据导出 */
    private boolean exportEnabled = true;

    /** 导出文件最大行数 */
    private int maxExportRows = 100000;

    /** Redis缓存前缀 */
    private String redisCachePrefix = "dashboard:";

    /** 统计数据刷新间隔（分钟） */
    private int statsRefreshInterval = 30;

    public boolean isCacheEnabled() {
        return cacheEnabled;
    }

    public void setCacheEnabled(boolean cacheEnabled) {
        this.cacheEnabled = cacheEnabled;
    }

    public int getCacheExpireTime() {
        return cacheExpireTime;
    }

    public void setCacheExpireTime(int cacheExpireTime) {
        this.cacheExpireTime = cacheExpireTime;
    }

    public int getDefaultPageSize() {
        return defaultPageSize;
    }

    public void setDefaultPageSize(int defaultPageSize) {
        this.defaultPageSize = defaultPageSize;
    }

    public int getMaxPageSize() {
        return maxPageSize;
    }

    public void setMaxPageSize(int maxPageSize) {
        this.maxPageSize = maxPageSize;
    }

    public boolean isDataPermissionEnabled() {
        return dataPermissionEnabled;
    }

    public void setDataPermissionEnabled(boolean dataPermissionEnabled) {
        this.dataPermissionEnabled = dataPermissionEnabled;
    }

    public int getDefaultTimeRange() {
        return defaultTimeRange;
    }

    public void setDefaultTimeRange(int defaultTimeRange) {
        this.defaultTimeRange = defaultTimeRange;
    }

    public int getMaxTimeRange() {
        return maxTimeRange;
    }

    public void setMaxTimeRange(int maxTimeRange) {
        this.maxTimeRange = maxTimeRange;
    }

    public boolean isAsyncEnabled() {
        return asyncEnabled;
    }

    public void setAsyncEnabled(boolean asyncEnabled) {
        this.asyncEnabled = asyncEnabled;
    }

    public int getAsyncThreadPoolSize() {
        return asyncThreadPoolSize;
    }

    public void setAsyncThreadPoolSize(int asyncThreadPoolSize) {
        this.asyncThreadPoolSize = asyncThreadPoolSize;
    }

    public boolean isExportEnabled() {
        return exportEnabled;
    }

    public void setExportEnabled(boolean exportEnabled) {
        this.exportEnabled = exportEnabled;
    }

    public int getMaxExportRows() {
        return maxExportRows;
    }

    public void setMaxExportRows(int maxExportRows) {
        this.maxExportRows = maxExportRows;
    }

    public String getRedisCachePrefix() {
        return redisCachePrefix;
    }

    public void setRedisCachePrefix(String redisCachePrefix) {
        this.redisCachePrefix = redisCachePrefix;
    }

    public int getStatsRefreshInterval() {
        return statsRefreshInterval;
    }

    public void setStatsRefreshInterval(int statsRefreshInterval) {
        this.statsRefreshInterval = statsRefreshInterval;
    }

    @Override
    public String toString() {
        return "DashboardConfig{" +
                "cacheEnabled=" + cacheEnabled +
                ", cacheExpireTime=" + cacheExpireTime +
                ", defaultPageSize=" + defaultPageSize +
                ", maxPageSize=" + maxPageSize +
                ", dataPermissionEnabled=" + dataPermissionEnabled +
                ", defaultTimeRange=" + defaultTimeRange +
                ", maxTimeRange=" + maxTimeRange +
                ", asyncEnabled=" + asyncEnabled +
                ", asyncThreadPoolSize=" + asyncThreadPoolSize +
                ", exportEnabled=" + exportEnabled +
                ", maxExportRows=" + maxExportRows +
                ", redisCachePrefix='" + redisCachePrefix + '\'' +
                ", statsRefreshInterval=" + statsRefreshInterval +
                '}';
    }
}