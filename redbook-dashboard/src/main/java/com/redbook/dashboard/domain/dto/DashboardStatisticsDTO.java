package com.redbook.dashboard.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 数据统计查询参数
 * 用于数据统计的查询条件
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("数据统计查询参数")
public class DashboardStatisticsDTO extends BaseDTO {
    
    /** 统计键 */
    @ApiModelProperty("统计键")
    private String statKey;
    
    /** 统计键列表 */
    @ApiModelProperty("统计键列表")
    private List<String> statKeys;
    
    /** 统计名称 */
    @ApiModelProperty("统计名称")
    private String statName;
    
    /** 业务类型 */
    @ApiModelProperty("业务类型")
    private String businessType;
    
    /** 业务类型列表 */
    @ApiModelProperty("业务类型列表")
    private List<String> businessTypes;
    
    /** 统计日期 */
    @ApiModelProperty("统计日期")
    private String statDate;
    
    /** 统计日期开始 */
    @ApiModelProperty("统计日期开始")
    private String statDateStart;
    
    /** 统计日期结束 */
    @ApiModelProperty("统计日期结束")
    private String statDateEnd;
    
    /** 统计时间段 */
    @ApiModelProperty("统计时间段")
    private String statPeriod;
    
    /** 统计时间段列表 */
    @ApiModelProperty("统计时间段列表")
    private List<String> statPeriods;
    
    /** 数据来源 */
    @ApiModelProperty("数据来源")
    private String dataSource;
    
    /** 数据来源列表 */
    @ApiModelProperty("数据来源列表")
    private List<String> dataSources;
    
    /** 显示类型（1:数值, 2:百分比, 3:图表） */
    @ApiModelProperty("显示类型（1:数值, 2:百分比, 3:图表）")
    private Integer displayType;
    
    /** 显示类型列表 */
    @ApiModelProperty("显示类型列表")
    private List<Integer> displayTypes;
    
    /** 图表类型（1:折线图, 2:柱状图, 3:饼图, 4:雷达图） */
    @ApiModelProperty("图表类型（1:折线图, 2:柱状图, 3:饼图, 4:雷达图）")
    private Integer chartType;
    
    /** 图表类型列表 */
    @ApiModelProperty("图表类型列表")
    private List<Integer> chartTypes;
    
    /** 是否在首页显示（0:否, 1:是） */
    @ApiModelProperty("是否在首页显示（0:否, 1:是）")
    private Integer showInHome;
    
    /** 最小统计值 */
    @ApiModelProperty("最小统计值")
    private Long minStatValue;
    
    /** 最大统计值 */
    @ApiModelProperty("最大统计值")
    private Long maxStatValue;
    
    /** 是否包含图表数据 */
    @ApiModelProperty("是否包含图表数据")
    private Boolean includeChartData;
    
    /** 是否计算增长率 */
    @ApiModelProperty("是否计算增长率")
    private Boolean calculateGrowthRate;
    
    /** 聚合类型（sum, avg, max, min, count） */
    @ApiModelProperty("聚合类型")
    private String aggregateType;
    
    /** 时间维度（day, week, month, quarter, year） */
    @ApiModelProperty("时间维度")
    private String timeDimension;
    
    /** 排行榜数量 */
    @ApiModelProperty("排行榜数量")
    private Integer topCount;
    
    /** 是否降序排列 */
    @ApiModelProperty("是否降序排列")
    private Boolean descOrder;
    
    /** 对比时间段 */
    @ApiModelProperty("对比时间段")
    private String comparePeriod;
    
    /** 对比开始时间 */
    @ApiModelProperty("对比开始时间")
    private String compareStartTime;
    
    /** 对比结束时间 */
    @ApiModelProperty("对比结束时间")
    private String compareEndTime;
}