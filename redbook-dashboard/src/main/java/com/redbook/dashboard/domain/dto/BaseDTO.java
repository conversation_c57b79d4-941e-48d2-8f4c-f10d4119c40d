package com.redbook.dashboard.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Dashboard模块基础数据传输对象
 * 用于查询参数和数据传输的基础DTO类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class BaseDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** ID */
    @ApiModelProperty("ID")
    private Long id;
    
    /** ID列表 */
    @ApiModelProperty("ID列表")
    private List<Long> ids;
    
    /** 关键字搜索 */
    @ApiModelProperty("关键字搜索")
    private String keyword;
    
    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("开始时间")
    private Date startTime;
    
    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("结束时间")
    private Date endTime;
    
    /** 开始时间字符串 */
    @ApiModelProperty("开始时间字符串")
    private String startTimeStr;
    
    /** 结束时间字符串 */
    @ApiModelProperty("结束时间字符串")
    private String endTimeStr;
    
    /** 状态 */
    @ApiModelProperty("状态")
    private Integer status;
    
    /** 状态列表 */
    @ApiModelProperty("状态列表")
    private List<Integer> statusList;
    
    /** 页码 */
    @ApiModelProperty("页码")
    private Integer pageNum;
    
    /** 页面大小 */
    @ApiModelProperty("页面大小")
    private Integer pageSize;
    
    /** 排序字段 */
    @ApiModelProperty("排序字段")
    private String orderBy;
    
    /** 排序方向 */
    @ApiModelProperty("排序方向")
    private String orderDirection;
    
    /** 分组字段 */
    @ApiModelProperty("分组字段")
    private String groupBy;
    
    /** 时间间隔（day, week, month, year） */
    @ApiModelProperty("时间间隔")
    private String interval;
    
    /** 限制数量 */
    @ApiModelProperty("限制数量")
    private Integer limit;
    
    /** 偏移量 */
    @ApiModelProperty("偏移量")
    private Integer offset;
    
    /** 业务类型 */
    @ApiModelProperty("业务类型")
    private String businessType;
    
    /** 数据类型 */
    @ApiModelProperty("数据类型")
    private String dataType;
    
    /** 统计类型 */
    @ApiModelProperty("统计类型")
    private String statsType;
    
    /** 排行字段 */
    @ApiModelProperty("排行字段")
    private String rankField;
    
    /** 排序字段 */
    @ApiModelProperty("排序字段")
    private String orderField;
}