package com.redbook.dashboard.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Dashboard模块基础实体类
 * 所有Dashboard模块的实体类都应该继承此类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class DashBoardBaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** ID */
    @ApiModelProperty("ID")
    private Long id;
    
    /** 创建者 */
    @ApiModelProperty("创建者")
    private String createBy;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    /** 更新者 */
    @ApiModelProperty("更新者")
    private String updateBy;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    
    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;
    
    /** 状态（0正常 1停用） */
    @ApiModelProperty("状态（0正常 1停用）")
    private Integer status;
    
    /** 删除标志（0代表存在 1代表删除） */
    @ApiModelProperty("删除标志（0代表存在 1代表删除）")
    private Integer delFlag;
}