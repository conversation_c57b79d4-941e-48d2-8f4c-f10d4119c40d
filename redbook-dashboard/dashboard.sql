一、数据库地址
jdbc:mysql://*********:3306?useSSL=false&createDatabaseIfNotExist=true&characterEncoding=UTF-8
User:root
Password:Hss@redpad123
二、数据库结构
--创建学习域每日学习数据的分析表(new)
create table intelligence_user_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `user_id` varchar(50) comment '学生ID',
    `area_id` int comment   '区域ID',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `county_id` int comment '区县ID',
    `alias_id` int comment  '体验中心ID',
    `exclusive_shop_id` int comment '专卖店ID',
    `code` varchar(100) comment '专卖店编码（唯一）',
    `manager_id` int comment    '关联店长账号ID',
    `class_id` int comment  '班级ID',
    `person_name` varchar(50) comment   '代理商姓名', 
    `province` varchar(50) comment  '省份',
    `city` varchar(50) comment  '城市',
    `county` varchar(50) comment    '区域',
    `alias` varchar(50) comment '体验中心',
    `shop_name` varchar(50) comment '专卖店名称',
    `user_name` varchar(50) comment '学生姓名',
    `teacher_name` varchar(50) comment  '学管师姓名或签名',
    `stage_cn` varchar(50) comment  '学段',
    `class_name` varchar(50) comment    '班级',
    `class_concat_ws` varchar(100) comment  '班级别名拼接',
    `content_type` varchar(50) comment  '专项类型',
    `study_module` varchar(50) comment  '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "学生日期维度的平均学习数据"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域全国平均学习数据分析表
create table intelligence_country_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "全国平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域省份平均学习数据分析表
create table intelligence_province_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `province` varchar(50) comment  '省份',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "省份平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域城市平均学习数据分析表
create table intelligence_city_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `city` varchar(50) comment  '城市',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "城市平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域体验中心平均学习数据分析表
create table intelligence_alias_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `alias_id` int comment  '体验中心ID',
    `alias` varchar(50) comment '体验中心',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "体验中心平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域专卖店平均学习数据分析表
create table intelligence_shop_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `alias_id` int comment  '体验中心ID',
    `exclusive_shop_id` int comment '专卖店ID',
    `shop_name` varchar(50) comment '专卖店名称',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` floa一、数据库地址
jdbc:mysql://*********:3306?useSSL=false&createDatabaseIfNotExist=true&characterEncoding=UTF-8
User:root
Password:Hss@redpad123
二、数据库结构
--创建学习域每日学习数据的分析表(new)
create table intelligence_user_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `user_id` varchar(50) comment '学生ID',
    `area_id` int comment   '区域ID',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `county_id` int comment '区县ID',
    `alias_id` int comment  '体验中心ID',
    `exclusive_shop_id` int comment '专卖店ID',
    `code` varchar(100) comment '专卖店编码（唯一）',
    `manager_id` int comment    '关联店长账号ID',
    `class_id` int comment  '班级ID',
    `person_name` varchar(50) comment   '代理商姓名', 
    `province` varchar(50) comment  '省份',
    `city` varchar(50) comment  '城市',
    `county` varchar(50) comment    '区域',
    `alias` varchar(50) comment '体验中心',
    `shop_name` varchar(50) comment '专卖店名称',
    `user_name` varchar(50) comment '学生姓名',
    `teacher_name` varchar(50) comment  '学管师姓名或签名',
    `stage_cn` varchar(50) comment  '学段',
    `class_name` varchar(50) comment    '班级',
    `class_concat_ws` varchar(100) comment  '班级别名拼接',
    `content_type` varchar(50) comment  '专项类型',
    `study_module` varchar(50) comment  '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "学生日期维度的平均学习数据"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域全国平均学习数据分析表
create table intelligence_country_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "全国平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域省份平均学习数据分析表
create table intelligence_province_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `province` varchar(50) comment  '省份',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "省份平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域城市平均学习数据分析表
create table intelligence_city_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `city` varchar(50) comment  '城市',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "城市平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域体验中心平均学习数据分析表
create table intelligence_alias_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `alias_id` int comment  '体验中心ID',
    `alias` varchar(50) comment '体验中心',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "体验中心平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域专卖店平均学习数据分析表
create table intelligence_shop_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `alias_id` int comment  '体验中心ID',
    `exclusive_shop_id` int comment '专卖店ID',
    `shop_name` varchar(50) comment '专卖店名称',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "专卖店平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域班级平均学习数据分析表
create table intelligence_class_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `alias_id` int comment  '体验中心ID',
    `exclusive_shop_id` int comment '专卖店ID',
    `class_id` int comment  '班级ID',
    `class_name` varchar(50) comment    '班级',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "班级平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
t comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "专卖店平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--学习域班级平均学习数据分析表
create table intelligence_class_study_module_day(
    `date_key` int comment  '日期键',
    `year` varchar(10) comment  '年',
    `month` varchar(10) comment '月',
    `week_of_year` varchar(10) comment  '周',
    `study_date` varchar(10) comment    '日',
    `province_id` int comment   '省份ID',
    `city_id` int comment   '城市ID',
    `alias_id` int comment  '体验中心ID',
    `exclusive_shop_id` int comment '专卖店ID',
    `class_id` int comment  '班级ID',
    `class_name` varchar(50) comment    '班级',
    `content_type` varchar(50) comment '专项类型',
    `study_module` varchar(50) comment '模块',
    `quiz_avg_score` int comment    '平均学后成绩',
    `improve_score_rate` float comment  '平均提分完成率',
    `improve_score_mean` int comment    '平均提升分数',
    `study_efficiency_mean` float comment   '平均学习效率',
    `study_effect_time_day` int comment '日均有效时长(分钟）',
    `study_new_num_day` int comment '日均新学个数',
    `study_speed` int comment   '平均新学速度',
    `review_frequency_day` int comment  '日复习次数',
    `review_num_day` int comment    '日复习个数',
    `review_multiplier_mean` int comment    '平均复习倍数'
) comment "班级平均学习数据分析表"
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
