# Redbook Dashboard 模块

## 模块简介

`redbook-dashboard` 是小红本运营管理系统的数据看板模块，提供学习数据统计、分析和可视化功能。

## 目录结构

```
redbook-dashboard/
├── src/main/java/com/redbook/dashboard/
│   ├── controller/          # 控制器层
│   │   └── BaseDashboardController.java
│   ├── service/            # 服务层
│   │   ├── IDashboardService.java
│   │   └── impl/
│   │       └── BaseDashboardServiceImpl.java
│   ├── mapper/             # 数据访问层
│   │   └── BaseDashboardMapper.java
│   ├── domain/             # 实体类
│   │   ├── entity/         # 数据库实体
│   │   │   └── BaseEntity.java
│   │   ├── vo/             # 视图对象
│   │   │   └── BaseVO.java
│   │   └── dto/            # 数据传输对象
│   │       └── BaseDTO.java
│   ├── config/             # 配置类
│   │   └── DashboardConfig.java
│   └── enums/              # 枚举类
│       └── DashboardEnum.java
└── src/main/resources/
    └── mapper/dashboard/    # MyBatis映射文件
        └── BaseDashboardMapper.xml
```

## 核心功能

### 1. 基础架构

- **BaseDashboardController**: 提供通用的控制器基类，包含响应处理和参数验证
- **IDashboardService**: 定义通用的服务接口
- **BaseDashboardServiceImpl**: 提供通用的服务实现，包含CRUD操作
- **BaseDashboardMapper**: 定义通用的数据访问接口

### 2. 数据模型

- **BaseEntity**: 通用实体基类，包含公共字段和方法
- **BaseVO**: 通用视图对象基类，用于API响应
- **BaseDTO**: 通用数据传输对象基类，用于接收请求参数

### 3. 配置管理

- **DashboardConfig**: 数据看板配置类，管理缓存、分页、权限等配置
- **DashboardEnum**: 通用枚举类，定义状态、类型等常量

### 4. 数据访问

- **BaseDashboardMapper.xml**: 通用SQL映射文件，提供统计、汇总、趋势等查询模板

## 使用指南

### 1. 创建新的业务实体

继承 `BaseEntity` 类：

```java
@TableName("your_table_name")
public class YourEntity extends BaseEntity {
    // 业务字段
}
```

### 2. 创建VO类

继承 `BaseVO` 类：

```java
@ApiModel("您的业务VO")
public class YourVO extends BaseVO {
    // 业务字段
}
```

### 3. 创建DTO类

继承 `BaseDTO` 类：

```java
@ApiModel("您的业务DTO")
public class YourDTO extends BaseDTO {
    // 业务字段和验证注解
}
```

### 4. 创建Mapper接口

继承 `BaseDashboardMapper` 接口：

```java
@Mapper
public interface YourMapper extends BaseDashboardMapper<YourEntity> {
    // 自定义查询方法
}
```

### 5. 创建Service

继承 `BaseDashboardServiceImpl` 类：

```java
@Service
public class YourServiceImpl extends BaseDashboardServiceImpl<YourMapper, YourEntity> implements IYourService {
    // 业务逻辑实现
}
```

### 6. 创建Controller

继承 `BaseDashboardController` 类：

```java
@Api(tags = "您的业务控制器")
@RestController
@RequestMapping("/dashboard/your-module")
public class YourController extends BaseDashboardController {
    // API接口实现
}
```

## 配置说明

### 独立数据源配置
Dashboard模块使用完全独立的数据源配置，配置文件位于模块内部，与环境无关：

**配置文件位置：** `src/main/resources/application-dashboard.yml`

```yaml
spring:
  datasource:
    druid:
      dashboard:
        enabled: true
        url: *****************************************************************************************************************************************************************************************
        username: devel
        password: $JQbW8M306H
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 连接池配置
        initial-size: 5
        min-idle: 5
        max-active: 20
        # ... 更多配置
```

**优势：**
- 模块完全自包含，便于独立部署
- 配置与环境无关，dev和prod使用相同配置
- 便于模块级别的配置管理和调整

### 缓存配置
```yaml
dashboard:
  cache:
    enabled: true          # 是否启用缓存
    expire: 3600          # 缓存过期时间(秒)
    prefix: "dashboard:"   # 缓存前缀
```

### 分页配置
```yaml
dashboard:
  page:
    size: 20              # 默认分页大小
    maxSize: 100          # 最大分页大小
```

## 数据源配置

### 模块内独立数据源使用
Dashboard模块使用完全独立的数据源配置，通过模块内的配置文件自动加载：

```java
@Service
@DS("DASHBOARD") // 指定使用Dashboard独立数据源
public class DashboardStatisticsServiceImpl extends BaseDashboardServiceImpl<DashboardStatistics> {
    // 所有数据操作都会使用dashboard模块内配置的数据源
}
```

### 自动配置机制
模块通过以下机制实现自动配置：

1. **配置文件自动加载：** `application-dashboard.yml`
2. **自动配置类：** `DashboardAutoConfiguration`
3. **Spring Boot自动发现：** `META-INF/spring.factories`

### 数据库初始化
1. 创建独立的数据库：`redbook_dashboard`
2. 执行初始化脚本：`sql/redbook_dashboard.sql`
3. 模块会自动加载配置，无需手动配置

### 模块配置结构
```
redbook-dashboard/
├── src/main/resources/
│   ├── application-dashboard.yml     # 模块独立配置
│   └── META-INF/spring.factories     # 自动配置声明
└── src/main/java/.../config/
    ├── DashboardAutoConfiguration.java    # 自动配置类
    └── DashboardDataSourceConfig.java     # 数据源配置
```

## 注意事项

1. **数据权限**: 启用数据权限控制时，需要在查询条件中添加相应的权限过滤
2. **缓存策略**: 合理使用Redis缓存，避免缓存穿透和雪崩
3. **分页查询**: 大数据量查询时建议使用分页，避免内存溢出
4. **异步处理**: 耗时操作建议使用异步处理，提高响应速度
5. **SQL优化**: 复杂查询需要添加适当的索引，优化查询性能

## 扩展开发

1. 根据具体业务需求，创建对应的实体类、VO、DTO
2. 实现具体的业务逻辑和数据查询
3. 添加相应的API接口和Swagger文档
4. 编写单元测试和集成测试
5. 配置相应的权限和缓存策略

## 依赖说明

- Spring Boot 2.5.14
- MyBatis Plus
- Redis
- Swagger 3.0.0
- 项目内部依赖：redbook-common、redbook-system、redbook-framework