package com.redbook.service;

import com.redbook.system.domain.PostSaleUserAddress;

import java.util.List;

/**
 * 用户地址Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface IPostSaleUserAddressService 
{
    /**
     * 查询用户地址
     * 
     * @param id 用户地址主键
     * @return 用户地址
     */
     PostSaleUserAddress selectPostSaleUserAddressById(Long id);

    /**
     * 查询用户地址列表
     * 
     * @param postSaleUserAddress 用户地址
     * @return 用户地址集合
     */
     List<PostSaleUserAddress> selectPostSaleUserAddressList(PostSaleUserAddress postSaleUserAddress);

    /**
     * 新增用户地址
     * 
     * @param postSaleUserAddress 用户地址
     * @return 结果
     */
     int insertPostSaleUserAddress(PostSaleUserAddress postSaleUserAddress);

    /**
     * 修改用户地址
     * 
     * @param postSaleUserAddress 用户地址
     * @return 结果
     */
     int updatePostSaleUserAddress(PostSaleUserAddress postSaleUserAddress);

    /**
     * 批量删除用户地址
     * 
     * @param ids 需要删除的用户地址主键集合
     * @return 结果
     */
     int deletePostSaleUserAddressByIds(Long[] ids);

    /**
     * 删除用户地址信息
     * 
     * @param id 用户地址主键
     * @return 结果
     */
     int deletePostSaleUserAddressById(Long id);
}
