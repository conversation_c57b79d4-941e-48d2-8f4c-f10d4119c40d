package com.redbook.service.impl;

import java.util.List;
import com.redbook.common.utils.DateUtils;
import com.redbook.service.IPostSaleRefundOrderService;
import com.redbook.system.domain.pay.PostSaleRefundOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.redbook.system.mapper.PostSaleRefundOrderMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
/**
 * 统一退款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-14
 */
@Service
public class PostSaleRefundOrderServiceImpl extends ServiceImpl<PostSaleRefundOrderMapper, PostSaleRefundOrder> implements IPostSaleRefundOrderService
{
    @Autowired
    private PostSaleRefundOrderMapper postSaleRefundOrderMapper;

    /**
     * 查询统一退款
     * 
     * @param id 统一退款主键
     * @return 统一退款
     */
    @Override
    public PostSaleRefundOrder selectPostSaleRefundOrderById(Long id)
    {
        return postSaleRefundOrderMapper.selectPostSaleRefundOrderById(id);
    }

    @Override
    public PostSaleRefundOrder selectPostSaleRefundOrder(String refundOrderNo) {
        return postSaleRefundOrderMapper.selectPostSaleRefundOrder(refundOrderNo);

    }

    /**
     * 查询统一退款列表
     * 
     * @param postSaleRefundOrder 统一退款
     * @return 统一退款
     */
    @Override
    public List<PostSaleRefundOrder> selectPostSaleRefundOrderList(PostSaleRefundOrder postSaleRefundOrder)
    {
        return postSaleRefundOrderMapper.selectPostSaleRefundOrderList(postSaleRefundOrder);
    }

    /**
     * 新增统一退款
     * 
     * @param postSaleRefundOrder 统一退款
     * @return 结果
     */
    @Override
    public int insertPostSaleRefundOrder(PostSaleRefundOrder postSaleRefundOrder)
    {
        postSaleRefundOrder.setCreateTime(DateUtils.getNowDate());
        return postSaleRefundOrderMapper.insertPostSaleRefundOrder(postSaleRefundOrder);
    }

    /**
     * 修改统一退款
     * 
     * @param postSaleRefundOrder 统一退款
     * @return 结果
     */
    @Override
    public int updatePostSaleRefundOrder(PostSaleRefundOrder postSaleRefundOrder)
    {
        return postSaleRefundOrderMapper.updatePostSaleRefundOrder(postSaleRefundOrder);
    }

    /**
     * 批量删除统一退款
     * 
     * @param ids 需要删除的统一退款主键
     * @return 结果
     */
    @Override
    public int deletePostSaleRefundOrderByIds(Long[] ids)
    {
        return postSaleRefundOrderMapper.deletePostSaleRefundOrderByIds(ids);
    }

    /**
     * 删除统一退款信息
     * 
     * @param id 统一退款主键
     * @return 结果
     */
    @Override
    public int deletePostSaleRefundOrderById(Long id)
    {
        return postSaleRefundOrderMapper.deletePostSaleRefundOrderById(id);
    }
}
