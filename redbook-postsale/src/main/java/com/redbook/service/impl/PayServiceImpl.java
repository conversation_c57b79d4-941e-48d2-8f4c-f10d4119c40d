package com.redbook.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.redbook.common.constant.BizRedisConstants;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.config.WxPayConfig;
import com.redbook.postsaleapi.model.RefundResult;
import com.redbook.postsaleapi.model.WechatPayOrderBean;
import com.redbook.postsaleapi.model.WechatRefundOrderBean;
import com.redbook.service.*;
import com.redbook.system.domain.PostSaleGoodsOrder;
import com.redbook.system.domain.PostSaleRepairOrder;
import com.redbook.system.domain.dto.postSale.applet.PostSaleRepairOrderPayConfirmDto;
import com.redbook.system.domain.pay.PostSalePayCallbackLog;
import com.redbook.system.domain.pay.PostSalePayOrder;
import com.redbook.system.domain.pay.PostSaleRefundCallbackLog;
import com.redbook.system.domain.pay.PostSaleRefundOrder;
import com.redbook.system.mapper.PostSaleRepairOrderMapper;
import com.redbook.system.service.postSale.IPostSaleRepairOrderService;
import com.redbook.util.pay.PaySignUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.*;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.OffsetDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28 15:34
 */
@Service
public class PayServiceImpl implements PayService {

    @Resource
    private WxPayConfig wxPayConfig;
    @Autowired
    private IPostSalePayCallbackLogService payCallbackLogService;
    @Autowired
    private IPostSaleRefundCallbackLogService refundCallbackLogService;
    @Autowired
    private IPostSaleRefundOrderService refundOrderService;
    @Autowired
    private IPostSalePayOrderService payOrderService;
    @Autowired
    private IPostSaleGoodsOrderService goodsOrderService;
    @Autowired
    private IPostSalePayOrderService postSalePayOrderService;
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IPostSaleRepairOrderService postSaleRepairOrderService;
    @Autowired
    private IPostSaleUserCartItemService userCartItemService;
    @Autowired
    private PostSaleRepairOrderMapper postSaleRepairOrderMapper;
    @Override
    public String createOrder(WechatPayOrderBean payOrderBean) {
        //创建初始化订单
        //请求微信支付相关配置
        InputStream stream = ResourceUtil.getStream(wxPayConfig.getPrivateKeyPath());
        Config config =
                new RSAAutoCertificateConfig.Builder()
                        .merchantId(wxPayConfig.getMerchantId())
                        .privateKey(loadPrivateKey(stream))
                        .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber())
                        .apiV3Key(wxPayConfig.getApiV3Key())
                        .build();
        JsapiService service = new JsapiService.Builder().config(config).build();
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        //单位分
        amount.setTotal(payOrderBean.getTotalMoney().multiply(BigDecimal.valueOf(100)).intValue());
        request.setAmount(amount);
        request.setAppid(wxPayConfig.getAppId());
        request.setMchid(wxPayConfig.getMerchantId());
        request.setDescription(payOrderBean.getDescription());
        request.setNotifyUrl(wxPayConfig.getPayNotifyUrl());
        request.setOutTradeNo(payOrderBean.getOutTradeNo());
        Payer payer = new Payer();
        payer.setOpenid(payOrderBean.getOpenid());
        request.setPayer(payer);
        PrepayResponse response = service.prepay(request);
        System.out.println("请求预支付下单，请求参数："+ JSONObject.toJSONString(request));
        System.out.println("订单【{}】发起预支付成功，返回信息：{}"+ response);
        return response.getPrepayId();
    }

    @Override
    public Transaction getOrder(String outTradeNo) {
        InputStream stream = ResourceUtil.getStream(wxPayConfig.getPrivateKeyPath());
        Config config = new RSAAutoCertificateConfig.Builder()
                .merchantId(wxPayConfig.getMerchantId())
                .privateKey(loadPrivateKey(stream))
                .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber())
                .apiV3Key(wxPayConfig.getApiV3Key())
                .build();
        JsapiService service = new JsapiService.Builder().config(config).build();
        QueryOrderByOutTradeNoRequest queryRequest = new QueryOrderByOutTradeNoRequest();
        queryRequest.setMchid(wxPayConfig.getMerchantId());
        queryRequest.setOutTradeNo(outTradeNo);
        Transaction result = null;
        try {
            result = service.queryOrderByOutTradeNo(queryRequest);
        } catch (ServiceException e) {
            // API返回失败, 例如ORDER_NOT_EXISTS
            System.out.printf("code=[%s], message=[%s]\n", e.getErrorCode(), e.getErrorMessage());
            System.out.printf("reponse body=[%s]\n", e.getResponseBody());
        }
        return result;
    }

    @Override
    public void closeOrder(String outTradeNo) {
        InputStream stream = ResourceUtil.getStream(wxPayConfig.getPrivateKeyPath());
        Config config = new RSAAutoCertificateConfig.Builder()
                .merchantId(wxPayConfig.getMerchantId())
                .privateKey(loadPrivateKey(stream))
                .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber())
                .apiV3Key(wxPayConfig.getApiV3Key())
                .build();
        JsapiService service = new JsapiService.Builder().config(config).build();
        CloseOrderRequest closeRequest = new CloseOrderRequest();
        closeRequest.setMchid(wxPayConfig.getMerchantId());
        closeRequest.setOutTradeNo(outTradeNo);
        // 方法没有返回值，意味着成功时API返回204 No Content
        service.closeOrder(closeRequest);
    }





    /**
     * 读取请求数据流
     * @param request
     * @return
     */
    private String getRequestBody(HttpServletRequest request) {
        StringBuffer sb = new StringBuffer();
        try (ServletInputStream inputStream = request.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        ) {
            String line;

            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
        }
        return sb.toString();
    }

    @Override
    public void payCallback(HttpServletRequest request, HttpServletResponse response) {
        //获取报文
        String body = getRequestBody(request);
        //随机串
        String nonceStr = request.getHeader("Wechatpay-Nonce");
        //微信传递过来的签名
        String signature = request.getHeader("Wechatpay-Signature");
        //证书序列号（微信平台）
        String serialNo = request.getHeader("Wechatpay-Serial");
        //时间戳
        String timestamp = request.getHeader("Wechatpay-Timestamp");

        InputStream is = null;
        try {
            is = request.getInputStream();
            // 构造 RequestParam
            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(serialNo)
                    .nonce(nonceStr)
                    .signature(signature)
                    .timestamp(timestamp)
                    .body(body)
                    .build();

            InputStream stream = ResourceUtil.getStream(wxPayConfig.getPrivateKeyPath());
            RSAAutoCertificateConfig config = new RSAAutoCertificateConfig.Builder()
                    .merchantId(wxPayConfig.getMerchantId())
                    .privateKey(loadPrivateKey(stream)) // 商户私钥对象
                    .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber()) // 商户证书序列号
                    .apiV3Key(wxPayConfig.getApiV3Key()) // APIv3密钥
                    .build();
            NotificationParser parser = new NotificationParser(config);
            // 验签、解密并转换成 Transaction
            Transaction transaction = parser.parse(requestParam, Transaction.class);
            //记录日志信息
            Transaction.TradeStateEnum state = transaction.getTradeState();
            String openid = transaction.getPayer().getOpenid();
            //回调表
            PostSalePayCallbackLog postSalePayCallbackLog = new PostSalePayCallbackLog();
            postSalePayCallbackLog.setNotifyContent(transaction.toString());
            postSalePayCallbackLog.setStatus(0);

            if (!"SUCCESS".equals(state.toString())) {
                postSalePayCallbackLog.setStatus(2);
                postSalePayCallbackLog.setOrderNo(transaction.getOutTradeNo());
                postSalePayCallbackLog.setTransactionId(transaction.getTransactionId());
                payCallbackLogService.insertPostSalePayCallbackLog(postSalePayCallbackLog);
                return;
            }

            PostSalePayOrder postSalePayOrder = payOrderService.selectByBizOrderNo(transaction.getOutTradeNo());
            if(ObjectUtil.isNull(postSalePayOrder) || postSalePayOrder.getStatus() != 0){
                sendSuccessResponse(response);
                return;
            }

            //插入回调记录表
            postSalePayCallbackLog.setStatus(1);
            postSalePayCallbackLog.setOrderNo(transaction.getOutTradeNo());
            postSalePayCallbackLog.setTransactionId(transaction.getTransactionId());
            payCallbackLogService.insertPostSalePayCallbackLog(postSalePayCallbackLog);

            //更新统一支付表
            postSalePayOrder.setStatus(1);
            postSalePayOrder.setTransactionId(transaction.getTransactionId());
            postSalePayOrder.setPayTime(convertWeChatTimeToDate(transaction.getSuccessTime()));
            payOrderService.updatePostSalePayOrder(postSalePayOrder);
            // 根据业务类型处理具体逻辑
            handleBusinessType(postSalePayOrder, transaction);

            //通知微信回调成功
            sendSuccessResponse(response);
        } catch (Exception e) {
            sendErrorResponse(response,"签名失败");
            e.printStackTrace();
        }finally {
            try {
                is.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

    }

    @Override
    public Refund getRefundOrder(String refundOrderNo) {

        InputStream stream = ResourceUtil.getStream(wxPayConfig.getPrivateKeyPath());
        Config config = new RSAAutoCertificateConfig.Builder()
                .merchantId(wxPayConfig.getMerchantId())
                .privateKey(loadPrivateKey(stream))
                .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber())
                .apiV3Key(wxPayConfig.getApiV3Key())
                .build();
        RefundService refundService = new RefundService.Builder().config(config).build();
        QueryByOutRefundNoRequest request = new QueryByOutRefundNoRequest();
        request.setOutRefundNo(refundOrderNo);
        Refund refund = refundService.queryByOutRefundNo(request);
        return refund;
    }

    @Override
    public RefundResult refunds(WechatRefundOrderBean refundOrderBean) {
        InputStream stream = ResourceUtil.getStream(wxPayConfig.getPrivateKeyPath());
        Config config = new RSAAutoCertificateConfig.Builder()
                .merchantId(wxPayConfig.getMerchantId())
                .privateKey(PaySignUtil.loadPrivateKey(stream))
                .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber())
                .apiV3Key(wxPayConfig.getApiV3Key())
                .build();
        RefundService refundService = new RefundService.Builder().config(config).build();
        CreateRequest request = new CreateRequest();
        request.setOutTradeNo(refundOrderBean.getOutTradeNo());
        request.setOutRefundNo(refundOrderBean.getOutRefundNo());
        request.setReason("退款");
        request.setNotifyUrl(wxPayConfig.getRefundNotifyUrl());
        AmountReq amountReq = new AmountReq();
        amountReq.setRefund(refundOrderBean.getRefundAmount().multiply(BigDecimal.valueOf(100)).longValue());
        amountReq.setCurrency("CNY");
        amountReq.setTotal(refundOrderBean.getTotalAmount().multiply(BigDecimal.valueOf(100)).longValue());
        request.setAmount(amountReq);
        Refund refund = refundService.create(request);
        RefundResult refundResult = new RefundResult();
        BeanUtils.copyProperties(refund,refundResult);
        refundResult.setStatus(refund.getStatus().toString());
        com.wechat.pay.java.service.refund.model.Amount amount = refund.getAmount();
        refundResult.setTotal(amount.getTotal());
        refundResult.setRefund(amount.getRefund());
        refundResult.setPayerTotal(amount.getPayerTotal());
        refundResult.setPayerRefund(amount.getPayerRefund());
        return refundResult;
    }


    @Override
    public void refundsCallback(HttpServletRequest request, HttpServletResponse response) {
        //获取报文
        String body = getRequestBody(request);
        //随机串
        String nonceStr = request.getHeader("Wechatpay-Nonce");
        //微信传递过来的签名
        String signature = request.getHeader("Wechatpay-Signature");
        //证书序列号（微信平台）
        String serialNo = request.getHeader("Wechatpay-Serial");
        //时间戳
        String timestamp = request.getHeader("Wechatpay-Timestamp");

        InputStream is = null;
        try {
            is = request.getInputStream();
            // 构造 RequestParam
            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(serialNo)
                    .nonce(nonceStr)
                    .signature(signature)
                    .timestamp(timestamp)
                    .body(body)
                    .build();

            InputStream stream = ResourceUtil.getStream(wxPayConfig.getPrivateKeyPath());
            RSAAutoCertificateConfig config = new RSAAutoCertificateConfig.Builder()
                    .merchantId(wxPayConfig.getMerchantId())
                    .privateKey(loadPrivateKey(stream)) // 商户私钥对象
                    .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber()) // 商户证书序列号
                    .apiV3Key(wxPayConfig.getApiV3Key()) // APIv3密钥
                    .build();
            NotificationParser parser = new NotificationParser(config);
            // 验签、解密并转换成 Refund
            Refund refund = parser.parse(requestParam, Refund.class);
            //记录日志信息
            Status state = refund.getStatus();
            //回调表
            PostSaleRefundCallbackLog refundCallbackLog = new PostSaleRefundCallbackLog();
            refundCallbackLog.setNotifyContent(refund.toString());
            refundCallbackLog.setStatus(0);

            //统一退款表
            PostSaleRefundOrder postSaleRefundOrder = refundOrderService.selectPostSaleRefundOrder(refund.getOutRefundNo());

            if(ObjectUtil.isNull(postSaleRefundOrder) || postSaleRefundOrder.getStatus() == 2){
                sendSuccessResponse(response);
                return;
            }

            //插入回调记录表
            refundCallbackLog.setStatus(1);
            refundCallbackLog.setOrderNo(refund.getOutTradeNo());
            refundCallbackLog.setRefundId(refund.getRefundId());
            refundCallbackLogService.insertPostSaleRefundCallbackLog(refundCallbackLog);

            //更新统一退款表
            postSaleRefundOrder.setStatus(1);
            postSaleRefundOrder.setRefundSuccessTime(convertWeChatTimeToDate(refund.getSuccessTime()));
            postSaleRefundOrder.setRefundId(refund.getRefundId());
            refundOrderService.updatePostSaleRefundOrder(postSaleRefundOrder);
            // 根据业务类型处理具体逻辑
            handleRefundBusinessType(postSaleRefundOrder, refund);

            //通知微信回调成功
            sendSuccessResponse(response);
        } catch (Exception e) {
            sendErrorResponse(response,"签名失败");
            e.printStackTrace();
        }finally {
            try {
                is.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void handleBusinessType(PostSalePayOrder payOrder, Transaction transaction) {
        String bizType = payOrder.getBizType();
        if ("SHOP".equals(bizType)) {
            PostSaleGoodsOrder goodsOrder = new PostSaleGoodsOrder();
            goodsOrder.setOrderNo(payOrder.getBizOrderNo());
            goodsOrder.setPaymentSerialNo(payOrder.getTransactionId());
            goodsOrder.setOrderStatus(0);
            goodsOrder.setPayMethod(1);
            goodsOrder.setBankType(transaction.getBankType());
            goodsOrder.setPayTime(convertWeChatTimeToDate(transaction.getSuccessTime()));
            goodsOrderService.updateOrder(goodsOrder);
            //清空购物车
            userCartItemService.deleteByUserId(Integer.parseInt(payOrder.getCreateBy()));
        } else if ("REPAIR".equals(bizType)) {
            PostSaleRepairOrderPayConfirmDto confirmDto = redisCache.getCacheObject(BizRedisConstants.PAY_CONFIRM_REPAIR_ORDER + payOrder.getBizOrderNo());
            confirmDto.setPayTime(convertWeChatTimeToDate(transaction.getSuccessTime()));
            Boolean aBoolean = postSaleRepairOrderService.payConfirm(confirmDto,payOrder.getId());
            if(aBoolean){
                redisCache.deleteObject(BizRedisConstants.PAY_CONFIRM_REPAIR_ORDER + payOrder.getBizOrderNo());
            }
        }
    }


    private void handleRefundBusinessType(PostSaleRefundOrder postSaleRefundOrder , Refund refund) {
        String bizType = postSaleRefundOrder.getBizType();
        if ("SHOP".equals(bizType)) {
            PostSaleGoodsOrder goodsOrder = new PostSaleGoodsOrder();
            goodsOrder.setId(postSaleRefundOrder.getBizOrderId());
            goodsOrder.setOrderStatus(2);
            goodsOrder.setRefundStatus(4);
            goodsOrder.setRefundMoney(new BigDecimal(refund.getAmount().getRefund()).divide(new BigDecimal(100)));
            goodsOrderService.updatePostSaleGoodsOrder(goodsOrder);
        } else if ("REPAIR".equals(bizType)) {
            PostSaleRepairOrder postSaleRepairOrder = new PostSaleRepairOrder();
            postSaleRepairOrder.setId(postSaleRefundOrder.getBizOrderId().intValue());
            postSaleRepairOrder.setRefundStatus(2);
            postSaleRepairOrderMapper.updatePostSaleRepairOrder(postSaleRepairOrder);
        }
    }

    public Date convertWeChatTimeToDate(String weChatTime) {
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(weChatTime);
        return Date.from(offsetDateTime.toInstant());
    }

    private void sendSuccessResponse(HttpServletResponse response) {
        try {
            response.setStatus(200);
            response.setContentType("application/json");
            response.getWriter().write("{\"code\":\"SUCCESS\",\"message\":\"SUCCESS\"}");
        } catch (Exception ex) {
            System.out.println("发送错误响应失败");
        }
    }

    private void sendErrorResponse(HttpServletResponse response, String message) {
        try {
            response.setStatus(500);
            response.setContentType("application/json");
            response.getWriter().write("{\"code\":\"FAIL\",\"message\":\""+message+"\"}");
        } catch (Exception ex) {
            System.out.println("发送错误响应失败");
        }
    }

    @Override
    public void cancel(String appletUserId) {
        List<PostSalePayOrder> postSalePayOrders = payOrderService.listByCreateUser(appletUserId);
        if(CollectionUtil.isNotEmpty(postSalePayOrders)){
            for(PostSalePayOrder order : postSalePayOrders){
                //微信关单
                closeOrder(order.getBizOrderNo());
                //删除系统订单
                goodsOrderService.deleteByOrderNo(order.getBizOrderNo());
                //统一支付单更新关闭状态
                payOrderService.closeOrder(order.getBizOrderNo());
            }
        }

    }


    private static PrivateKey loadPrivateKey(InputStream inputStream) {
        try {
            ByteArrayOutputStream array = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                array.write(buffer, 0, length);
            }
            String privateKey = array.toString("utf-8").replace("-----BEGIN PRIVATE KEY-----", "")
                    .replace("-----END PRIVATE KEY-----", "").replaceAll("\\s+", "");
            KeyFactory kf = KeyFactory.getInstance("RSA");
            return kf.generatePrivate(new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey)));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("当前Java环境不支持RSA", e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("无效的密钥格式");
        } catch (IOException e) {
            throw new RuntimeException("无效的密钥");
        }
    }





}
