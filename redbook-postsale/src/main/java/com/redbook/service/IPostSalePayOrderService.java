package com.redbook.service;

import com.redbook.system.domain.pay.PostSalePayOrder;

import java.util.List;

/**
 * 统一支付Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-01
 */
public interface IPostSalePayOrderService 
{
    /**
     * 查询统一支付
     * 
     * @param id 统一支付主键
     * @return 统一支付
     */
     PostSalePayOrder selectPostSalePayOrderById(Long id);
     PostSalePayOrder selectByBizOrderNo(String bizOrderNo);
    List<PostSalePayOrder> listByCreateUser(String createBy);

    /**
     * 查询统一支付列表
     * 
     * @param postSalePayOrder 统一支付
     * @return 统一支付集合
     */
     List<PostSalePayOrder> selectPostSalePayOrderList(PostSalePayOrder postSalePayOrder);

    /**
     * 新增统一支付
     * 
     * @param postSalePayOrder 统一支付
     * @return 结果
     */
     int insertPostSalePayOrder(PostSalePayOrder postSalePayOrder);

    /**
     * 修改统一支付
     * 
     * @param postSalePayOrder 统一支付
     * @return 结果
     */
     int updatePostSalePayOrder(PostSalePayOrder postSalePayOrder);
     int closeOrder(String bizOrderNo);

    /**
     * 批量删除统一支付
     * 
     * @param ids 需要删除的统一支付主键集合
     * @return 结果
     */
     int deletePostSalePayOrderByIds(Long[] ids);

    /**
     * 删除统一支付信息
     * 
     * @param id 统一支付主键
     * @return 结果
     */
     int deletePostSalePayOrderById(Long id);
}
