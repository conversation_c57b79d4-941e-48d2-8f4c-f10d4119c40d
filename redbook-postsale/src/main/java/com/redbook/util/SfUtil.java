package com.redbook.util;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.sf.csim.express.service.CallExpressServiceTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Component
@Slf4j
public class SfUtil {

    /**
     * 顺丰快递：
     * 顾客编码:HBKJmFrLQmD
     * 沙箱校验码:Ce6pSpCRNVmfIjUsf0IwirVu2S8EZeAp
     * 生产校验码:MXsPqhBQJxJMSl8lbVK5CRqOoSEZD3to
     * https://qiao.sf-express.com/developSupport/195960?activeIndex=149495
     */

    @Value("${sf.call.url}")
    private String sfCallUrl;

    @Value("${sf.client.code}")
    private String sfClientCode;

    @Value("${sf.check.word}")
    private String sfCheckWord;
    private final String EXP_RECE_CREATE_ORDER = "EXP_RECE_CREATE_ORDER";

    /**
     * 订单结果查询接口serviceCode
     */
    private final String EXP_RECE_SEARCH_ROUTES = "EXP_RECE_SEARCH_ROUTES";

    /*
     * 订单取消\确认接口 serviceCode
     * */
    private final String EXP_RECE_UPDATE_ORDER = "EXP_RECE_UPDATE_ORDER";

    private final String API_RESULT_CODE_A1000 = "A1000";

    private final String ERROR_CODE_S0000 = "S0000";

    // 重复下单
    private final String ERROR_CODE_8016 = "8016";

    private final String LANGUAGE_ZHCN = "zh-cn";

    private final String One = "1";

    public String queryOrder(String orderId,String phone) {
        // 封装数据
        Map params = new HashMap();
        String timeStamp = String.valueOf(System.currentTimeMillis());
        JSONObject jSONObjectParam = new JSONObject();
        jSONObjectParam.put("trackingType", 1);
        jSONObjectParam.put("language", LANGUAGE_ZHCN);
        jSONObjectParam.put("trackingNumber", orderId);
        jSONObjectParam.put("checkPhoneNo", phone);
        String msgData = JSONObject.toJSONString(jSONObjectParam);

        params.put("partnerID", sfClientCode);
        params.put("requestID", UUID.randomUUID().toString().replace("-", ""));
        params.put("serviceCode", EXP_RECE_SEARCH_ROUTES);
        params.put("timestamp", timeStamp);
        params.put("msgData", msgData);
        String result = "";
        try {
            params.put("msgDigest", CallExpressServiceTools.getMsgDigest(msgData, timeStamp, sfCheckWord));
            log.info("调用丰桥订单结果查询接口开始, 入参:{}, sfCallUrl:{}, sfClientCode:{}, sfCheckWord:{}", params.get("msgData"), sfCallUrl, sfClientCode, sfCheckWord);
            result = HttpUtil.post(sfCallUrl, params);
        } catch (Exception e) {
            log.error("调用丰桥订单结果查询接口异常", e);
        }
        log.info("调用丰桥订单结果查询接口结束, 出参:{}", result);
        return result;
    }


}
