package com.redbook.dto.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * @Author: qwy
 * @Date: 2019/4/1 15:16
 * @Version 1.0
 */
@ApiModel(reference = "AppletUserCheckAuthorizeRequestDto")
public class AppletUserCheckAuthorizeRequestDto {

    @ApiModelProperty(value = "微信用户所在端唯一标识", required = true)
    private String openId;

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
