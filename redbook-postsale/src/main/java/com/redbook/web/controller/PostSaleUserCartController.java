package com.redbook.web.controller;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.config.aop.RequestLimit;
import com.redbook.service.IPostSaleUserCartItemService;
import com.redbook.service.PayService;
import com.redbook.system.domain.PostSaleGoodsOrder;
import com.redbook.system.domain.PostSaleUserCartItem;
import com.redbook.util.wehChat.JwtTokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 购物车Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/applet/userCart")
@Api(tags = "小程序-购物车")
public class PostSaleUserCartController extends BaseController
{

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    HttpServletRequest request;
    @Autowired
    private IPostSaleUserCartItemService postSaleUserCartItemService;
    @Autowired
    private PayService payService;
    /**
     * 查询购物车列表
     */
    @GetMapping("/list")
    @ApiOperation("查询购物车列表")
    public TableDataInfo<PostSaleUserCartItem> list()
    {
        String appletUserId = jwtTokenUtil.getUserIdFromRequest(request);
        startPage();
        List<PostSaleUserCartItem> list = postSaleUserCartItemService.selectPostSaleUserCartItemList(Integer.valueOf(appletUserId));
        return getDataTable(list);
    }

    /**
     * 新增购物车
     */
    @ApiOperation("新增购物车")
    @Log(title = "购物车", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostSaleUserCartItem postSaleUserCartItem)
    {
        String appletUserId = jwtTokenUtil.getUserIdFromRequest(request);
        return toAjax(postSaleUserCartItemService.insertPostSaleUserCartItem(Integer.valueOf(appletUserId),postSaleUserCartItem));
    }

    /**
     * 提交购物车
     */
    @ApiOperation("提交购物车")
    @Log(title = "提交购物车", businessType = BusinessType.INSERT)
    @PostMapping("/submit")
    @RequestLimit
    public AjaxResult<Map<String, Object>> submit(@RequestBody PostSaleGoodsOrder postSaleGoodsOrder)
    {
        String appletUserId = jwtTokenUtil.getUserIdFromRequest(request);
        return AjaxResult.success("成功",postSaleUserCartItemService.submit(Integer.valueOf(appletUserId),postSaleGoodsOrder));
    }

    /**
     * 删除购物车
     */
    @ApiOperation("删除购物车")
    @Log(title = "购物车", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        String appletUserId = jwtTokenUtil.getUserIdFromRequest(request);
        return toAjax(postSaleUserCartItemService.deletePostSaleUserCartItemByIds(Integer.valueOf(appletUserId),ids));
    }

    /**
     * 提交购物车之后，未支付，立即取消订单
     * @param request
     * @throws Exception
     */
    @ApiOperation("未支付取消订单")
    @PostMapping("/cancelOrder")
    public void cancel(HttpServletRequest request){
        String appletUserId = jwtTokenUtil.getUserIdFromRequest(request);
        payService.cancel(appletUserId);
    }


}
