package com.redbook.web.controller;

import com.redbook.common.annotation.Log;
import com.redbook.common.constant.PostSaleConstants;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.config.aop.RequestLimit;
import com.redbook.postsaleapi.service.WechatPayService;
import com.redbook.service.IPostSalePayOrderService;
import com.redbook.system.domain.Model;
import com.redbook.system.domain.PostSaleRepairOrder;
import com.redbook.system.domain.PostSaleRepairOrderItemMessage;
import com.redbook.system.domain.dto.postSale.applet.PostSaleHandleExceptionDto;
import com.redbook.system.domain.dto.postSale.applet.PostSaleRepairOrderItemListDto;
import com.redbook.system.domain.dto.postSale.applet.PostSaleRepairOrderPayDto;
import com.redbook.system.domain.pay.PostSalePayOrder;
import com.redbook.system.service.IModelService;
import com.redbook.system.service.postSale.IPostSaleRepairOrderService;
import com.redbook.util.wehChat.JwtTokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/postSale/applet/repairOrder/")
@Api(tags = "小程序-寄修单")
public class AppletRepairOrderController extends BaseController {
    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    HttpServletRequest request;
    @Autowired
    private IPostSaleRepairOrderService postSaleRepairOrderService;
    @Autowired
    private IModelService modelService;
    @Autowired
    private IPostSalePayOrderService payOrderService;
    @Autowired
    private WechatPayService wechatPayService;

    @PostMapping("add")
    @ApiOperation("添加寄修单")
    @RequestLimit
    public AjaxResult<Boolean> add(@RequestBody @ApiParam(value = "请创建PostSaleRepairOrder 对象进行传参", required = true) PostSaleRepairOrder postSaleRepairOrder) {
        return AjaxResult.success(postSaleRepairOrderService.insertPostSaleRepairOrder(postSaleRepairOrder, jwtTokenUtil.getUserIdFromRequest(request), PostSaleConstants.USER_TYPE_APPLET) > 0);
    }
    @ApiOperation("修改寄修单")
    @Log(title = "寄修单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostSaleRepairOrder postSaleRepairOrder) {
        return AjaxResult.success(postSaleRepairOrderService.updatePostSaleRepairOrder(postSaleRepairOrder, jwtTokenUtil.getUserIdFromRequest(request), PostSaleConstants.USER_TYPE_APPLET));
    }
    @ApiOperation("异常处理")
    @PostMapping("handleException")
    public AjaxResult handleException(@RequestBody PostSaleHandleExceptionDto postSaleHandleExceptionDto) {
        return AjaxResult.success(postSaleRepairOrderService.handleException(postSaleHandleExceptionDto));
    }

    @ApiOperation("评价")
    @PostMapping("evaluate")
    public AjaxResult evaluate(@ApiParam(value = "寄修单id") @RequestParam Integer orderId, @ApiParam(value = "星级") @RequestParam Integer star) {
        return AjaxResult.success(postSaleRepairOrderService.evaluate(orderId, star));
    }

    @PostMapping("modelList")
    @ApiOperation("型号列表")
    public AjaxResult<List<Model>> modelList(@RequestBody @ApiParam(value = "请创建Model 对象进行传参", required = true) Model model) {
        model.setStatus("0");
        return AjaxResult.success(modelService.selectModelList(model));
    }

    @ApiOperation("寄修单条目留言")
    @PostMapping(value = "addItemMessage")
    public AjaxResult addItemMessage(@RequestBody PostSaleRepairOrderItemMessage postSaleRepairOrderItemMessage) {
        return AjaxResult.success(postSaleRepairOrderService.addItemMessage(postSaleRepairOrderItemMessage, jwtTokenUtil.getUserIdFromRequest(request), PostSaleConstants.USER_TYPE_APPLET) > 0);
    }

    @ApiOperation("根据寄修单条目id查询留言备注信息")
    @GetMapping(value = "itemMessage/{itemId}")
    public AjaxResult getItemMessage(@PathVariable("itemId") Integer itemId) {
        return AjaxResult.success(postSaleRepairOrderService.getItemMessage(itemId, jwtTokenUtil.getUserIdFromRequest(request), PostSaleConstants.USER_TYPE_APPLET));
    }

    @PostMapping("itemList")
    @ApiOperation("查询寄修单条目列表")
    public AjaxResult itemList(@RequestBody PostSaleRepairOrderItemListDto postSaleRepairOrderItemListDto) {
        return AjaxResult.success(postSaleRepairOrderService.selectPostSaleRepairOrderItemList(postSaleRepairOrderItemListDto, jwtTokenUtil.getUserIdFromRequest(request), PostSaleConstants.USER_TYPE_APPLET));
    }

    @PostMapping("itemDetailInfo")
    @ApiOperation("查询寄修单条目详情")
    public AjaxResult itemDetailInfo(@ApiParam(value = "寄修单id") @RequestParam Integer orderId, @ApiParam(value = "寄修单条目id") @RequestParam Integer itemId) {
        return AjaxResult.success(postSaleRepairOrderService.selectPostSaleRepairOrderById(orderId));
    }


    @PostMapping("pay")
    @ApiOperation("支付")
    public AjaxResult pay(@RequestBody @ApiParam(value = "请创建PostSaleRepairOrderPayDto 对象进行传参", required = true) PostSaleRepairOrderPayDto postSaleRepairOrderPayDto) {
        return AjaxResult.success(postSaleRepairOrderService.pay(postSaleRepairOrderPayDto, jwtTokenUtil.getUserIdFromRequest(request), PostSaleConstants.USER_TYPE_APPLET));
    }

    /**
     * 生成支付订单，调起收银台，前端查询用户实际支付状态
     *
     * @param orderNo
     * @return
     */
    @GetMapping("/getPayStatus")
    @ApiOperation("获取支付状态")
    public AjaxResult getPayStatus(@RequestParam("orderNo") String orderNo) {
        PostSalePayOrder postSalePayOrder = payOrderService.selectByBizOrderNo(orderNo);
        if (postSalePayOrder == null || postSalePayOrder.getStatus() != 1) {
            return AjaxResult.success(false);
        }
        return AjaxResult.success(true);
    }

    /**
     * 关闭微信系统中的订单状态
     *
     * @param orderNo
     * @return
     */
    @PostMapping("/closePayOrder")
    @ApiOperation("关闭微信支付订单")
    public AjaxResult closePayOrder(@ApiParam(value = "支付单号") @RequestParam("orderNo") String orderNo) {
        Boolean aBoolean = wechatPayService.closeOrder(orderNo);
        return AjaxResult.success(aBoolean);
    }


    @PostMapping("/expressCompanyList")
    @ApiOperation("快递公司列表")
    public AjaxResult expressCompanyList(@ApiParam(value = "类型 1：上门取件 2：自主寄回") @RequestParam Integer type) {
        return AjaxResult.success(postSaleRepairOrderService.expressCompanyList(type));
    }
    @PostMapping("logisticsInquiry")
    @ApiOperation("物流查询")
    public AjaxResult logisticsInquiry(@ApiParam(value = "快递公司id") @RequestParam Integer expressCompanyId, @ApiParam(value = "快递单号") @RequestParam String expressNo) {
        return AjaxResult.success(postSaleRepairOrderService.logisticsInquiry(expressCompanyId, expressNo));
    }

    @PostMapping("tabletInfoBySn")
    @ApiOperation("根据sn码或者设备信息")
    public AjaxResult tabletInfoBySn(@ApiParam(value = "sn码") @RequestParam String sn) {
        return AjaxResult.success(postSaleRepairOrderService.tabletInfoBySn(sn));
    }
}
