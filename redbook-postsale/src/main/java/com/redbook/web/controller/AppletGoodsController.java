package com.redbook.web.controller;

import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.system.domain.PostSaleGoods;
import com.redbook.system.domain.Product;
import com.redbook.system.service.IProductService;
import com.redbook.system.service.postSale.IPostSaleGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 商品Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/applet/goods")
@Api(tags = "小程序-配件商品")
public class AppletGoodsController extends BaseController
{
    @Autowired
    private IPostSaleGoodsService postSaleGoodsService;
    @Autowired
    private IProductService productService;
    /**
     * 查询商品列表
     */
    @GetMapping("/list")
    @ApiOperation("查询商品列表")
    public TableDataInfo<Product> list(Product product )
    {
        product.setShowSystemType("postSale");
        startPage();
        List<Product> products = productService.selectProductList(product);
        return getDataTable(products);
    }

    /**
     * 获取商品详细信息
     */
    @ApiOperation("获取商品详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(productService.selectProductById(id));
    }


}
