package com.redbook.common.exception;

import lombok.*;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public final class ServiceException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     * <p>
     * 和 {@link CommonResult#getDetailMessage()} 一致的设计
     */
    private String detailMessage;

    private Exception e;


    public synchronized static ServiceException fail(String msg) {
        return ServiceException.builder()
                .code(500)
                .message(msg)
                .build();
    }

    public synchronized static ServiceException fail(String msg, Exception e) {
        return ServiceException.builder()
                .code(500)
                .message(msg)
                .detailMessage(e.getMessage())
                .e(e)
                .build();
    }

    public synchronized static ServiceException fail(Integer code, String msg, Exception e) {
        return ServiceException.builder()
                .code(code)
                .message(msg)
                .detailMessage(e.getMessage())
                .e(e)
                .build();
    }

    public static Exception fail(String msg, int code) {
        return ServiceException.builder()
                .code(code)
                .message(msg)
                .build();
    }

    public ServiceException( String message,Integer code) {
        this.code = code;
        this.message = message;
    }

    public ServiceException(String message) {
        this.message = message;
    }
}