package com.redbook.common.utils.bean.weChat;

/**
 * @Description: 票据
 * @Author: 18372
 * @Date: 2020/4/24 14:26
 * @Version: 1.0
 */
public class Ticket extends BaseErrorEntity {
    /**
     * 临时票据，用于在获取授权链接时作为参数传入
     */
    private String ticket;

    /**
     * ticket 的有效期，一般为 7200 秒
     */
    private int expires_in;

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }

    public int getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(int expires_in) {
        this.expires_in = expires_in;
    }

    @Override
    public String toString() {
        return "Ticket{" +
            "ticket='" + ticket + '\'' +
            ", expires_in=" + expires_in +
            '}';
    }
}
