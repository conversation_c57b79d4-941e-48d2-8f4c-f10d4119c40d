package com.redbook.common.utils;

import java.text.NumberFormat;
import java.util.Random;
import java.util.UUID;

/**
 * @description:
 */
public class IdentityGenerator {

    public static String nextIdentity() {

        return nextIdentity("");
    }

    public static String nextIdentity(String prefix) {
        return prefix + UUID.randomUUID().toString().trim().replaceAll("-", "");
    }


    public static String uuid() {
        return UUID.randomUUID().toString().trim().replaceAll("-", "");
    }




    public static String randomPassword(int several) {
        String password = "";
        for (int i = 0; i < several; i++) {
            Random ran = new Random();
            int nextInt = ran.nextInt(62);
            password += thick(nextInt);
        }


        String s = "(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])[0-9a-zA-Z]{8,}";
        boolean b = password.matches(s);
        if (b) {
            return password;
        }
        return randomPassword(several);
    }

    public static char thick(int in) {
        char[] chars = {'1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l',
                'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x',
                'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
                'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
                'W', 'X', 'Y', 'Z'};
        return chars[in];
    }


    public static String randomUppercase(int several) {
        String password = "";
        for (int i = 0; i < several; i++) {
            Random ran = new Random();
            int nextInt = ran.nextInt(36);
            password += thick2(nextInt);
        }


        String s = "(?=.*[A-Z])(?=.*[0-9])[0-9A-Z]{8,}";
        boolean b = password.matches(s);
        if (b) {
            return password;
        }
        return randomPassword(several);
    }

    public static char thick2(int in) {
        char[] chars = {'1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
                'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
                'W', 'X', 'Y', 'Z'};
        return chars[in];
    }

    public static String intToTtring(int i, int n) {

        NumberFormat nf = NumberFormat.getInstance();

        nf.setGroupingUsed(false);

        nf.setMinimumIntegerDigits(n);
        return nf.format(i);
    }

    public static String randomString(int several)
    {
        String randomNum = "";
        for (int i = 0; i < several; i++) {
            Random ran = new Random();
            int nextInt = ran.nextInt(9);
            randomNum += thick3(nextInt);
        }

        return randomNum;
    }

    public static char thick3(int in) {
        char[] chars = {'0', '1', '2', '3', '5', '6', '7', '8', '9'};//, '4'
        return chars[in];
    }

}
