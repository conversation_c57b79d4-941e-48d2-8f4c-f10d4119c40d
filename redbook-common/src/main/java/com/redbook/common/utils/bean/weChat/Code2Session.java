package com.redbook.common.utils.bean.weChat;

/**
 * @Description: 登录凭证校验返回值
 * @Author: 乔伟亚
 * @Date: 2020/4/24 14:08
 * @Version: 1.0
 */
public class Code2Session extends BaseErrorEntity {
    /**
     * 用户唯一标识
     */
    private String openid;

    /**
     * 会话密钥
     */
    private String session_key;

    /**
     * 用户在开放平台的唯一标识符
     */
    private String unionid;


    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getSession_key() {
        return session_key;
    }

    public void setSession_key(String session_key) {
        this.session_key = session_key;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    @Override
    public String toString() {
        return "Code2Session{" +
            "openid='" + openid + '\'' +
            ", session_key='" + session_key + '\'' +
            ", unionid='" + unionid + '\'' +
            '}';
    }
}
