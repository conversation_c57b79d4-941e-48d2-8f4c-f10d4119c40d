package com.redbook.common.config.weChat;

/**
 * @Description: 微信请求的url
 * @Author: 乔伟亚
 * @Date: 2020/4/24 11:01
 * @Version: 1.0
 */
public class WeChatUrls {

    private static String HOST = "https://api.weixin.qq.com/";

    private static String HOST_MP = "https://mp.weixin.qq.com/";
    /**
     * 基础支持,获取access_token,该access_token用于调用其他接口
     */
    public static String GET_ACCESS_TOKEN_BASIC = HOST + "cgi-bin/token";

    /**
     * 获取的是特殊的网页授权access_token
     */
    public static String GET_ACCESS_TOKEN_BY_CODE = HOST + "sns/oauth2/access_token";

    /**
     * 检验授权凭证（access_token）是否有效
     */
    public static String CHECK_ACCESS_TOKEN = HOST + "sns/auth";

    public static String GET_MEDIAID = HOST + "cgi-bin/media/upload?access_token=";

    /**
     * 刷新refresh_token
     */
    public static String REFRESH_TOKEN = HOST + "sns/oauth2/refresh_token";
    /**
     * 登录凭证校验
     */
    public static String CODE2SESSION = HOST + "sns/jscode2session";
    /**
     * 拉取用户信息
     */
    public static String GET_USERINFO = HOST + "sns/userinfo";
    /**
     * 此接口也是获取用户信息，但是需要关注公众号
     */
    public static String GET_USERINFO_2 = HOST + "cgi-bin/user/info";

    /**
     * 获取api_ticket票据
     */
    public static String GET_API_TICKET = HOST + "cgi-bin/ticket/getticket";
    /**
     * 获取关注者列表
     */
    public static String GET_LIST_OF_FOLLOWERS = HOST + "cgi-bin/user/get";

    /**
     * 获取二维码的ticket
     */
    public static String GET_QRCODE_TICKET = HOST + "cgi-bin/qrcode/create?access_token=";
    /**
     * 根据ticket获取二维码
     */
    public static String GET_QRCODE_BY_TICKET = HOST_MP + "cgi-bin/showqrcode?ticket=";
    /**
     * 获取手机号
     */
    public static String GET_PHONE_NUMBER = HOST + "wxa/business/getuserphonenumber?access_token=";

}
