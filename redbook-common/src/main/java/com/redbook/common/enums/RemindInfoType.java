package com.redbook.common.enums;

/**
 * 提醒消息样式类型
 * <AUTHOR>
 * @date 2024-01-16 16:39:22
 */
public enum RemindInfoType {

    SUCCESS("success", "成功样式"),
    WARNING("warning", "警告样式"),
    INFO("info", "消息样式"),
    ERROR("error", "错误样式")
    ;

    private final String type;
    private final String info;

    RemindInfoType(String type, String info)
    {
        this.type = type;
        this.info = info;
    }

    public String getType() {
        return type;
    }

    public String getInfo()
    {
        return info;
    }
}
