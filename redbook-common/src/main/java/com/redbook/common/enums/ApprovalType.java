package com.redbook.common.enums;

/**
 * 审批类型
 * <AUTHOR>
 * @date 2023/12/14 11:43
 */
public enum ApprovalType {

    VOUCHER("1", "代金券"),
    CHARGE("2", "打款"),
    ACCOUNT_APPLY("3", "账号申请"),
    CREATE_EXCLUSIVE_SHOP("4", "建店申请"),
    INVOICE_APPLY("5", "开票申请"),
    PRE_DELIVERY("6", "提前发货申请"),
    RENEW_REFUND_APPLY("7", "退费申请");

    private final String code;
    private final String info;

    ApprovalType(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
