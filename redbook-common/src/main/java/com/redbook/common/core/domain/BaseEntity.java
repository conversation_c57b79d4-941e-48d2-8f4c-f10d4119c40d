package com.redbook.common.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.io.Serializable;
import java.util.*;

/**
 * Entity基类
 * 
 * <AUTHOR>
 */
public class BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 搜索值 */
    @TableField(exist = false)
    private String searchValue;

    /** 创建者 */
    @TableField(exist = false)
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time")
    private Date createTime;

    /** 更新者 */
    @TableField(exist = false)
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time")
    private Date updateTime;

    /** 备注 */
    @TableField(exist = false)
    private String remark;
    @TableField(exist = false)
    private boolean needStatus;
    @TableField(exist = false)
    private Long mId;
    @TableField(exist = false)
    private Long agentId;
    //专卖店ID
    @TableField(exist = false)
    private Integer exclusiveShopId;
    @TableField(exist = false)
    private String exclusiveShopName;
    //店长ID
    @TableField(exist = false)
    private Long exclusiveShopManagerId;
    @TableField(exist = false)
    private String exclusiveShopManagerName;
    @TableField(exist = false)
    /** 请求参数 */
    private Map<String, Object> params;

    @TableField(exist = false)
    private String agentName;

    //代理商agentId列表
    @TableField(exist = false)
    private List<Integer> agentIdList = new ArrayList<>();
    //代理商aid列表
    @TableField(exist = false)
    private List<String> aidList = new ArrayList<>();
    //专卖店id列表
    @TableField(exist = false)
    private List<Integer> exclusiveShopIdList = new ArrayList<>();

    public String getSearchValue()
    {
        return searchValue;
    }

    public void setSearchValue(String searchValue)
    {
        this.searchValue = searchValue;
    }

    public String getCreateBy()
    {
        return createBy;
    }

    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public String getUpdateBy()
    {
        return updateBy;
    }

    public void setUpdateBy(String updateBy)
    {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime()
    {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime)
    {
        this.updateTime = updateTime;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public Map<String, Object> getParams()
    {
        if (params == null)
        {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params)
    {
        this.params = params;
    }

    public boolean isNeedStatus() {
        return needStatus;
    }

    public void setNeedStatus(boolean needStatus) {
        this.needStatus = needStatus;
    }

    public Long getmId() {
        return mId;
    }

    public void setmId(Long mId) {
        this.mId = mId;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public void setExclusiveShopId(Integer exclusiveShopId) {
        this.exclusiveShopId = exclusiveShopId;
    }
    public Integer getExclusiveShopId() {
        return this.exclusiveShopId;
    }

    public Long getExclusiveShopManagerId() {
        return exclusiveShopManagerId;
    }

    public void setExclusiveShopManagerId(Long exclusiveShopManagerId) {
        this.exclusiveShopManagerId = exclusiveShopManagerId;
    }

    public String getExclusiveShopName() {
        return exclusiveShopName;
    }

    public void setExclusiveShopName(String exclusiveShopName) {
        this.exclusiveShopName = exclusiveShopName;
    }

    public String getExclusiveShopManagerName() {
        return exclusiveShopManagerName;
    }

    public void setExclusiveShopManagerName(String exclusiveShopManagerName) {
        this.exclusiveShopManagerName = exclusiveShopManagerName;
    }

    public List<Integer> getAgentIdList() {
        return agentIdList;
    }

    public void setAgentIdList(List<Integer> agentIdList) {
        this.agentIdList = agentIdList;
    }

    public List<Integer> getExclusiveShopIdList() {
        return exclusiveShopIdList;
    }

    public void setExclusiveShopIdList(List<Integer> exclusiveShopIdList) {
        this.exclusiveShopIdList = exclusiveShopIdList;
    }

    public List<String> getAidList() {
        return aidList;
    }

    public void setAidList(List<String> aidList) {
        this.aidList = aidList;
    }
}
