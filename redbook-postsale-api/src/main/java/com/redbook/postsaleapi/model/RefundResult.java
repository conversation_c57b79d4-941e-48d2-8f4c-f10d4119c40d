package com.redbook.postsaleapi.model;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-04-15 15:31
 */
public class RefundResult implements Serializable {
    private static final long serialVersionUID = 1L;

    private String refundId;
    private String outRefundNo;
    private String transactionId;
    private String outTradeNo;
    private String userReceivedAccount;
    private String successTime;
    private String createTime;
    private Long total;
    private Long refund;
    private Long payerTotal;
    private Long payerRefund;
    private String status;

    public RefundResult() {
    }

    public String getRefundId() {
        return this.refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getOutRefundNo() {
        return this.outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getTransactionId() {
        return this.transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return this.outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getUserReceivedAccount() {
        return this.userReceivedAccount;
    }

    public void setUserReceivedAccount(String userReceivedAccount) {
        this.userReceivedAccount = userReceivedAccount;
    }

    public String getSuccessTime() {
        return this.successTime;
    }

    public void setSuccessTime(String successTime) {
        this.successTime = successTime;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getRefund() {
        return refund;
    }

    public void setRefund(Long refund) {
        this.refund = refund;
    }

    public Long getPayerTotal() {
        return payerTotal;
    }

    public void setPayerTotal(Long payerTotal) {
        this.payerTotal = payerTotal;
    }

    public Long getPayerRefund() {
        return payerRefund;
    }

    public void setPayerRefund(Long payerRefund) {
        this.payerRefund = payerRefund;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
