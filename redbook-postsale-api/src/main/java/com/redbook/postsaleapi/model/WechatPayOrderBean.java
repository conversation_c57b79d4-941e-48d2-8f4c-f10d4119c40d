package com.redbook.postsaleapi.model;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-03-28 15:00
 */
public class WechatPayOrderBean implements Serializable {

    private static final long serialVersionUID = 1L;
    //@ApiModelProperty("小程序用户id")
    private Integer appletUserId;
    //@ApiModelProperty("业务类型 (REPAIR:寄修单, SHOP:商城单)")
    private String bizType = "REPAIR";
//    @ApiModelProperty(value = "openid oCLnv65oauR7zSBV5XRq6G57MBgE")
    private String openid;
//    @ApiModelProperty("订单号")
    private Integer bizOrderId;
    private String outTradeNo;
//    @ApiModelProperty("订单总金额")
    private BigDecimal totalMoney;
//    @ApiModelProperty("商品信息描述，用户微信账单的商品字段中可见,商户需传递能真实代表商品信息的描述，不能超过127个字符。")
    private String description;

    public Integer getBizOrderId() {
        return bizOrderId;
    }

    public void setBizOrderId(Integer bizOrderId) {
        this.bizOrderId = bizOrderId;
    }

    public Integer getAppletUserId() {
        return appletUserId;
    }

    public void setAppletUserId(Integer appletUserId) {
        this.appletUserId = appletUserId;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public BigDecimal getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(BigDecimal totalMoney) {
        this.totalMoney = totalMoney;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
