package com.redbook.listener;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.redbook.common.sms.ZTSmsSender;
import com.redbook.system.mq.QueueConstants;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@RocketMQMessageListener(topic = QueueConstants.SMS_SENDER, consumerGroup = QueueConstants.CONSUMER_GROUP)
@Component
public class SmsSenderConsumer implements RocketMQListener<String> {
    @Override
    public void onMessage(String s) {
        JSONObject jsonObject = JSONUtil.parseObj(s);
        String phone = jsonObject.getStr("phone");
        String code = jsonObject.getStr("code");
        ZTSmsSender.sendVerificationCode(code, phone);
    }
}
