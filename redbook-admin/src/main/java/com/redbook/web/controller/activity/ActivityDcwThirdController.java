package com.redbook.web.controller.activity;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.ActivityDcwThirdUser;
import com.redbook.system.domain.ActivityUser;
import com.redbook.system.domain.dto.ActivityDcwThirdIndexListDto;
import com.redbook.system.service.IActivityDcwThirdService;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


@RestController
@RequestMapping("/activity/dcw/third/")
@Api(tags = "单词王争霸赛第三届")
public class ActivityDcwThirdController extends BaseController {

    @Autowired
    QueryParamUtil queryParamUtil;
    @Autowired
    IActivityDcwThirdService activityDcwThirdService;

    @GetMapping("list")
    @ApiOperation("查询单词王争霸赛3")
    public TableDataInfo<ActivityDcwThirdUser> list(ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto) {
        queryParamUtil.setQueryParam(activityDcwThirdIndexListDto);
        startPage();
        List<ActivityDcwThirdUser> list = activityDcwThirdService.selectRecordList(activityDcwThirdIndexListDto);
        return getDataTable(list);
    }


    @ApiOperation("导出单词王争霸赛3")
    @Log(title = "单词王争霸赛3", businessType = BusinessType.EXPORT)
    @PostMapping("export")
    public void export(HttpServletResponse response, ActivityDcwThirdIndexListDto activityDcwThirdIndexListDto) {
        queryParamUtil.setQueryParam(activityDcwThirdIndexListDto);
        List<ActivityDcwThirdUser> list = activityDcwThirdService.selectRecordList(activityDcwThirdIndexListDto);
        activityDcwThirdService.export(list,response,activityDcwThirdIndexListDto);
    }
}
