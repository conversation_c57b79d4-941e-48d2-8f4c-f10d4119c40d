package com.redbook.web.controller.agent;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.AgentUser;
import com.redbook.system.domain.dto.TeachChangeAgentDto;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.IAgentUserService;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 老师管理Controller
 *
 * <AUTHOR>
 * @date 2022-12-09
 */
@RestController
@RequestMapping("/teacher")
@Api(tags = "老师")
public class AgentUserController extends BaseController {
    @Autowired
    private IAgentUserService agentUserService;
    @Autowired
    IAgentService agentService;
    @Autowired
    QueryParamUtil queryParamUtil;

    /**
     * 查询老师管理列表
     */
    @GetMapping("/list")
    @ApiOperation("查询老师列表")
    public TableDataInfo<AgentUser> list(AgentUser agentUser) {
        queryParamUtil.setQueryParamGetAidList(agentUser);
        startPage();
        List<AgentUser> list = agentUserService.selectAgentUserList(agentUser);
        return getDataTable(list);
    }

    /**
     * 查询老师管理列表
     */
    @GetMapping("/list/info")
    @ApiOperation("查询老师列表汇总信息")
    public String info(AgentUser agentUser) {
        queryParamUtil.setQueryParamGetAidList(agentUser);
        List<AgentUser> all = agentUserService.selectAgentUserList(agentUser);
        return "共<span>" + all.size() + "</span>个学管师。";//，已过期:" + all.stream().filter(x -> x.getLastAccessDate().before(new Date())).count() + "个
    }

//    /**
//     * 导出老师管理列表
//     */
//    @ApiOperation("导出老师列表")
//    @Log(title = "老师管理", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, AgentUser agentUser) {
//        queryParamUtil.setQueryParamGetAidList(agentUser);
//        List<AgentUser> list = agentUserService.selectAgentUserList(agentUser);
//        ExcelUtil<AgentUser> util = new ExcelUtil<AgentUser>(AgentUser.class);
//        util.exportExcel(response, list, "老师管理数据");
//    }


    /**
     * 新增老师管理
     */
    @ApiOperation("新增老师")
    @Log(title = "老师管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Validated AgentUser agentUser) {
        return toAjax(agentUserService.insertAgentUser(agentUser));
    }

    /**
     * 修改老师管理
     */
    @ApiOperation("修改老师")
    @Log(title = "老师管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgentUser agentUser) {
        return toAjax(agentUserService.updateAgentUser(agentUser));
    }

    @GetMapping("/updatePassword")
    @ApiOperation(value = "重置密码")
    @Log(title = "老师管理", businessType = BusinessType.UPDATE)
    @ResponseBody
    public AjaxResult resetPassword(String userId) {
        return AjaxResult.success("操作成功", agentUserService.resetPassword(userId));
    }

    /**
     * 删除老师管理
     */
    @ApiOperation("删除老师")
    @Log(title = "老师管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(agentUserService.deleteAgentUserByIds(ids));
    }

    @ApiOperation("老师变更代理商")
    @Log(title = "老师管理", businessType = BusinessType.UPDATE)
    @PostMapping("/changeAgent")
    public AjaxResult changeAgent(@RequestBody @Validated TeachChangeAgentDto teachChangeAgentDto) {
        return toAjax(agentUserService.changeAgent(teachChangeAgentDto));
    }
}
