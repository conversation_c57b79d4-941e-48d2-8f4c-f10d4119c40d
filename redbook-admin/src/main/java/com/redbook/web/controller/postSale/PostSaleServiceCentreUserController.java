package com.redbook.web.controller.postSale;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.system.domain.PostSaleServiceCentreUser;
import com.redbook.system.service.postSale.IPostSaleServiceCentreUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 售后服务中心主管账号关联Controller
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/postSale/admin/serviceCentreUser")
@Api(tags = "售后-服务中心主管账号关联")
public class PostSaleServiceCentreUserController extends BaseController {
    @Autowired
    private IPostSaleServiceCentreUserService postSaleServiceCentreUserService;

    /**
     * 查询售后服务中心主管账号关联列表
     */
    @GetMapping("/list")
    @ApiOperation("查询售后服务中心主管账号关联列表")
    public TableDataInfo<PostSaleServiceCentreUser> list(PostSaleServiceCentreUser postSaleServiceCentreUser) {
        startPage();
        List<PostSaleServiceCentreUser> list = postSaleServiceCentreUserService.selectPostSaleServiceCentreUserList(postSaleServiceCentreUser);
        return getDataTable(list);
    }


    /**
     * 获取售后服务中心主管账号关联详细信息
     */
    @ApiOperation("获取售后服务中心主管账号关联详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(postSaleServiceCentreUserService.selectPostSaleServiceCentreUserById(id));
    }

    /**
     * 新增售后服务中心主管账号关联
     */
    @ApiOperation("新增售后服务中心主管账号关联")
    @Log(title = "售后服务中心主管账号关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostSaleServiceCentreUser postSaleServiceCentreUser) {
        return toAjax(postSaleServiceCentreUserService.insertPostSaleServiceCentreUser(postSaleServiceCentreUser));
    }

    /**
     * 修改售后服务中心主管账号关联
     */
    @ApiOperation("修改售后服务中心主管账号关联")
    @Log(title = "售后服务中心主管账号关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostSaleServiceCentreUser postSaleServiceCentreUser) {
        return toAjax(postSaleServiceCentreUserService.updatePostSaleServiceCentreUser(postSaleServiceCentreUser));
    }

}
