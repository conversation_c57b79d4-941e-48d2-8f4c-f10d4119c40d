package com.redbook.web.controller.postSale;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.PostSaleGoodsInOut;
import com.redbook.system.service.postSale.IPostSaleGoodsInOutService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品出入库Controller
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/admin/goodsInOut")
@Api(tags = "售后-商品出入库")
public class PostSaleGoodsInOutController extends BaseController {
    @Autowired
    private IPostSaleGoodsInOutService postSaleGoodsInOutService;

    /**
     * 查询商品出入库列表
     */
    @GetMapping("/list")
    @ApiOperation("查询商品出入库列表")
    public TableDataInfo<PostSaleGoodsInOut> list(PostSaleGoodsInOut postSaleGoodsInOut) {
        startPage();
        List<PostSaleGoodsInOut> list = postSaleGoodsInOutService.selectPostSaleGoodsInOutList(postSaleGoodsInOut);
        TableDataInfo<PostSaleGoodsInOut> dataTable = getDataTable(list);
        dataTable.setListDataInfo(getListDataInfo(postSaleGoodsInOut));
        return dataTable;
    }

    private String getListDataInfo(PostSaleGoodsInOut postSaleGoodsInOut) {
        clearPage();
        List<PostSaleGoodsInOut> list = postSaleGoodsInOutService.selectPostSaleGoodsInOutList(postSaleGoodsInOut);
        return "数量" + list.stream().mapToLong(PostSaleGoodsInOut::getIntOutNum).sum()
                + ",成本" + list.stream().filter(item -> item.getGoodTotalCost() != null).map(PostSaleGoodsInOut::getGoodTotalCost).reduce(BigDecimal.ZERO, BigDecimal::add)
                + ",销售额" + list.stream().filter(item -> item.getType() == 2 && item.getGoodSaleTotalPrice() != null).map(PostSaleGoodsInOut::getGoodSaleTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 导出商品出入库列表
     */
    @ApiOperation("导出商品出入库列表")
    @Log(title = "商品出入库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PostSaleGoodsInOut postSaleGoodsInOut) {
        List<PostSaleGoodsInOut> list = postSaleGoodsInOutService.selectPostSaleGoodsInOutList(postSaleGoodsInOut);
        ExcelUtil<PostSaleGoodsInOut> util = new ExcelUtil<PostSaleGoodsInOut>(PostSaleGoodsInOut.class);
        util.exportExcel(response, list, "商品出入库数据");
    }

    /**
     * 获取商品出入库详细信息
     */
    @ApiOperation("获取商品出入库详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(postSaleGoodsInOutService.selectPostSaleGoodsInOutById(id));
    }

    /**
     * 新增商品出入库
     */
    @ApiOperation("新增商品出入库")
    @Log(title = "商品出入库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostSaleGoodsInOut postSaleGoodsInOut) {
        return toAjax(postSaleGoodsInOutService.insertPostSaleGoodsInOut(postSaleGoodsInOut));
    }

}
