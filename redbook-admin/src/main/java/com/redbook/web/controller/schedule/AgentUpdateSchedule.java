package com.redbook.web.controller.schedule;

import com.redbook.common.core.redis.RedisCache;
import com.redbook.system.domain.Agent;
import com.redbook.system.service.IAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("agentUpdateSchedule")
public class AgentUpdateSchedule {

    @Autowired
    IAgentService agentService;
    @Autowired
    RedisCache redisCache;

    /**
     * 更新代理商分级（根据正式会员数量）
     */
    public void resetAgentLevel(){
        redisCache.deleteKeys("agentLevel:id:*");
        List<Agent> agentList = agentService.selectAgentList(Agent.builder().status(0L).build());
        agentList.forEach(agent -> {
            Integer formalUserCount = agent.getFormalUserCount();
            if (agent!=null){
                if (formalUserCount >= 800) {
                    agent.setLevel("S+");
                } else if (formalUserCount >= 400) {
                    agent.setLevel("S");
                } else if (formalUserCount >= 200) {
                    agent.setLevel("A+");
                } else if (formalUserCount >= 110) {
                    agent.setLevel("A");
                } else if (formalUserCount >= 80) {
                    agent.setLevel("B");
                } else if (formalUserCount >= 50) {
                    agent.setLevel("C");
                } else if (formalUserCount >= 20) {
                    agent.setLevel("D");
                } else if (formalUserCount >= 1) {
                    agent.setLevel("E");
                }else {
                    agent.setLevel("F");
                }
                agentService.updateAgent(Agent.builder().id(agent.getId()).level(agent.getLevel()).build());
                redisCache.setCacheObject("agentLevel:id:"+agent.getId(),agent.getLevel());
            }
        });
    }
}
