package com.redbook.web.controller.activity.schedule;

import com.redbook.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component("smallDcwSchedule")
public class SmallDcwSchedule {
    private static final Logger log = LoggerFactory.getLogger(SmallDcwSchedule.class);

    @Autowired
    Environment env;
    @Autowired
    RedisTemplate redisTemplate;
    private String getHost() {
        String host = "https://api.xiaohongben888.com/";
        String profile = env.getActiveProfiles()[0];
        if (profile.equals("dev") || profile.equals("test")) {
            host = "https://test.xiaohongben888.com/";
        }
        return host;
    }

    /**
     * 活动结束自动兑换
     */
    public void exchangeCoinInRedbook() {
        String url = getHost() + "redbook/activity/smallDcw/schdule/exchangeGift/9/11";
        log.info("定时器=" + url);
        HttpUtils.sendGet(url);
    }
    public void nationalRewrd() {
        String redisKey = Thread.currentThread().getStackTrace()[1].getMethodName();
        try {
            Object object = redisTemplate.opsForValue().get(redisKey);
            if(object==null) {
                redisTemplate.opsForValue().set(redisKey,"1",10, TimeUnit.MINUTES);
                String url = getHost() + "redbook/activity/smallDcw/v3/schdule/nationalRewrd/17/17";
                log.info("定时器=" + url);
                HttpUtils.sendGet(url);
            }
        }catch (Exception ex){
            throw  ex;
        }finally {
//            redisTemplate.delete(redisKey);
        }
    }
}
