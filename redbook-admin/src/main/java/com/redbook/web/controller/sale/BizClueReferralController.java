package com.redbook.web.controller.sale;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.BizClueReferral;
import com.redbook.system.service.IBizClueReferralService;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.core.page.TableDataInfo;

/**
 * 线索转介绍Controller
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
@RestController
@RequestMapping("/biz/referral")
@Api(tags = "线索转介绍")
public class BizClueReferralController extends BaseController
{
    @Autowired
    private IBizClueReferralService bizClueReferralService;

    /**
     * 查询线索转介绍列表
     */
    @GetMapping("/list")
    @ApiOperation("查询线索转介绍列表")
    public TableDataInfo<BizClueReferral> list(BizClueReferral bizClueReferral)
    {
        startPage();
        List<BizClueReferral> list = bizClueReferralService.selectBizClueReferralList(bizClueReferral);
        return getDataTable(list);
    }

    /**
     * 导出线索转介绍列表
     */
    @ApiOperation("导出线索转介绍列表")
    @Log(title = "线索转介绍", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizClueReferral bizClueReferral)
    {
        List<BizClueReferral> list = bizClueReferralService.selectBizClueReferralList(bizClueReferral);
        ExcelUtil<BizClueReferral> util = new ExcelUtil<BizClueReferral>(BizClueReferral.class);
        util.exportExcel(response, list, "线索转介绍数据");
    }

    /**
     * 获取线索转介绍详细信息
     */
    @ApiOperation("获取线索转介绍详细信息")
    @GetMapping(value = "/{clueReferralId}")
    public AjaxResult getInfo(@PathVariable("clueReferralId") Long clueReferralId)
    {
        return success(bizClueReferralService.selectBizClueReferralByClueReferralId(clueReferralId));
    }

    /**
     * 新增线索转介绍
     */
    @ApiOperation("新增线索转介绍")
    @Log(title = "线索转介绍", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizClueReferral bizClueReferral)
    {
        return toAjax(bizClueReferralService.insertBizClueReferral(bizClueReferral));
    }

    /**
     * 修改线索转介绍
     */
    @ApiOperation("修改线索转介绍")
    @Log(title = "线索转介绍", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizClueReferral bizClueReferral)
    {
        return toAjax(bizClueReferralService.updateBizClueReferral(bizClueReferral));
    }

    /**
     * 删除线索转介绍
     */
    @ApiOperation("删除线索转介绍")
    @Log(title = "线索转介绍", businessType = BusinessType.DELETE)
	@DeleteMapping("/{clueReferralIds}")
    public AjaxResult remove(@PathVariable Long[] clueReferralIds)
    {
        return toAjax(bizClueReferralService.deleteBizClueReferralByClueReferralIds(clueReferralIds));
    }
}
