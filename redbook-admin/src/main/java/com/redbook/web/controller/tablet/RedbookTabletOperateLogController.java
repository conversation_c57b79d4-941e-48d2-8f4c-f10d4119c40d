package com.redbook.web.controller.tablet;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.RedbookTabletOperateLog;
import com.redbook.system.service.IRedbookTabletOperateLogService;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.core.page.TableDataInfo;

/**
 * 小红本操作日志Controller
 * 
 * <AUTHOR>
 * @date 2024-03-07
 */
@RestController
@RequestMapping("/system/tablet/log")
@Api(tags = "小红本操作日志")
public class RedbookTabletOperateLogController extends BaseController
{
    @Autowired
    private IRedbookTabletOperateLogService redbookTabletOperateLogService;

    /**
     * 查询小红本操作日志列表
     */
    @GetMapping("/list")
    @ApiOperation("查询小红本操作日志列表")
    public TableDataInfo<RedbookTabletOperateLog> list(RedbookTabletOperateLog redbookTabletOperateLog)
    {
        startPage();
        List<RedbookTabletOperateLog> list = redbookTabletOperateLogService.selectRedbookTabletOperateLogList(redbookTabletOperateLog);
        return getDataTable(list);
    }

    /**
     * 导出小红本操作日志列表
     */
    @ApiOperation("导出小红本操作日志列表")
    @Log(title = "小红本操作日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RedbookTabletOperateLog redbookTabletOperateLog)
    {
        List<RedbookTabletOperateLog> list = redbookTabletOperateLogService.selectRedbookTabletOperateLogList(redbookTabletOperateLog);
        ExcelUtil<RedbookTabletOperateLog> util = new ExcelUtil<RedbookTabletOperateLog>(RedbookTabletOperateLog.class);
        util.exportExcel(response, list, "小红本操作日志数据");
    }

    /**
     * 获取小红本操作日志详细信息
     */
    @ApiOperation("获取小红本操作日志详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(redbookTabletOperateLogService.selectRedbookTabletOperateLogById(id));
    }

    /**
     * 新增小红本操作日志
     */
    @ApiOperation("新增小红本操作日志")
    @Log(title = "小红本操作日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RedbookTabletOperateLog redbookTabletOperateLog)
    {
        return toAjax(redbookTabletOperateLogService.insertRedbookTabletOperateLog(redbookTabletOperateLog));
    }

    /**
     * 修改小红本操作日志
     */
    @ApiOperation("修改小红本操作日志")
    @Log(title = "小红本操作日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RedbookTabletOperateLog redbookTabletOperateLog)
    {
        return toAjax(redbookTabletOperateLogService.updateRedbookTabletOperateLog(redbookTabletOperateLog));
    }

    /**
     * 删除小红本操作日志
     */
    @ApiOperation("删除小红本操作日志")
    @Log(title = "小红本操作日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(redbookTabletOperateLogService.deleteRedbookTabletOperateLogByIds(ids));
    }
}
