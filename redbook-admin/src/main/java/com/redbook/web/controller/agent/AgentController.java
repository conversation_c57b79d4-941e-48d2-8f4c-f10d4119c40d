package com.redbook.web.controller.agent;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.entity.SysUser;
import com.redbook.common.core.domain.model.LoginUser;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.IdentityGenerator;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.AgentOperLog;
import com.redbook.system.domain.dto.*;
import com.redbook.system.domain.vo.AgentAreaVo;
import com.redbook.system.domain.vo.AgentDetailVo;
import com.redbook.system.domain.vo.AgentMappingListVo;
import com.redbook.system.service.IAgentOperLogService;
import com.redbook.system.service.IAgentService;
import com.redbook.system.service.ISysUserService;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.redbook.common.utils.DingUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 代理商Controller
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@RestController
@RequestMapping("/system/agent")
@Api(tags = "代理商")
public class AgentController extends BaseController {
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IAgentOperLogService agentOperLogService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    QueryParamUtil queryParamUtil;


    @GetMapping("/mapping")
    @ApiOperation("代理商映射列表")
    public AjaxResult<AgentMappingListVo> agentMappingListVoAjaxResult(@ApiParam("用户id") Long userId, @ApiParam("角色id") Long roleId) {
        return AjaxResult.success(agentService.agentMappingListVo(userId, roleId));
    }

    @ApiOperation("保存代理商映射")
    @PostMapping("/mapping")
    @Log(title = "代理商映射", businessType = BusinessType.INSERT)
    public AjaxResult saveAgentMapping(@ApiParam("代理商映射列表") @RequestBody AgentMappingDto dto) {
        return success(agentService.saveAgentMapping(dto));
    }

    @ApiOperation("代理商区域详情")
    @GetMapping("/area")
    public AjaxResult<AgentAreaVo> agentAreaVoAjaxResult(@ApiParam("代理商id") Long id) {
        return AjaxResult.success(agentService.getAgentArea(id));
    }

    /**
     * 查询代理商列表
     */
    @GetMapping("/list")
    @ApiOperation("查询代理商列表")
//    @Log(title = "代理商", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('agentMange')")
    public TableDataInfo<Agent> list(Agent agent) {
        queryParamUtil.setQueryParam(agent);
        startPage();
        List<Agent> list = agentService.selectAgentList(agent);
        return getDataTable(list);
    }

    /**
     * 查询代理商列表
     */
    @GetMapping("/list/info")
    @ApiOperation("代理商列表信息汇总信息")
    @Log(title = "代理商列表信息汇总信息", businessType = BusinessType.OTHER)
    @PreAuthorize("@ss.hasPermi('agentMange')")
    public String info(Agent agent) {
        queryParamUtil.setQueryParam(agent);
        StringBuilder sb = new StringBuilder();
        List<Agent> all = agentService.selectAgentList(agent);
        if (all.size()>1000){
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser.getUser().getRole().getRoleId()==100||loginUser.getUser().getRole().getRoleId()==106){
                DingUtil.sendMsg("异常信息"+loginUser.getUser().getUserName()+"共签约"+all.size()+"个区域，请及时处理");
            }
        }
        sb.append("共签约").append("<span>").append(all.size()).append("</span>").append("个区域");
        long num1 = all.stream().filter(agent1 -> agent1.getAgreementLevel() == 1).count();
        long num2 = all.stream().filter(agent1 -> agent1.getAgreementLevel() == 2).count();
        if((num1+num2)>0){
            sb.append("：");
            if (num1>0){
                sb.append("<span>").append(num1).append("</span>").append("个省级代理，");
            }
            if (num2>0){
                sb.append("<span>").append(num2).append("</span>").append("个市级代理，");
            }
            sb.append("<span>").append(all.stream().filter(agent1 -> agent1.getAgreementLevel() == 3).count()).append("</span>").append("个区县级代理。");
        }else {
            sb.append("。");
        }
        sb.append("（");
        sb.append("一级店："+all.stream().map(Agent::getShopCountLevel1).reduce(0, Integer::sum)+"个，");
        sb.append("二级店："+all.stream().map(Agent::getShopCountLevel2).reduce(0, Integer::sum)+"个；");
        sb.append("正式会员："+all.stream().map(Agent::getFormalUserCount).reduce(0, Integer::sum)+"个，");
        sb.append("体验会员："+all.stream().map(Agent::getExperienceUserCount).reduce(0, Integer::sum)+"个");
        sb.append("）");
//        sb.append("<span>").append(all.stream().filter(agent1 -> agent1.getAgreementLevel() == 4).count()).append("</span>").append("个店级代理商。");
        return sb.toString();
    }


    @ApiOperation("新增代理商")
    @PostMapping("add")
    @Log(title = "新增代理商", businessType = BusinessType.INSERT)
    public AjaxResult addAgent(@RequestBody @Validated AddAgentDto dto) {
        return AjaxResult.e(200, null, agentService.addAgent(dto));
    }

    /**
     * 导出代理商列表
     */
    @ApiOperation("导出代理商列表")
    @Log(title = "代理商", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Agent agent) {
        List<Agent> list = agentService.selectAgentList(agent);
        ExcelUtil<Agent> util = new ExcelUtil<Agent>(Agent.class);
        util.exportExcel(response, list, "代理商数据");
    }

    /**
     * 获取代理商详细信息
     */
    @ApiOperation("获取代理商详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult<AgentDetailVo> getInfo(@PathVariable("id") Long id) {
        return success(agentService.getAgentDetail(id));
    }

    @Log(title = "代理商", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改代理商")
    public AjaxResult edit(@RequestBody UpdateAgentDto agent) {
        return toAjax(agentService.updateAgent(agent));
    }
    @Log(title = "设置代理商赠送的硬件数量", businessType = BusinessType.UPDATE)
    @PutMapping("/setAgentGiftCount")
    @ApiOperation("设置代理商赠送的硬件数量")
    public AjaxResult setAgentGiftCount(@RequestBody SetAgentGiftCountDto agent) {
        return toAjax(agentService.setAgentGiftCount(agent));
    }
    @Log(title = "设置代理商会员课程", businessType = BusinessType.UPDATE)
    @PutMapping("/setGiftAccount")
    @ApiOperation("设置代理商会员课程")
    public AjaxResult configGiftAccount(@RequestBody SetAgentGiftAccountDto agent) {
        return toAjax(agentService.setGiftAccount(agent));
    }
    @Log(title = "代理商日志", businessType = BusinessType.INSERT)
    @PostMapping("/log")
    @ApiOperation("新增代理商日志")
    public AjaxResult addAgentLog(@RequestBody AgentOperLog dto) {
        return toAjax(agentOperLogService.insertAgentOperLog(dto));
    }

    @ApiOperation("退盟")
    @PostMapping("/exit")
    @Log(title = "退盟", businessType = BusinessType.UPDATE)
    public AjaxResult exit(@RequestBody @Validated AgentExitDto dto) {
        return toAjax(agentService.exit(dto));
    }

    @ApiOperation("续约")
    @PostMapping("/renew")
    @Log(title = "续约", businessType = BusinessType.UPDATE)
    public AjaxResult renew(@RequestBody @Validated AgentRenewDto dto) {
        return toAjax(agentService.renew(dto));
    }

    @ApiOperation("代理商日志列表")
    @GetMapping("/log/list")
    public TableDataInfo<AgentOperLog> agentLogList(AgentOperLog agentOperLog) {
        List<AgentOperLog> list = agentOperLogService.selectAgentOperLogList(agentOperLog);
        return getDataTable(list);
    }
    @ApiOperation("代理商硬件赠送数量")
    @GetMapping("/gift/count/{id}")
    public Integer getGiftCount(@PathVariable("id") Long id) {
        return agentService.getAgentDetail(id).getAgent().getGiftCount();
    }

    @ApiOperation(value = "重置签约人账号密码")
    @PutMapping("/updateAccountPassword")
    public AjaxResult resetPassword(Long userId) {
        SysUser sysUser = userService.selectUserById(userId);
        if(sysUser.getRole().getRoleId().intValue()!=100){
            return error("只能重置签约人登录账号密码！");
        }
        String password = IdentityGenerator.randomString(6);
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setUpdateBy(getUsername());
        if(userService.resetPwd(user)>0){
            return AjaxResult.success("操作成功！", password);
        }
        return error("操作失败！");
    }
}
