package com.redbook.web.controller.order;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.system.domain.StoreOrderListRequest;
import com.redbook.system.domain.StoreOrderPostRequest;
import com.redbook.system.domain.vo.StoreOrderListVo;
import com.redbook.system.service.IOrderManagerService;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;

@Controller
@RequestMapping("/storeOrder")
public class OrderManagerAction extends BaseController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    HttpSession session;
    @Autowired
    private IOrderManagerService orderManagerService;
    @Autowired
    QueryParamUtil queryParamUtil;
    /**
     * 克莱曼商城订单管理
     * @param storeOrderListRequest
     * @return
     */
    @GetMapping("/storeOrderList")
    @ResponseBody
    @ApiOperation(value = "克莱曼商城订单管理")
    public TableDataInfo<StoreOrderListVo> storeOrderList(StoreOrderListRequest storeOrderListRequest) {
        queryParamUtil.setQueryParam(storeOrderListRequest);
        startPage();
        List<StoreOrderListVo> storeOrderListVoList= orderManagerService.storeOrderList(storeOrderListRequest);
        return getDataTable(storeOrderListVoList);
    }

    /**
     * 更新订单为已兑换
     * @return
     */
    @PostMapping("/updateOrderToConverted")
    @ResponseBody
    @ApiOperation(value = "更新订单为已兑换")
    @Log(title = "克莱曼商城订单管理", businessType = BusinessType.CONVERTED)
    public AjaxResult<Boolean> updateOrderToConverted(@RequestBody StoreOrderPostRequest storeOrderPostRequest) {
        boolean flag = orderManagerService.updateOrderToConverted(storeOrderPostRequest.getOrderIdArr());
        return AjaxResult.success(flag);
    }

    /**
     * 更新订单为已取消
     * @return
     */
    @PostMapping("/updateOrderToCanceled")
    @ApiOperation(value = "更新订单为已取消")
    @ResponseBody
    @Log(title = "克莱曼商城订单管理", businessType = BusinessType.CANCELED)
    public AjaxResult<Boolean> updateOrderToCanceled(@RequestBody StoreOrderPostRequest storeOrderPostRequest) {
        if(StringUtils.isBlank(storeOrderPostRequest.getDescription())){
            storeOrderPostRequest.setDescription("运管取消");
        }
        boolean flag = orderManagerService.updateOrderToCanceled(storeOrderPostRequest.getOrderIdArr(),storeOrderPostRequest.getDescription() );
        return AjaxResult.success(flag);
    }


}
