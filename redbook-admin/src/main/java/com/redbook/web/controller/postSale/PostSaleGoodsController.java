package com.redbook.web.controller.postSale;

import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.service.postSale.IPostSaleGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.PostSaleGoods;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.core.page.TableDataInfo;

/**
 * 商品Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/admin/goods")
@Api(tags = "售后-商品")
public class PostSaleGoodsController extends BaseController
{
    @Autowired
    private IPostSaleGoodsService postSaleGoodsService;

    /**
     * 查询商品列表
     */
    @GetMapping("/list")
    @ApiOperation("查询商品列表")
    public TableDataInfo<PostSaleGoods> list(PostSaleGoods postSaleGoods)
    {
        startPage();
        List<PostSaleGoods> list = postSaleGoodsService.selectPostSaleGoodsList(postSaleGoods);
        return getDataTable(list);
    }

    /**
     * 导出商品列表
     */
    @ApiOperation("导出商品列表")
    @Log(title = "商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PostSaleGoods postSaleGoods)
    {
        List<PostSaleGoods> list = postSaleGoodsService.selectPostSaleGoodsList(postSaleGoods);
        ExcelUtil<PostSaleGoods> util = new ExcelUtil<PostSaleGoods>(PostSaleGoods.class);
        util.exportExcel(response, list, "商品数据");
    }

    /**
     * 获取商品详细信息
     */
    @ApiOperation("获取商品详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(postSaleGoodsService.selectPostSaleGoodsById(id));
    }

    /**
     * 新增商品
     */
    @ApiOperation("新增商品")
    @Log(title = "商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostSaleGoods postSaleGoods)
    {
        postSaleGoods.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
        postSaleGoods.setGoodStoreNum(0L);
        postSaleGoods.setGoodTotalCost(postSaleGoods.getGoodCost().multiply(BigDecimal.ZERO));
        return toAjax(postSaleGoodsService.insertPostSaleGoods(postSaleGoods));
    }

    /**
     * 修改商品
     */
    @ApiOperation("修改商品")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostSaleGoods postSaleGoods)
    {
        return toAjax(postSaleGoodsService.updatePostSaleGoods(postSaleGoods));
    }

    /**
     * 删除商品
     */
    @ApiOperation("删除商品")
    @Log(title = "商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(postSaleGoodsService.deletePostSaleGoodsByIds(ids));
    }
}
