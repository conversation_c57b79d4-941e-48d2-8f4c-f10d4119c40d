package com.redbook.web.controller.common;

import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.entity.ExclusiveShop;
import com.redbook.common.core.domain.model.LoginUser;
import com.redbook.common.enums.ApprovalType;
import com.redbook.common.enums.RemindInfoType;
import com.redbook.common.utils.SecurityUtils;
import com.redbook.system.domain.Agent;
import com.redbook.system.domain.AgentAccount;
import com.redbook.system.domain.Approval;
import com.redbook.system.domain.SchoolRemoteStatus;
import com.redbook.system.domain.vo.RemindInfoVo;
import com.redbook.system.service.*;
import com.redbook.system.util.DateUtil;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/remind")
@Api(tags = "提醒消息")
public class RemindController extends BaseController {
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IAgentAccountService agentAccountService;
    @Autowired
    private ISchoolRemoteStatusService schoolRemoteStatusService;
    @Autowired
    private IExclusiveShopService exclusiveShopService;
    @Autowired
    private IApprovalService approvalService;
    @Autowired
    QueryParamUtil queryParamUtil;
    @GetMapping("/list")
    @ApiOperation("查询提醒信息列表")
    public AjaxResult<List<RemindInfoVo>> msgList() {
        List<RemindInfoVo> list = new ArrayList<>();
        Date date30 = DateUtil.addDay(new Date(), 30);//30天后的日期
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Boolean limitAgent = loginUser.getUser().getLimitAgent();
        //密码是123456的，提醒修改密码
        if(loginUser.getPassword().equals("e10adc3949ba59abbe56e057f20f883e")){
            list.add(RemindInfoVo.builder().remindInfoType(RemindInfoType.ERROR.getType()).title("密码已泄露").message("您的密码过于简单，请及时修改密码！").build());
        }
        //签约人、经理等有区域限制的用户
        if(limitAgent && loginUser.getUser().getRole().getRoleId().intValue()!=106){
            //1、合同到期提醒
            Agent agent = Agent.builder().build();
            queryParamUtil.setQueryParam(agent);
            List<Agent> agentList = agentService.selectAgentList(agent);
            //(1)：合同已到期
            {
                List<String> expireAgentList = agentList.stream().filter(a -> a.getStatus() == 0 && a.getAgreementEnd().getTime() < DateUtil.getToday0HoursTime()).map(a -> a.getName()).collect(Collectors.toList());
                if (expireAgentList.size() > 0) {
                    String message = expireAgentList.stream().collect(Collectors.joining("、"));
                    if(message.length()>50){
                        message = message.substring(0,50)+"...";
                    }
                    list.add(RemindInfoVo.builder().remindInfoType(RemindInfoType.ERROR.getType()).title(expireAgentList.size() + "个区域合同已过期").message(message).build());
                }
            }
            //(2)：合同30天内到期
            {
                List<String> soonExpireAgentList = agentList.stream().filter(a -> a.getStatus() == 0 && a.getAgreementEnd().getTime() >= DateUtil.getToday0HoursTime() && a.getAgreementEnd().getTime() < date30.getTime()).map(a -> a.getName()).collect(Collectors.toList());
                if (soonExpireAgentList.size() > 0) {
                    String message = soonExpireAgentList.stream().collect(Collectors.joining("、"));
                    if(message.length()>50){
                        message = message.substring(0,50)+"...";
                    }
                    list.add(RemindInfoVo.builder().remindInfoType(RemindInfoType.WARNING.getType()).title( soonExpireAgentList.size() + "个区域合同即将到期").message(message).build());
                }
            }
            //2、一对一到期提醒：到期30天内开始提醒。（已过期的不再提醒）
            {
                SchoolRemoteStatus schoolRemoteStatus = SchoolRemoteStatus.builder().build();
                queryParamUtil.setQueryParam(schoolRemoteStatus);
                List<SchoolRemoteStatus> remoteStatusList = schoolRemoteStatusService.selectSchoolRemoteStatusList(schoolRemoteStatus);
                List<String> soonExpireAgentList = remoteStatusList.stream().filter(r -> r.getExpirationDate() != null && r.getExpirationDate().getTime() >= DateUtil.getToday0HoursTime() && r.getExpirationDate().getTime() < date30.getTime()).map(r -> r.getAgentName()).collect(Collectors.toList());
                if (soonExpireAgentList.size() > 0) {
                    String message = soonExpireAgentList.stream().collect(Collectors.joining("、"));
                    if(message.length()>50){
                        message = message.substring(0,50)+"...";
                    }
                    list.add(RemindInfoVo.builder().remindInfoType(RemindInfoType.WARNING.getType()).title(soonExpireAgentList.size() + "个区域一对一远程服务即将到期").message(message).build());
                }
            }
            //3、余额不足提醒：余额低于1000时提醒。
            {
                AgentAccount agentAccount = AgentAccount.builder().build();
                queryParamUtil.setQueryParam(agentAccount);
                List<AgentAccount> agentAccounts = agentAccountService.selectAgentAccountList(agentAccount);
                List<String> lowMoneyAgentList = agentAccounts.stream().filter(a -> a.getTotalMoney().doubleValue()<1000).map(r -> r.getAgentName()).collect(Collectors.toList());
                if (lowMoneyAgentList.size() > 0) {
                    list.add(RemindInfoVo.builder().remindInfoType(RemindInfoType.INFO.getType()).title(lowMoneyAgentList.size() + "个区域账户余额不足1000").message("为了不影响区域运营，请及时充值。").build());
                }
            }
            //4、未创建专卖店提醒
            {
                List<String> noShopAgentList = agentList.stream().filter(a -> (a.getShopCountLevel1()+a.getShopCountLevel2())==0).map(a -> a.getName()).collect(Collectors.toList());
                if (noShopAgentList.size() > 0) {
                    String message = noShopAgentList.stream().collect(Collectors.joining("、"));
                    if(message.length()>50){
                        message = message.substring(0,50)+"...";
                    }
                    list.add(RemindInfoVo.builder().remindInfoType(RemindInfoType.WARNING.getType()).title(noShopAgentList.size() + "个区域未创建专卖店").message(message).build());
                }
            }
        }
        //签约人、店长
        if(loginUser.getUser().getRole().getRoleId().intValue()==100||loginUser.getUser().getRole().getRoleId().intValue()==106){
            ExclusiveShop exclusiveShop = ExclusiveShop.builder().build();
            queryParamUtil.setQueryParam(exclusiveShop);
            List<ExclusiveShop> exclusiveShopList = exclusiveShopService.selectExclusiveShopList(exclusiveShop);
            //5、专卖店到期提醒：到期30天内开始提醒。
            {
                List<String> soonExpireExclusiveShopList = exclusiveShopList.stream().filter(es -> es.getStatus() == 0 && es.getEndDate().getTime() < date30.getTime()).map(es -> es.getName()).collect(Collectors.toList());
                if (soonExpireExclusiveShopList.size() > 0) {
                    String message = soonExpireExclusiveShopList.stream().collect(Collectors.joining("、"));
                    if(message.length()>50){
                        message = message.substring(0,50)+"...";
                    }
                    list.add(RemindInfoVo.builder().remindInfoType(RemindInfoType.WARNING.getType()).title(soonExpireExclusiveShopList.size() + "个专卖店即将到期").message(message).build());
                }
            }
        }
        //7、待审批审批提醒。
        {
            Approval approval = new Approval();
            approval.setApprovalStatus("0");
            approval.setFirstApproverId(getUserId()+"");
            List<Approval> approvalList = approvalService.selectApprovalList(approval);
            approval = new Approval();
            approval.setApprovalStatus("1");
            approval.setFinalApproverId(getUserId()+"");
            approvalList.addAll(approvalService.selectApprovalList(approval));
            if(approvalList.size()>0){
                list.add(RemindInfoVo.builder().remindInfoType(RemindInfoType.WARNING.getType()).title("待处理审批提醒" ).message("共"+approvalList.size() + "个，请前往”审批“频道查看。").build());
            }
        }
        return AjaxResult.success(list);
    }
}
