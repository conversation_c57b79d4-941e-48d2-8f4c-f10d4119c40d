package com.redbook.web.controller.postSale;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.PostSaleGoodsOrder;
import com.redbook.system.domain.postsale.PostSaleGoodsBlackMailDomain;
import com.redbook.system.domain.postsale.PostSaleGoodsDomain;
import com.redbook.system.service.postSale.IPostSaleAdminGoodsOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 售后后台-商品订单Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/admin/goodsOrder")
@Api(tags = "售后-商品订单")
public class PostSaleGoodsOrderController extends BaseController
{
    @Autowired
    private IPostSaleAdminGoodsOrderService adminGoodsOrderService;

    @Autowired
    HttpServletRequest request;

    /**
     * 商城订单管理（品控、回寄工程师查看）
     * 查询商品订单列表
     */
    @GetMapping("/list")
    @ApiOperation("查询商品订单列表")
    public TableDataInfo<PostSaleGoodsOrder> list(PostSaleGoodsOrder postSaleGoodsOrder)
    {
        startPage();
        List<PostSaleGoodsOrder> list = adminGoodsOrderService.goodsOrderList(postSaleGoodsOrder);
        return getDataTable(list);
    }

    /**
     * 财务管理中心/商城回款统计（财务查看）
     * 查询商品订单列表
     */
    @GetMapping("/summaryList")
    @ApiOperation("商城回款统计列表")
    public TableDataInfo<PostSaleGoodsOrder> paybackList(PostSaleGoodsOrder postSaleGoodsOrder)
    {
        startPage();
        List<PostSaleGoodsOrder> list = adminGoodsOrderService.paybackList(postSaleGoodsOrder);
        String summaryCount = adminGoodsOrderService.summaryCount();
        TableDataInfo<PostSaleGoodsOrder> dataTable = getDataTable(list);
        dataTable.setListDataInfo(summaryCount);
        return dataTable;
    }

    /**
     * 前端默认pageSize: 9999
     * 导出商城回款统计
     */
    @ApiOperation("导出商城回款统计")
    @Log(title = "商城回款统计", businessType = BusinessType.EXPORT)
    @PostMapping("/summaryListExport")
    public void export(HttpServletResponse response, PostSaleGoodsOrder postSaleGoodsOrder)
    {
        List<PostSaleGoodsOrder> list = adminGoodsOrderService.paybackList(postSaleGoodsOrder);
        ExcelUtil<PostSaleGoodsOrder> util = new ExcelUtil<PostSaleGoodsOrder>(PostSaleGoodsOrder.class);
        util.exportExcel(response, list, "商城回款统计");
    }

    /**
     * 获取商品订单详细信息
     */
    @ApiOperation("获取商品订单详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(adminGoodsOrderService.selectById(id));
    }

    @ApiOperation("打单发货")
    @PostMapping(value = "/blackmail")
    public AjaxResult blackmail(@RequestBody PostSaleGoodsBlackMailDomain postSaleGoodsBlackMailDomain)
    {
        return adminGoodsOrderService.blackmail(postSaleGoodsBlackMailDomain);
    }

    /**
     * 售后经理处理
     * @param id
     * @return
     */
    @ApiOperation("取消订单-审核处理")
    @PostMapping(value = "/cancelOrderHandle/{id}")
    public AjaxResult cancelOrderHandle(@PathVariable("id") Long id)
    {
        return success(adminGoodsOrderService.cancelOrderHandle(id));
    }

    /**
     * 财务人员处理
     * @param postSaleGoodsDomain
     * @return
     */
    @ApiOperation("取消订单-确认退款")
    @PostMapping(value = "/cancelOrderConfirm")
    public AjaxResult cancelOrderConfirm(@RequestBody PostSaleGoodsDomain postSaleGoodsDomain)
    {
        return success(adminGoodsOrderService.cancelOrderConfirm(postSaleGoodsDomain));
    }



}
