package com.redbook.web.controller.postSale;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.system.service.postSale.IPostSaleGoodsClassifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.PostSaleGoodsClassify;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.core.page.TableDataInfo;

/**
 * 商品类型Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/admin/goodsClassify")
@Api(tags = "售后-商品类型")
public class PostSaleGoodsClassifyController extends BaseController
{
    @Autowired
    private IPostSaleGoodsClassifyService postSaleGoodsClassifyService;

    /**
     * 查询商品类型列表
     */
    @GetMapping("/list")
    @ApiOperation("查询商品类型列表")
    public TableDataInfo<PostSaleGoodsClassify> list(PostSaleGoodsClassify postSaleGoodsClassify)
    {
        startPage();
        List<PostSaleGoodsClassify> list = postSaleGoodsClassifyService.selectPostSaleGoodsClassifyList(postSaleGoodsClassify);
        return getDataTable(list);
    }


    /**
     * 新增商品类型
     */
    @ApiOperation("新增商品类型")
    @Log(title = "商品类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostSaleGoodsClassify postSaleGoodsClassify)
    {
        return toAjax(postSaleGoodsClassifyService.insertPostSaleGoodsClassify(postSaleGoodsClassify));
    }

    /**
     * 修改商品类型
     */
    @ApiOperation("修改商品类型")
    @Log(title = "商品类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostSaleGoodsClassify postSaleGoodsClassify)
    {
        return toAjax(postSaleGoodsClassifyService.updatePostSaleGoodsClassify(postSaleGoodsClassify));
    }

    /**
     * 删除商品类型
     */
    @ApiOperation("删除商品类型")
    @Log(title = "商品类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(postSaleGoodsClassifyService.deletePostSaleGoodsClassifyByIds(ids));
    }
}
