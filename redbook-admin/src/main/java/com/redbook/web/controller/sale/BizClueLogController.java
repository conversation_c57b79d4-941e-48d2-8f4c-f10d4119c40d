package com.redbook.web.controller.sale;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.BizClueLog;
import com.redbook.system.service.IBizClueLogService;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.core.page.TableDataInfo;

/**
 * 线索动态日志Controller
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
@RestController
@RequestMapping("/biz/log")
@Api(tags = "线索动态日志")
public class BizClueLogController extends BaseController
{
    @Autowired
    private IBizClueLogService bizClueLogService;

    /**
     * 查询线索动态日志列表
     */
    @GetMapping("/list")
    @ApiOperation("查询线索动态日志列表")
    public TableDataInfo<BizClueLog> list(BizClueLog bizClueLog)
    {
        startPage();
        List<BizClueLog> list = bizClueLogService.selectBizClueLogList(bizClueLog);
        return getDataTable(list);
    }

    /**
     * 导出线索动态日志列表
     */
    @ApiOperation("导出线索动态日志列表")
    @Log(title = "线索动态日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizClueLog bizClueLog)
    {
        List<BizClueLog> list = bizClueLogService.selectBizClueLogList(bizClueLog);
        ExcelUtil<BizClueLog> util = new ExcelUtil<BizClueLog>(BizClueLog.class);
        util.exportExcel(response, list, "线索动态日志数据");
    }

    /**
     * 获取线索动态日志详细信息
     */
    @ApiOperation("获取线索动态日志详细信息")
    @GetMapping(value = "/{clueLogId}")
    public AjaxResult getInfo(@PathVariable("clueLogId") Long clueLogId)
    {
        return success(bizClueLogService.selectBizClueLogByClueLogId(clueLogId));
    }

    /**
     * 新增线索动态日志
     */
    @ApiOperation("新增线索动态日志")
    @Log(title = "线索动态日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizClueLog bizClueLog)
    {
        return toAjax(bizClueLogService.insertBizClueLog(bizClueLog));
    }

    /**
     * 修改线索动态日志
     */
    @ApiOperation("修改线索动态日志")
    @Log(title = "线索动态日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizClueLog bizClueLog)
    {
        return toAjax(bizClueLogService.updateBizClueLog(bizClueLog));
    }

    /**
     * 删除线索动态日志
     */
    @ApiOperation("删除线索动态日志")
    @Log(title = "线索动态日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{clueLogIds}")
    public AjaxResult remove(@PathVariable Long[] clueLogIds)
    {
        return toAjax(bizClueLogService.deleteBizClueLogByClueLogIds(clueLogIds));
    }
}
