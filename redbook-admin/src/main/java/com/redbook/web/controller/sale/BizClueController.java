package com.redbook.web.controller.sale;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.BizClue;
import com.redbook.system.domain.dto.BizCluePostDTO;
import com.redbook.system.domain.excel.BizClueExcel;
import com.redbook.system.domain.vo.BizClueEditVO;
import com.redbook.system.domain.vo.BizClueListVO;
import com.redbook.system.domain.vo.BizClueReferralVO;
import com.redbook.system.domain.vo.BizClueRequestVO;
import com.redbook.system.enums.ClueCustomerLabelEnum;
import com.redbook.system.enums.CluePoolTypeEnum;
import com.redbook.system.enums.ClueStatusEnum;
import com.redbook.system.service.IBizClueService;
import com.redbook.system.util.ComUtil;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 线索Controller
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
@RestController
@RequestMapping("/biz/clue")
@Api(tags = "线索")
public class BizClueController extends BaseController
{
    @Autowired
    private IBizClueService bizClueService;
    @Autowired
    QueryParamUtil queryParamUtil;
    /**
     * 查询线索列表
     */
    @GetMapping("/list")
    @ApiOperation("查询线索列表")
    public TableDataInfo<BizClueListVO> list(BizClueRequestVO bizClueRequestVO)
    {
        queryParamUtil.setQueryParam(bizClueRequestVO);
        startPage();
        List<BizClueListVO> list = bizClueService.selectBizClueList(bizClueRequestVO);
        TableDataInfo<BizClueListVO> dataTable = getDataTable(list);
        dataTable.setListDataInfo(this.getListDataInfo(bizClueRequestVO));
        return dataTable;
    }
    /**
     * 获取列表数据简介方法
     * @param
     * @return
     */
    public String getListDataInfo(BizClueRequestVO bizClueRequestVO){
        //清除分页线程
        clearPage();
        List<BizClueListVO> listAll = bizClueService.selectBizClueList(bizClueRequestVO);
        StringBuilder sb = new StringBuilder();
        sb.append("共有").append("<span>").append(listAll.size()).append("</span>").append("条。");
        AtomicReference<Boolean> flag = new AtomicReference<>(false);
        //状态枚举list
        List<ClueStatusEnum> clueStatusEnumList = ClueStatusEnum.getList();
        clueStatusEnumList.forEach(enumItem ->{
            long count = listAll.stream().filter(item -> Objects.equals(enumItem.getValue(), item.getStatus())).count();
            if((int) count > 0){
                flag.set(true);
                sb.append(enumItem.getName()).append("状态").append("<span>").append(count).append("</span>").append("条").append("，");
            }
        });
        if(flag.get()){
            //清除最后一个字符
            sb.deleteCharAt(sb.length()-1);
            sb.append("。");
        }
        return sb.toString();
    }
    /**
     * 获取线索详细信息
     */
    @ApiOperation("获取线索详细信息")
    @GetMapping(value = "/{clueId}")
    public AjaxResult<BizClueEditVO> getInfo(@PathVariable("clueId") Long clueId)
    {
        return success(bizClueService.selectBizClueByClueId(clueId));
    }
    /**
     * 获取线索转介绍信息
     */
    @ApiOperation("获取线索转介绍信息")
    @GetMapping(value = "/referral/{clueId}")
    public AjaxResult<BizClueReferralVO> getInfoByReferral(@PathVariable("clueId") Long clueId)
    {
        return success(bizClueService.selectClueListForReferral(clueId));
    }


    /**
     * 分配：只有共有池、跟进池的线索，才会被分配
     * 更新为：跟进池
     */
    @PostMapping("/assign")
    @ApiOperation("分配")
    @Log(title = "线索", businessType = BusinessType.ASSIGN)
    public AjaxResult assign(@RequestBody BizCluePostDTO bizCluePostDTO)
    {
        final int i = bizClueService.batchAssign(bizCluePostDTO);
        if(i == 0){
            return AjaxResult.error("数据异常，无可分配的线索（线索必须为共有池、跟进池）！");
        }
        return AjaxResult.success("分配成功！",i);
    }
    /**
     * 批量分配：只有共有池、跟进池的线索，才会被分配
     * 更新为：跟进池
     */
    @PostMapping("/batchAssign")
    @ApiOperation("批量分配")
    @Log(title = "线索", businessType = BusinessType.BATCH_ASSIGN)
    public AjaxResult batchAssign(@RequestBody BizCluePostDTO bizCluePostDTO)
    {
        final int i = bizClueService.batchAssign(bizCluePostDTO);
        if(i == 0){
            return AjaxResult.error("数据异常，无可分配的线索（提交线索必须为共有池、跟进池）！");
        }
        return AjaxResult.success("批量分配成功！",i);
    }

    /**
     * 回收共有池：只有跟进池的线索，才会被回收
     */
    @PostMapping("/recycleCommonPool")
    @ApiOperation("回收共有池")
    @Log(title = "线索", businessType = BusinessType.RECYCLE_COMMON_POOL)
    public AjaxResult recycleCommonPool(@RequestBody BizCluePostDTO bizCluePostDTO)
    {
        final int i = bizClueService.recycleCommonPool(bizCluePostDTO);
        if(i == 0){
            return AjaxResult.error("数据异常，无可分配的线索（提交线索必须为跟进池）！");
        }
        return AjaxResult.success("回收共有池成功！",i);
    }

    /**
     * 批量转移：
     */
    @PostMapping("/batchTransfer")
    @ApiOperation("批量转移")
    @Log(title = "线索", businessType = BusinessType.BATCH_TRANSFER)
    public AjaxResult batchTransfer(@RequestBody BizCluePostDTO bizCluePostDTO)
    {
        if(ComUtil.isEmpty(bizCluePostDTO.getAgentId()) && ComUtil.isEmpty(bizCluePostDTO.getExclusiveShopId())){
            return AjaxResult.error("数据异常，区域或专卖店必须选择一个！");
        }

        final int i = bizClueService.batchTransfer(bizCluePostDTO);
        if(i == 0){
            return AjaxResult.error("数据异常，无可分配的线索（提交线索必须为共有池、跟进池）！");
        }
        return AjaxResult.success("批量分配成功！",i);
    }


    /**
     * 线索状态下拉列表
     */
    @GetMapping("/statusList")
    @ApiOperation("线索状态下拉列表")
    public AjaxResult<List<ClueStatusEnum.ClueStatusDO>> statusList()
    {
        return AjaxResult.success(ClueStatusEnum.getStatusList());
    }

    /**
     * 线索质量标签下拉列表
     */
    @GetMapping("/customerLabelList")
    @ApiOperation("线索质量标签下拉列表")
    public AjaxResult<List<ClueCustomerLabelEnum.ClueCustomerLabelDO>> customerLabelList()
    {
        return AjaxResult.success(ClueCustomerLabelEnum.getCustomerLabelList());
    }
    /**
     * 线索池下拉列表
     */
    @GetMapping("/cluePoolTypeList")
    @ApiOperation("线索池下拉列表")
    public AjaxResult<List<CluePoolTypeEnum.CluePoolTypeDO>> cluePoolTypeList()
    {
        return AjaxResult.success(CluePoolTypeEnum.getCluePoolTypeList());
    }

    /**
     * 下载导入模版
     * @param response
     */
    @ApiOperation("下载批量导入模版")
    @Log(title = "线索", businessType = BusinessType.IMPORT)
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<BizClueExcel> util = new ExcelUtil<BizClueExcel>(BizClueExcel.class);
        util.importTemplateExcel(response,"线索批量录入模版");
    }

    /**
     * 导入
     * @param file 文件
     * @param agentId 区域
     * @param exclusiveShopId 专卖店
     */
    @ApiOperation("线索导入")
    @Log(title = "线索", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(@ApiParam("上传文件") @RequestParam("file") MultipartFile file,
                                 @ApiParam(value = "区域id") @RequestParam(value = "agentId",required = false) Long agentId,
                                 @ApiParam("专卖店id") @RequestParam(value = "exclusiveShopId",required = false) Integer exclusiveShopId) throws Exception {

        return bizClueService.importData(file,agentId,exclusiveShopId);
    }

    /**
     * 导出线索列表
     */
    @ApiOperation("导出线索列表")
    @Log(title = "线索", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizClueRequestVO bizClueRequestVO)
    {
        queryParamUtil.setQueryParam(bizClueRequestVO);
        List<BizClueListVO> list = bizClueService.selectBizClueList(bizClueRequestVO);
        ExcelUtil<BizClueListVO> util = new ExcelUtil<BizClueListVO>(BizClueListVO.class);
        util.exportExcel(response, list, "线索数据");
    }


    /**
     * 新增线索
     */
    @ApiOperation("新增线索")
    @Log(title = "线索", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizClue bizClue)
    {
        return toAjax(bizClueService.insertBizClue(bizClue));
    }

    /**
     * 修改线索
     */
    @ApiOperation("修改线索")
    @Log(title = "线索", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizClue bizClue)
    {
        return toAjax(bizClueService.updateBizClue(bizClue));
    }

    /**
     * 删除线索
     */
    @ApiOperation("删除线索")
    @Log(title = "线索", businessType = BusinessType.DELETE)
	@DeleteMapping("/{clueIds}")
    public AjaxResult remove(@PathVariable Long[] clueIds)
    {
        return toAjax(bizClueService.deleteBizClueByClueIds(clueIds));
    }
}
