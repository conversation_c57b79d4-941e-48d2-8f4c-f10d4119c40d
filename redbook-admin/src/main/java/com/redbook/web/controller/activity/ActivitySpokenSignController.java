package com.redbook.web.controller.activity;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.system.domain.ActivityDrawRecord;
import com.redbook.system.domain.ActivitySpokenSignUser;
import com.redbook.system.domain.dto.ActivitySpokenSignIndexListDto;
import com.redbook.system.domain.dto.ActivityUserDrawRecordListDto;
import com.redbook.system.service.IActivityDcwThirdService;
import com.redbook.system.service.IActivitySpokenSignService;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


@RestController
@RequestMapping("/activity/spokenSign/")
@Api(tags = "口语打卡")
public class ActivitySpokenSignController extends BaseController {

    @Autowired
    QueryParamUtil queryParamUtil;
    @Autowired
    IActivityDcwThirdService activityDcwThirdService;
    @Autowired
    IActivitySpokenSignService activitySpokenSignService;
    @GetMapping("fourth/list")
    @ApiOperation("第四届口语打卡争霸赛列表")
    public TableDataInfo<ActivitySpokenSignUser> fourthList(ActivitySpokenSignIndexListDto activitySpokenSignIndexListDto) {
        queryParamUtil.setQueryParam(activitySpokenSignIndexListDto);
        startPage();
        List<ActivitySpokenSignUser> list = activitySpokenSignService.fourthList(activitySpokenSignIndexListDto);
        return getDataTable(list);
    }


    @ApiOperation("导出第四届口语打卡争霸赛数据")
    @Log(title = "导出第四届口语打卡争霸赛数据", businessType = BusinessType.EXPORT)
    @PostMapping("fourth/export")
    public void fourthExport(HttpServletResponse response, ActivitySpokenSignIndexListDto activitySpokenSignIndexListDto) {
        queryParamUtil.setQueryParam(activitySpokenSignIndexListDto);
        List<ActivitySpokenSignUser> list = activitySpokenSignService.fourthList(activitySpokenSignIndexListDto);
        activitySpokenSignService.fourthExport(list,response,activitySpokenSignIndexListDto);
    }

    @GetMapping("fourth/userDrawRecordList")
    @ApiOperation("第四届口语打卡争霸赛抽奖列表")
    public List<ActivityDrawRecord> fourthUserDrawRecordList(ActivityUserDrawRecordListDto activityUserDrawRecordListDto) {
        return activitySpokenSignService.fourthUserDrawRecordList(activityUserDrawRecordListDto);
    }
}
