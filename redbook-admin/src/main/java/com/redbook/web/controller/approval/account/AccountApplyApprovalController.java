package com.redbook.web.controller.approval.account;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.AccountApplyApproval;
import com.redbook.system.service.IAccountApplyApprovalService;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 账号申请审批Controller
 * 
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/system/approval")
@Api(tags = "账号审批管理")
public class AccountApplyApprovalController extends BaseController
{
    @Autowired
    private IAccountApplyApprovalService accountApplyApprovalService;
    @Autowired
    QueryParamUtil queryParamUtil;
    /**
     * 查询账号申请审批列表
     */
    @GetMapping("/list")
    @ApiOperation("查询账号申请审批列表")
    public TableDataInfo<AccountApplyApproval> list(AccountApplyApproval accountApplyApproval){
        queryParamUtil.setQueryParam(accountApplyApproval);
        startPage();
        List<AccountApplyApproval> list = accountApplyApprovalService.selectAccountApplyApprovalList(accountApplyApproval);
        return getDataTable(list);
    }
    /**
     * 查询商品订单列表汇总信息
     */
    @GetMapping("/list/info")
    @ApiOperation("查询账号申请汇总信息")
    public String info(AccountApplyApproval accountApplyApproval) {
        queryParamUtil.setQueryParam(accountApplyApproval);
        StringBuilder sb = new StringBuilder();
        List<AccountApplyApproval> all = accountApplyApprovalService.selectAccountApplyApprovalList(accountApplyApproval);
        sb.append("共").append(all.size()).append("条记录，");
        sb.append("共").append(all.stream().mapToInt(AccountApplyApproval::getApplyCount).sum()).append("个账号，");
        sb.append("启用").append(all.stream().mapToInt(AccountApplyApproval::getEnableCount).sum()).append("个，");
        sb.append("未启用").append(all.stream().mapToInt(AccountApplyApproval::getApplyCount).sum()-all.stream().mapToInt(AccountApplyApproval::getEnableCount).sum()).append("个。");
        return sb.toString();
    }
    /**
     * 导出账号申请审批列表
     */
    @ApiOperation("导出账号申请审批列表")
    @Log(title = "账号申请审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AccountApplyApproval accountApplyApproval){
        queryParamUtil.setQueryParam(accountApplyApproval);
        List<AccountApplyApproval> list = accountApplyApprovalService.selectAccountApplyApprovalList(accountApplyApproval);
        ExcelUtil<AccountApplyApproval> util = new ExcelUtil<AccountApplyApproval>(AccountApplyApproval.class);
        util.exportExcel(response, list, "账号申请审批数据");
    }

    /**
     * 获取账号申请审批详细信息
     */
    @ApiOperation("获取账号申请审批详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(accountApplyApprovalService.selectAccountApplyApprovalById(id));
    }
  /**
     * 获取账号申请审批详细信息
     */
    @ApiOperation("获取账号申请审批详细信息通过recordNo")
    @GetMapping(value = "recordNo/{recordNo}")
    public AjaxResult getByRecordNo(@PathVariable("recordNo") String recordNo)
    {
        return success(accountApplyApprovalService.selectAccountApplyApprovalByRecordNo(recordNo));
    }

    /**
     * 新增账号申请审批
     */
    @ApiOperation("新增账号申请审批")
    @Log(title = "账号申请审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AccountApplyApproval accountApplyApproval)
    {
        return toAjax(accountApplyApprovalService.insertAccountApplyApproval(accountApplyApproval));
    }

    /**
     * 修改账号申请审批
     */
    @ApiOperation("修改账号申请审批")
    @Log(title = "账号申请审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AccountApplyApproval accountApplyApproval)
    {
        return toAjax(accountApplyApprovalService.updateAccountApplyApproval(accountApplyApproval));
    }

    /**
     * 删除账号申请审批
     */
    @ApiOperation("删除账号申请审批")
    @Log(title = "账号申请审批", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(accountApplyApprovalService.deleteAccountApplyApprovalByIds(ids));
    }
}
