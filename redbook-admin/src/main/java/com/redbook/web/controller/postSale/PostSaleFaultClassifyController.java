package com.redbook.web.controller.postSale;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.system.service.postSale.IPostSaleFaultClassifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.PostSaleFaultClassify;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.core.page.TableDataInfo;

/**
 * 售后故障分类Controller
 * 
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/postSale/admin/faultClassify")
@Api(tags = "售后-故障分类")
public class PostSaleFaultClassifyController extends BaseController
{
    @Autowired
    private IPostSaleFaultClassifyService postSaleFaultClassifyService;

    /**
     * 查询售后故障分类列表
     */
    @GetMapping("/list")
    @ApiOperation("查询售后故障分类列表")
    public TableDataInfo<PostSaleFaultClassify> list(PostSaleFaultClassify postSaleFaultClassify)
    {
        startPage();
        List<PostSaleFaultClassify> list = postSaleFaultClassifyService.selectPostSaleFaultClassifyList(postSaleFaultClassify);
        return getDataTable(list);
    }

    /**
     * 新增售后故障分类
     */
    @ApiOperation("新增售后故障分类")
    @Log(title = "售后故障分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostSaleFaultClassify postSaleFaultClassify)
    {
        return toAjax(postSaleFaultClassifyService.insertPostSaleFaultClassify(postSaleFaultClassify));
    }

    /**
     * 修改售后故障分类
     */
    @ApiOperation("修改售后故障分类")
    @Log(title = "售后故障分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostSaleFaultClassify postSaleFaultClassify)
    {
        return toAjax(postSaleFaultClassifyService.updatePostSaleFaultClassify(postSaleFaultClassify));
    }

    /**
     * 删除售后故障分类
     */
    @ApiOperation("删除售后故障分类")
    @Log(title = "售后故障分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(postSaleFaultClassifyService.deletePostSaleFaultClassifyByIds(ids));
    }
}
