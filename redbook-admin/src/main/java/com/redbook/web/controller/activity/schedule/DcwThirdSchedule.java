package com.redbook.web.controller.activity.schedule;

import com.redbook.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component("dcwThirdSchedule")
public class DcwThirdSchedule {
    private static final Logger log = LoggerFactory.getLogger(DcwThirdSchedule.class);
    private static final int activityBaseId = 12;
    private static final int activityContentId = 15;
    @Autowired
    Environment env;

    private String getHost() {
        String host = "https://api.xiaohongben888.com/";
        String profile = env.getActiveProfiles()[0];
        if (profile.equals("dev") || profile.equals("test")) {
            host = "https://test.xiaohongben888.com/";
        }
        return host;
    }

    public void gameStage1() {
        String url = getHost() + "redbook/activity/dcw/third/schedule/handleGameStageReward/"+activityBaseId+"/"+activityContentId+"/1/0";
        log.info("定时器=" + url);
        HttpUtils.sendGet(url);
    }

    public void gameStage2() {
        String url = getHost() + "redbook/activity/dcw/third/schedule/handleGameStageReward/"+activityBaseId+"/"+activityContentId+"/2/0";
        log.info("定时器=" + url);
        HttpUtils.sendGet(url);
    }

    public void gameStage3() {
        String url = getHost() + "redbook/activity/dcw/third/schedule/handleGameStageReward/"+activityBaseId+"/"+activityContentId+"/3/0";
        log.info("定时器=" + url);
        HttpUtils.sendGet(url);
    }

    public void gameStage4Model100() {
        String url = getHost() + "redbook/activity/dcw/third/schedule/handleGameStageReward/"+activityBaseId+"/"+activityContentId+"/4/100";
        log.info("定时器=" + url);
        HttpUtils.sendGet(url);
    }

    public void gameStage4Model32() {
        String url = getHost() + "redbook/activity/dcw/third/schedule/handleGameStageReward/"+activityBaseId+"/"+activityContentId+"/4/32";
        log.info("定时器=" + url);
        HttpUtils.sendGet(url);
    }

    public void gameStage4Model16() {
        String url = getHost() + "redbook/activity/dcw/third/schedule/handleGameStageReward/"+activityBaseId+"/"+activityContentId+"/4/16";
        log.info("定时器=" + url);
        HttpUtils.sendGet(url);
    }

    public void gameStage4Model8() {
        String url = getHost() + "redbook/activity/dcw/third/schedule/handleGameStageReward/"+activityBaseId+"/"+activityContentId+"/4/8";
        log.info("定时器=" + url);
        HttpUtils.sendGet(url);
    }
}
