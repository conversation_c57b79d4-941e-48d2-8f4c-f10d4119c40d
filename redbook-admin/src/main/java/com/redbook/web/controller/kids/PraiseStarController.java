package com.redbook.web.controller.kids;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.kids.PraiseStar;
import com.redbook.system.service.kids.IPraiseStarService;
import com.redbook.common.utils.poi.ExcelUtil;

/**
 * 夸夸星语Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/kids/star")
@Api(tags = "夸夸星语")
public class PraiseStarController extends BaseController
{
    @Autowired
    private IPraiseStarService praiseStarService;

    /**
     * 查询夸夸星语列表
     */
    @GetMapping("/list")
    @ApiOperation("查询夸夸星语列表")
    public TableDataInfo<PraiseStar> list(PraiseStar praiseStar)
    {
        startPage();
        List<PraiseStar> list = praiseStarService.selectPraiseStarList(praiseStar);
        return getDataTable(list);
    }

    /**
     * 导出夸夸星语列表
     */
    @ApiOperation("导出夸夸星语列表")
    @Log(title = "夸夸星语", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PraiseStar praiseStar)
    {
        List<PraiseStar> list = praiseStarService.selectPraiseStarList(praiseStar);
        ExcelUtil<PraiseStar> util = new ExcelUtil<PraiseStar>(PraiseStar.class);
        util.exportExcel(response, list, "夸夸星语数据");
    }

    /**
     * 获取夸夸星语详细信息
     */
    @ApiOperation("获取夸夸星语详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(praiseStarService.selectPraiseStarById(id));
    }

    /**
     * 新增夸夸星语
     */
    @ApiOperation("新增夸夸星语")
    @Log(title = "夸夸星语", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PraiseStar praiseStar)
    {
        return toAjax(praiseStarService.insertPraiseStar(praiseStar));
    }

    /**
     * 修改夸夸星语
     */
    @ApiOperation("修改夸夸星语")
    @Log(title = "夸夸星语", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PraiseStar praiseStar)
    {
        return toAjax(praiseStarService.updatePraiseStar(praiseStar));
    }

    /**
     * 删除夸夸星语
     */
    @ApiOperation("删除夸夸星语")
    @Log(title = "夸夸星语", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(praiseStarService.deletePraiseStarByIds(ids));
    }
}
