package com.redbook.web.controller.agent;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageInfo;
import com.redbook.common.annotation.Log;
import com.redbook.common.constant.CacheConstants;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.domain.entity.AgentInfo;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.core.redis.RedisCache;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.exception.ServiceException;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.StringUtils;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.common.utils.sign.Md5Utils;
import com.redbook.system.domain.*;
import com.redbook.system.domain.dto.AgentChargeDto;
import com.redbook.system.domain.dto.StatisticsInvoiceInfoDto;
import com.redbook.system.domain.dto.TransactionStatisticsDto;
import com.redbook.system.domain.vo.*;
import com.redbook.system.service.*;
import com.redbook.system.util.GsonManager;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理商钱包账户Controller
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@RestController
@RequestMapping("/agent/account")
@Api(tags = "代理商钱包账户")
public class AgentAccountController extends BaseController {
    @Autowired
    private IAgentAccountService agentAccountService;
    @Autowired
    private IAgentTransactionInfoService agentTransactionInfoService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    QueryParamUtil queryParamUtil;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IAgentRechargeOrderService agentRechargeOrderService;
    @Autowired
    private IApprovalService approvalService;


    /**
     * 查询代理商交易记录列表
     */
    @GetMapping("/tran/list")
    @ApiOperation("查询代理商交易记录列表")
    public TableDataInfo<AgentTransactionInfo> list(AgentTransactionInfo agentTransactionInfo) {
        if (getLoginUser().getUser().getLimitAgent()) agentTransactionInfo.setmId(getUserId());
        startPage();
        List<AgentTransactionInfo> list = agentTransactionInfoService.selectAgentTransactionInfoList(agentTransactionInfo);
        return getDataTable(list);
    }

    /**
     * 查询代理商交易记录列表
     */
    @GetMapping("/tran/list/info")
    @ApiOperation("查询代理商交易记录列表汇总信息")
    public String info(AgentTransactionInfo agentTransactionInfo) {
        if (getLoginUser().getUser().getLimitAgent()) agentTransactionInfo.setmId(getUserId());
        StringBuilder sb = new StringBuilder();
        List<AgentTransactionInfo> all = agentTransactionInfoService.selectAgentTransactionInfoList(agentTransactionInfo);
        sb.append("共有<span>").append(all.size()).append("</span>条记录。");
        sb.append("总收入：<span>").append(all.stream().filter(agentTransactionInfo1 -> agentTransactionInfo1.getPaymentType() == 0).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>元，");
        sb.append("总支出：<span>").append(all.stream().filter(agentTransactionInfo1 -> agentTransactionInfo1.getPaymentType() == 1).map(AgentTransactionInfo::getMoney).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>元。");
        return sb.toString();
    }


    /**
     * 查询代理商钱包账户列表
     */
    @GetMapping("/list")
    @ApiOperation("查询代理商钱包账户列表")
    public TableDataInfo<AgentAccount> list(AgentAccount agentAccount) {
        queryParamUtil.setQueryParam(agentAccount);
        startPage();
        return getDataTable(agentAccountService.selectAgentAccountList(agentAccount));
    }

    /**
     * 查询代理商钱包账户列表
     */
    @GetMapping("/list/info")
    @ApiOperation("查询代理商钱包账户列表汇总信息")
    public String info(AgentAccount agentAccount) {
        queryParamUtil.setQueryParam(agentAccount);
        StringBuilder sb = new StringBuilder();
        List<AgentAccount> agentAccounts = agentAccountService.selectAgentAccountList(agentAccount);
        sb.append("共有").append("<span>").append(agentAccounts.size()).append("</span>").append("个区域。");
        sb.append("余额汇总：").append("<span>").append(agentAccounts.stream().map(AgentAccount::getTotalMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>").append("元").append("（");
        BigDecimal money = agentAccounts.stream().map(AgentAccount::getDepositMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(money.doubleValue()>0){
            sb.append("保证金：").append("<span>").append(money).append("</span>").append("元").append("，");
        }
        money = agentAccounts.stream().map(AgentAccount::getRenewMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(money.doubleValue()>0) {
            sb.append("续约款：").append("<span>").append(money).append("</span>").append("元").append("，");
        }
        sb.append("硬件款").append("<span>").append(agentAccounts.stream().map(AgentAccount::getHardwareMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>").append("元").append("，");
        sb.append("期末会员款").append("<span>").append(agentAccounts.stream().map(AgentAccount::getMemberMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>").append("元").append("，");
        sb.append("期末代金券").append("<span>").append(agentAccounts.stream().map(AgentAccount::getVoucherMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>").append("元").append("，");
        sb.append("期初会员款").append("<span>").append(agentAccounts.stream().map(AgentAccount::getNewMemberMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>").append("元").append("，");
        sb.append("期初代金券").append("<span>").append(agentAccounts.stream().map(AgentAccount::getNewVoucherMoney).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>").append("元").append("）");
        return sb.toString();
    }

    /**
     * 获取代理商钱包账户详细信息
     */
    @ApiOperation("获取代理商钱包账户详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult<AgentAccountSummaryVo> getInfo(@PathVariable("id") Long id) {
        return success(agentAccountService.selectAgentAccountVoByAgentId(id));
    }

    @ApiOperation("统计可开发票信息")
    @GetMapping("/statisticsInvoiceInfo")
    public AjaxResult<StatisticsInvoiceInfoVo> statisticsInvoiceInfo(StatisticsInvoiceInfoDto dto) {
        if(dto.getAgentId()==null){
            return error("区域ID不能为空！");
        }
        return success(agentTransactionInfoService.statisticsInvoiceInfo(dto));
    }
    @ApiOperation("导出-统计可开发票信息")
    @PostMapping("/exportStatisticsInvoiceInfo")
    public void exportStatisticsInvoiceInfo(StatisticsInvoiceInfoDto dto, HttpServletResponse response) throws Exception {
        if(dto.getAgentId()==null){
            throw new Exception("区域ID不能为空！");
        }
        AgentParamVo params = sysUserService.getParams(null);
        Map<Long, String> collect = params.getAgentInfoList().stream().collect(Collectors.toMap(AgentInfo::getId, AgentInfo::getName));
        String agentName = collect.get(dto.getAgentId());
        String title = agentName + " " + DateUtils.format(dto.getStartDate(), DateUtils.YYYY_MM_DD) +" 至 " +DateUtils.format(dto.getEndDate(), DateUtils.YYYY_MM_DD);

        Approval approval = approvalService.selectApprovalById(dto.getApprovalId());
        String approvalContent = approval.getApprovalContent();
        Map map = GsonManager.fromJson(approvalContent, Map.class);
        String secondRow = (String) map.get("invoiceInfo");

        String type = (String) map.get("type");
        if("prodOrder".equals(type)){
            Object prodOrderIdList = map.get("prodOrderIdList");
            if(prodOrderIdList != null){
                List<String> list = (List<String>) prodOrderIdList;
                if(CollectionUtil.isNotEmpty(list)){
                    //硬件订单
                    List<InvoiceDetailExport> invoiceDetailExportList = agentTransactionInfoService.statisticsInvoiceByOrder(list);
                    ExcelUtil<InvoiceDetailExport> util = new ExcelUtil<InvoiceDetailExport>(InvoiceDetailExport.class);
                    util.exportExcel(response, invoiceDetailExportList, "统计可开发票信息",title,secondRow);
                }
            }
        }else {
            //充值记录
            AgentRechargeOrder agentRechargeOrder = new AgentRechargeOrder();
            agentRechargeOrder.setAgentId(dto.getAgentId());
            Object rechargeIdList = map.get("rechargeIdList");
            if(rechargeIdList != null){
                List<Long> list = (List<Long>) rechargeIdList;
                if(CollectionUtil.isNotEmpty(list)){
                    agentRechargeOrder.setRechargeIdList(list);
                    List<AgentRechargeOrder> agentRechargeOrders = agentRechargeOrderService.selectAgentRechargeOrderList(agentRechargeOrder);
                    ExcelUtil<AgentRechargeOrder> util = new ExcelUtil<AgentRechargeOrder>(AgentRechargeOrder.class);
                    util.exportExcel(response, agentRechargeOrders, "统计可开发票信息",title,secondRow);
                }
            }
        }
    }


    @ApiOperation("代理商交易统计")
    @GetMapping("/transactionStatistics")
    public TableDataInfo<TransactionStatisticsVo> transactionStatistics(TransactionStatisticsDto dto) {
        if(dto.getTransactionStartDate()==null||dto.getTransactionEndDate()==null){
            throw ServiceException.fail("消费日期不能为空！");
        }
        queryParamUtil.setQueryParam(dto);
        startPage();
        PageInfo<TransactionStatisticsVo> list = agentTransactionInfoService.transactionStatistics(dto);
        TableDataInfo<TransactionStatisticsVo> tableDataInfo = new TableDataInfo<>();
        tableDataInfo.setRows(list.getList());
        tableDataInfo.setTotal(list.getTotal());
        return tableDataInfo;
    }
    @ApiOperation("代理商交易统计-汇总信息")
    @GetMapping("/transactionStatistics/info")
    public String info(TransactionStatisticsDto dto) {
        if(dto.getTransactionStartDate()==null||dto.getTransactionEndDate()==null){
            throw ServiceException.fail("消费日期不能为空！");
        }
        queryParamUtil.setQueryParam(dto);
        StringBuilder sb = new StringBuilder();
        PageInfo<TransactionStatisticsVo> all = agentTransactionInfoService.transactionStatistics(dto);
        sb.append("共有<span>").append(all.getTotal()).append("</span>个区域。");
        sb.append("总收入：<span>").append(all.getList().stream().map(TransactionStatisticsVo::getTotalMoneyIn).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>元，");
        sb.append("总支出：<span class='expenditure'>").append(all.getList().stream().map(TransactionStatisticsVo::getTotalMoneyOut).reduce(BigDecimal.ZERO, BigDecimal::add)).append("</span>元。");
        return sb.toString();
    }

    /**
     * 手动充值
     */
    @ApiOperation("手动充值")
    @Log(title = "代理商钱包账户", businessType = BusinessType.UPDATE)
    @PutMapping("/recharge")
    public AjaxResult recharge(@RequestBody AgentChargeDto chargeDto) {
        chargeDto.setTransactionType(chargeDto.getPaymentType() == 0 ? "人工充值" : "人工扣费");
        return success(agentAccountService.handle(chargeDto) != null);
    }

    /**
     * 修改代理商钱包账户
     */
    @ApiOperation("修改支付密码")
    @Log(title = "代理商钱包账户", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult edit(String code, String payPassword) {
        //只有代理商账号允许修改支付密码
        if(getLoginUser().getUser().getRole().getRoleId().intValue()!=100) {
            return error("您没有权限修改");
        }
        String verifyKey = CacheConstants.CAPTCHA_SMS_CODE_KEY + getLoginUser().getUser().getPhonenumber();
        String captcha = redisCache.getCacheObject(verifyKey);
        if (StringUtils.isEmpty(captcha) || !captcha.equals(code)) {
            throw ServiceException.fail("验证码错误");
        }
        return success(agentAccountService.changePayPassword(Md5Utils.hash(payPassword)));
    }

    @ApiOperation("开启动态价格优惠/余额转移")
    @Log(title = "代理商钱包账户", businessType = BusinessType.UPDATE)
    @PutMapping("/openRebate")
    public AjaxResult openRebate(Long agentId) {
        //只有代理商账号允许开启动态价格优惠
        if(getLoginUser().getUser().getRole().getRoleId().intValue()!=100) {
            return error("您没有权限修改");
        }
        AgentAccount agentAccount = agentAccountService.selectAgentAccountById(agentId);
        if(agentAccount==null){
            throw ServiceException.fail("该代理商不存在");
        }
        if(agentAccount.getRebateStatus()){
            throw ServiceException.fail("该代理商已开启动态价格优惠");
        }
        return success(agentAccountService.openRebate(agentId));
    }
}
