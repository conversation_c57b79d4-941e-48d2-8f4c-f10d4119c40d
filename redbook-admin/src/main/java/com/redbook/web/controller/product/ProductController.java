package com.redbook.web.controller.product;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.Product;
import com.redbook.system.domain.ProductCategory;
import com.redbook.system.domain.ProductSizeInventory;
import com.redbook.system.domain.dto.AddProductSizeInventoryDto;
import com.redbook.system.service.IProductCategoryService;
import com.redbook.system.service.IProductService;
import com.redbook.system.service.IProductSizeInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商品Controller
 *
 * <AUTHOR>
 * @date 2023-05-09
 */
@RestController
@RequestMapping("/system/product")
@Api(tags = "商品")
public class ProductController extends BaseController {
    @Autowired
    private IProductService productService;
    @Autowired
    private IProductSizeInventoryService productSizeInventoryService;

    @Autowired
    private IProductCategoryService productCategoryService;

    /**
     * 查询商品类别列表
     */
    @GetMapping("category/list")
    @ApiOperation("查询商品类别列表")
    public TableDataInfo<ProductCategory> list(ProductCategory productCategory) {
        startPage();
        List<ProductCategory> list = productCategoryService.selectProductCategoryList(productCategory);
        return getDataTable(list);
    }

    /**
     * 查询商品列表
     */
    @GetMapping("/list")
    @ApiOperation("查询商品列表")
    public TableDataInfo<Product> list(Product product) {
        List<Product> list = productService.selectProductList(product);
        product.setStatus(-1);
        if (product.isNeedStatus()){
            List<Product> all = productService.selectProductList(product);
            if (all.size() == 0) {
                return getDataTable(list);
            }
            String result = "共有" + all.size() + "个商品，" + all.stream().filter(product1 -> product1.getStatus() == 0).count() + "个已下架，" + all.stream().filter(product1 -> product1.getStatus() == -1).count() + "个已删除。";
            return getDataTable(list,result);
        }
        return getDataTable(list);
    }

    /**
     * 导出商品列表
     */
    @ApiOperation("导出商品列表")
    @Log(title = "商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Product product) {
        List<Product> list = productService.selectProductList(product);
        ExcelUtil<Product> util = new ExcelUtil<Product>(Product.class);
        util.exportExcel(response, list, "商品数据");
    }

    /**
     * 获取商品详细信息
     */
    @ApiOperation("获取商品详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(productService.selectProductById(id));
    }

    /**
     * 新增商品
     */
    @ApiOperation("新增商品")
    @Log(title = "商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Product product) {
        return toAjax(productService.insertProduct(product));
    }

    /**
     * 修改商品
     */
    @ApiOperation("修改商品")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Product product) {
        return toAjax(productService.updateProduct(product));
    }

    /**
     * 删除商品
     */
    @ApiOperation("修改商品状态")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @PutMapping("changeStatus/{ids}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "需要修改状态的商品 ids", required = true, dataType = "Integer[]", paramType = "path", dataTypeClass = Integer[].class),
            @ApiImplicitParam(name = "status", value = "状态 0 下架  1 上架 - 1 删除", required = true, dataType = "Integer", paramType = "query", dataTypeClass = Integer.class)
    })
    public AjaxResult changeStatus(@PathVariable Integer[] ids, Integer status) {
        return toAjax(productService.changeStatus(ids, status));
    }

    @ApiOperation("修改商品展示系统")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @PutMapping("changeShowSystem/{ids}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "需要修改状态的商品 ids", required = true, dataType = "Integer[]", paramType = "path", dataTypeClass = Integer[].class),
            @ApiImplicitParam(name = "showSystem", value = "展示系统：0：运管+小程序 1：仅运管 2：仅小程序", required = true, dataType = "Integer", paramType = "query", dataTypeClass = Integer.class)
    })
    public AjaxResult changeShowSystem(@PathVariable Integer[] ids, Integer showSystem) {
        return toAjax(productService.changeShowSystem(ids, showSystem));
    }

    /**
     * 修改商品尺码库存
     */
    @ApiOperation("增加商品库存")
    @Log(title = "增加商品库存", businessType = BusinessType.UPDATE)
    @PostMapping("addProductSizeInventorys")
    public AjaxResult addProductSizeInventorys(@RequestBody List<AddProductSizeInventoryDto> productSizeInventorys) {
        return toAjax(productSizeInventoryService.addProductSizeInventorys(productSizeInventorys));
    }

    @ApiOperation("增加商品库存（虚拟商品入库）")
    @Log(title = "增加商品库存（虚拟商品入库）", businessType = BusinessType.UPDATE)
    @PostMapping("addVirtualGoodsProductSizeInventorys")
    public AjaxResult addVirtualGoodsProductSizeInventorys(AddProductSizeInventoryDto addProductSizeInventoryDto) throws Exception {
        return success(productSizeInventoryService.addVirtualGoodsProductSizeInventorys(addProductSizeInventoryDto));
    }
}
