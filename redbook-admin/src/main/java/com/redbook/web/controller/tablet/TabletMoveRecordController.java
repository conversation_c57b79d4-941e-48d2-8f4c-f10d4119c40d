package com.redbook.web.controller.tablet;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.TabletMoveRecord;
import com.redbook.system.service.ITabletMoveRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 硬件转移记录Controller
 * 
 * <AUTHOR>
 * @date 2022-11-17
 */
@RestController
@RequestMapping("/system/record")
@Api(tags = "硬件转移记录")
public class TabletMoveRecordController extends BaseController
{
    @Autowired
    private ITabletMoveRecordService tabletMoveRecordService;

    /**
     * 查询硬件转移记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询硬件转移记录列表")
    public TableDataInfo<TabletMoveRecord> list(TabletMoveRecord tabletMoveRecord)
    {
        startPage();
        List<TabletMoveRecord> list = tabletMoveRecordService.selectTabletMoveRecordList(tabletMoveRecord);
        return getDataTable(list);
    }

    /**
     * 导出硬件转移记录列表
     */
    @ApiOperation("导出硬件转移记录列表")
    @Log(title = "硬件转移记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TabletMoveRecord tabletMoveRecord)
    {
        List<TabletMoveRecord> list = tabletMoveRecordService.selectTabletMoveRecordList(tabletMoveRecord);
        ExcelUtil<TabletMoveRecord> util = new ExcelUtil<TabletMoveRecord>(TabletMoveRecord.class);
        util.exportExcel(response, list, "硬件转移记录数据");
    }

    /**
     * 获取硬件转移记录详细信息
     */
    @ApiOperation("获取硬件转移记录详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tabletMoveRecordService.selectTabletMoveRecordById(id));
    }

    /**
     * 新增硬件转移记录
     */
    @ApiOperation("新增硬件转移记录")
    @Log(title = "硬件转移记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TabletMoveRecord tabletMoveRecord)
    {
        return toAjax(tabletMoveRecordService.insertTabletMoveRecord(tabletMoveRecord));
    }

    /**
     * 修改硬件转移记录
     */
    @ApiOperation("修改硬件转移记录")
    @Log(title = "硬件转移记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TabletMoveRecord tabletMoveRecord)
    {
        return toAjax(tabletMoveRecordService.updateTabletMoveRecord(tabletMoveRecord));
    }

    /**
     * 删除硬件转移记录
     */
    @ApiOperation("删除硬件转移记录")
    @Log(title = "硬件转移记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tabletMoveRecordService.deleteTabletMoveRecordByIds(ids));
    }
}
