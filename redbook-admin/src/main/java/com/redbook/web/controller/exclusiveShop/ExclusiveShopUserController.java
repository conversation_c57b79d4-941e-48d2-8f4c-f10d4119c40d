package com.redbook.web.controller.exclusiveShop;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.redbook.system.domain.ExclusiveShopUser;
import com.redbook.system.service.IExclusiveShopUserService;
import com.redbook.common.utils.poi.ExcelUtil;

/**
 * 专卖店人员管理Controller
 * 
 * <AUTHOR>
 * @date 2024-11-05
 */
@RestController
@RequestMapping("/system/shop/user")
@Api(tags = "专卖店人员管理")
public class ExclusiveShopUserController extends BaseController
{
    @Autowired
    private IExclusiveShopUserService exclusiveShopUserService;

    /**
     * 查询专卖店人员管理列表
     */
    @GetMapping("/list")
    @ApiOperation("查询专卖店人员管理列表")
    public TableDataInfo<ExclusiveShopUser> list(ExclusiveShopUser exclusiveShopUser)
    {
        startPage();
        List<ExclusiveShopUser> list = exclusiveShopUserService.selectExclusiveShopUserList(exclusiveShopUser);
        return getDataTable(list);
    }

    /**
     * 导出专卖店人员管理列表
     */
    @ApiOperation("导出专卖店人员管理列表")
    @Log(title = "专卖店人员管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExclusiveShopUser exclusiveShopUser)
    {
        List<ExclusiveShopUser> list = exclusiveShopUserService.selectExclusiveShopUserList(exclusiveShopUser);
        ExcelUtil<ExclusiveShopUser> util = new ExcelUtil<ExclusiveShopUser>(ExclusiveShopUser.class);
        util.exportExcel(response, list, "专卖店人员管理数据");
    }

    /**
     * 获取专卖店人员管理详细信息
     */
    @ApiOperation("获取专卖店人员管理详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult<ExclusiveShopUser> getInfo(@PathVariable("id") Long id)
    {
        return success(exclusiveShopUserService.selectExclusiveShopUserById(id));
    }

    /**
     * 新增专卖店人员管理
     */
    @ApiOperation("新增专卖店人员管理")
    @Log(title = "专卖店人员管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExclusiveShopUser exclusiveShopUser)
    {
        return toAjax(exclusiveShopUserService.insertExclusiveShopUser(exclusiveShopUser));
    }

    /**
     * 修改专卖店人员管理
     */
    @ApiOperation("修改专卖店人员管理")
    @Log(title = "专卖店人员管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExclusiveShopUser exclusiveShopUser)
    {
        return toAjax(exclusiveShopUserService.updateExclusiveShopUser(exclusiveShopUser));
    }

    /**
     * 删除专卖店人员管理
     */
    @ApiOperation("删除专卖店人员管理")
    @Log(title = "专卖店人员管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(exclusiveShopUserService.deleteExclusiveShopUserByIds(ids));
    }
}
