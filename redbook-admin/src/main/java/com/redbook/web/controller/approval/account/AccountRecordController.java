package com.redbook.web.controller.approval.account;

import com.redbook.common.annotation.Log;
import com.redbook.common.core.controller.BaseController;
import com.redbook.common.core.domain.AjaxResult;
import com.redbook.common.core.page.TableDataInfo;
import com.redbook.common.enums.BusinessType;
import com.redbook.common.utils.DateUtils;
import com.redbook.common.utils.poi.ExcelUtil;
import com.redbook.system.domain.AccountRecord;
import com.redbook.system.domain.TrialAccountApplications;
import com.redbook.system.domain.dto.AccountChangeTeacherAndClassDto;
import com.redbook.system.service.IAccountRecordService;
import com.redbook.system.service.ITrialAccountApplicationsService;
import com.redbook.system.util.QueryParamUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 账号记录Controller
 * 
 * <AUTHOR>
 * @date 2023-06-13
 */
@RestController
@RequestMapping("/system/account/record")
@Api(tags = "账号记录管理")
public class AccountRecordController extends BaseController
{
    @Autowired
    private IAccountRecordService accountRecordService;
    @Autowired
    QueryParamUtil queryParamUtil;
    @Autowired
    ITrialAccountApplicationsService trialAccountApplicationsService;

    /**
     * 查询账号记录列表
     */
    @GetMapping("/list")
    @ApiOperation("查询账号记录列表")
    public TableDataInfo<AccountRecord> list(AccountRecord accountRecord)
    {
        queryParamUtil.setQueryParamGetAidList(accountRecord);
        startPage();
        List<AccountRecord> list = accountRecordService.selectAccountRecordList(accountRecord);
        return getDataTable(list);
    }
    @GetMapping("/list/info")
    @ApiOperation("查询账号记录列表")
    @ResponseBody
    public String info(AccountRecord accountRecord)
    {
        queryParamUtil.setQueryParamGetAidList(accountRecord);
        return accountRecordService.countAccountRecordList(accountRecord);
    }


//    /**
//     * 导出账号记录列表
//     */
//    @ApiOperation("导出账号记录列表")
//    @Log(title = "账号记录", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, AccountRecord accountRecord)
//    {
//        List<AccountRecord> list = accountRecordService.selectAccountRecordList(accountRecord);
//        ExcelUtil<AccountRecord> util = new ExcelUtil<AccountRecord>(AccountRecord.class);
//        util.exportExcel(response, list, "账号记录数据");
//    }
//
//    /**
//     * 获取账号记录详细信息
//     */
//    @ApiOperation("获取账号记录详细信息")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(accountRecordService.selectAccountRecordById(id));
//    }

    /**
     * 新增账号记录
     */
    @ApiOperation("新增账号记录")
    @Log(title = "账号记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AccountRecord accountRecord)
    {
        return toAjax(accountRecordService.insertAccountRecord(accountRecord));
    }

    /**
     * 修改账号记录
     */
    @ApiOperation("修改账号记录")
    @Log(title = "账号记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AccountRecord accountRecord)
    {
        return toAjax(accountRecordService.updateAccountRecord(accountRecord));
    }

    /**
     * 删除账号记录
     */
    @ApiOperation("删除账号记录")
    @Log(title = "账号记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(accountRecordService.deleteAccountRecordByIds(ids));
    }

    @ApiOperation("修改账号代理商、老师、班级")
    @PostMapping("/updateAccountTeacherAndClass")
    @ResponseBody
    public AjaxResult updateAccountTeacherAndClass(@RequestBody AccountChangeTeacherAndClassDto dto) {
        return AjaxResult.success(accountRecordService.updateAccountTeacherAndClass(dto));
    }
    //查询可申请的体验账号数量 按代理商查看
    @ApiOperation("查询可申请的体验账号数量 按代理商查看")
    @GetMapping("/getExperienceAccountNum")
    @ResponseBody
    public AjaxResult getExperienceAccountNum(Long agentId,String accountPrefix) {
        return AjaxResult.success(accountRecordService.getExperienceAccountNum(agentId,accountPrefix));
    }

    //申请体验账号 根据数量申请
    @ApiOperation("申请体验账号")
    @PostMapping("/applyExperienceAccount")
    @ResponseBody
    @Log(title = "申请体验账号", businessType = BusinessType.INSERT)
    public AjaxResult applyExperienceAccount(@RequestParam("agentId") Long agentId,@RequestParam("num") Integer num,@RequestParam("remark") String remark,
                                             @RequestParam("accountPrefix") String accountPrefix,
                                             @RequestParam("extra") String extra,@RequestParam("system") String system) {
        return AjaxResult.success(accountRecordService.applyExperienceAccount(agentId,num,remark,accountPrefix,extra,system));
    }

    @ApiOperation("设置账号学段")
    @PostMapping("/setAccountStage")
    @ResponseBody
    public AjaxResult setAccountStage(@RequestParam String[] accountNos,@RequestParam Integer stage) {
        return AjaxResult.success(accountRecordService.setAccountStage(accountNos,stage));
    }

    //申请体验账号(专卖店）
    @ApiOperation("申请体验账号(专卖店）")
    @PostMapping("/applyExperienceAccountForShop")
    @ResponseBody
    @Log(title = "申请体验账号", businessType = BusinessType.INSERT)
    public AjaxResult applyExperienceAccountForShop(@RequestParam("shopId") Long shopId,@RequestParam("num") Integer num,String remark) {
        return AjaxResult.success(accountRecordService.applyExperienceAccountForShop(shopId,num,remark));
    }

    //查询当前专卖店可申请的体验账号数量
    @ApiOperation("查询当前专卖店可申请的体验账号数量")
    @GetMapping("/getExperienceAccountNumForShop")
    @ResponseBody
    public AjaxResult getExperienceAccountNumForShop(Long shopId) {
        return AjaxResult.success(accountRecordService.getExperienceAccountNumForShop(shopId));
    }

    @ApiOperation("专卖店申请体验账号列表")
    @GetMapping("/getShopApplyList")
    @ResponseBody
    public TableDataInfo<TrialAccountApplications> getShopApplyList(TrialAccountApplications trialAccountApplications) {
        queryParamUtil.setQueryParamGetAidList(trialAccountApplications);
        startPage();
        List<TrialAccountApplications> list = trialAccountApplicationsService.selectTrialAccountApplicationsList(trialAccountApplications);
        return getDataTable(list);
    }
    /**
     * 修改体验账号申请（专卖店）
     */
    @ApiOperation("修改体验账号申请（专卖店）")
    @Log(title = "体验账号申请（专卖店）", businessType = BusinessType.UPDATE)
    @PutMapping("shopApplyEdit")
    public AjaxResult edit(@RequestBody TrialAccountApplications trialAccountApplications)
    {
        return toAjax(trialAccountApplicationsService.updateTrialAccountApplications(trialAccountApplications));
    }




}
