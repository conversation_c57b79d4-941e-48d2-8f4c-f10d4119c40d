<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>redbook</artifactId>
        <groupId>com.redbook</groupId>
        <version>3.8.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>redbook-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- spring-boot-devtools -->
       <!-- <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> &lt;!&ndash; 表示依赖不会传递 &ndash;&gt;
        </dependency>-->


        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.redbook</groupId>
            <artifactId>redbook-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.redbook</groupId>
            <artifactId>redbook-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.redbook</groupId>
            <artifactId>redbook-generator</artifactId>
        </dependency>
        <!--顺丰快递-->
        <dependency>
            <groupId>ShunFeng</groupId>
            <artifactId>ShunFeng</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/SF-CSIM-EXPRESS-SDK-V2.1.7.jar</systemPath>
        </dependency>

        <!-- postsale-api-->
        <dependency>
            <groupId>com.redbook</groupId>
            <artifactId>redbook-postsale-api</artifactId>
            <version>${redbook.version}</version>
        </dependency>

        <!-- dashboard-->
        <dependency>
            <groupId>com.redbook</groupId>
            <artifactId>redbook-dashboard</artifactId>
            <version>${redbook.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>

        <finalName>${project.artifactId}</finalName>
    </build>


</project>